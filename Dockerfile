FROM healadmin/openjdk:17.0.12

RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get -y install ca-certificates make openssl bash && \
    apt-get clean

ENV PATH "$PATH:/usr/local/ssl/bin"

ADD http://192.168.13.69:8081/nexus/repository/third-party/consul-template /usr/local/bin/consul-template

ADD ./conf /etc/consul-template/conf
ADD ./templates /etc/consul-template/templates

COPY ./heal-controlcenter /opt/heal-controlcenter/
COPY ./entrypoint.sh /opt/heal-controlcenter/entrypoint.sh

RUN chmod +x /opt/heal-controlcenter/entrypoint.sh /usr/local/bin/consul-template && \
    mkdir -p /tmp/logs

ENTRYPOINT ["/opt/heal-controlcenter/entrypoint.sh"]