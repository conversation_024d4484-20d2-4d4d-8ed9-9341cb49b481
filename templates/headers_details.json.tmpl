 {
  "Access-Control-Allow-Methods": "{{ key "service/healcontrolcenter/access/control/allow/methods" }}",
  "Access-Control-Allow-Credentials": "true",
  "X-Content-Type-Options": "application/json",
  "Access-Control-Allow-Headers": "{{ key "service/healcontrolcenter/access/control/allow/headers" }}",
  "Access-Control-Allow-Origin": "{{ key "service/healcontrolcenter/access/control/allow/origins" }}",
  "X-Frame-Options": "SAMEORIGIN",
  "Cache-Control": "no-cache, no-store, must-revalidate",
  "Expires": "0",
  "Pragma": "no-cache",
  "Strict-Transport-Security": "max-age=31536000",
  "Content-Security-Policy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';",
  "Server": "{{ key "service/healcontrolcenter/server/name" }}"
}