package com.heal.controlcenter.swagger;

import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Common API responses annotation for standardizing error responses across controllers.
 * This annotation provides standard 400, 401, and 500 error responses using the
 * proper error response schemas that match the actual ExceptionHandler output.
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@ApiResponses(value = {
        @ApiResponse(
                responseCode = "400",
                description = "Bad Request - Invalid input parameters, malformed request data, or validation failures",
                content = @Content(
                        mediaType = "application/json",
                        schema = @Schema(implementation = ErrorResponseSchemas.ErrorResponse.class)
                )
        ),
        @ApiResponse(
                responseCode = "401",
                description = "Unauthorized - Invalid, missing, or expired authentication token",
                content = @Content(
                        mediaType = "application/json",
                        schema = @Schema(implementation = ErrorResponseSchemas.UnauthorizedResponse.class)
                )
        ),
        @ApiResponse(
                responseCode = "500",
                description = "Internal Server Error - Unexpected server-side error occurred during request processing",
                content = @Content(
                        mediaType = "application/json",
                        schema = @Schema(implementation = ErrorResponseSchemas.ServerErrorResponse.class)
                )
        )
})
public @interface CommonApiResponses {
}