package com.heal.controlcenter.exception;

public class HealControlCenterException extends Exception {
    private final String errorMessage;
    private Object errorObject;

    public HealControlCenterException(String message, Throwable cause, String errorMessage) {
        super(message, cause);
        this.errorMessage = errorMessage;
    }

    public HealControlCenterException(Object message, String errorMessage) {
        super(errorMessage);
        this.errorMessage = errorMessage;
        this.errorObject = message;
    }

    public HealControlCenterException(Throwable cause, String errorMessage) {
        super(cause);
        this.errorMessage = errorMessage;
    }

    public HealControlCenterException(String errorMessage) {
        super(errorMessage);
        this.errorMessage = errorMessage;
    }

    public String getSimpleMessage() {
        return "HealControlCenterException :: " + this.errorMessage;
    }

    public Object getErrorObject() {
        return this.errorObject;
    }
}