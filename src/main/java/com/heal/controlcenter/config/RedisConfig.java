package com.heal.controlcenter.config;

import io.lettuce.core.ClientOptions;
import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.cluster.ClusterTopologyRefreshOptions;
import io.lettuce.core.protocol.ProtocolVersion;
import io.lettuce.core.resource.DefaultClientResources;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import jakarta.annotation.PreDestroy;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Base64;
import java.util.List;

@Configuration
@Slf4j
public class RedisConfig {

    @Value("${spring.redis.cluster.nodes:localhost:6379}")
    private List<String> clusterNodes;

    @Value("${spring.redis.ssl:true}")
    private boolean sslEnabled;

    @Value("${spring.redis.username:}")
    private String username;

    @Value("${spring.redis.password:}")
    private String password;

    @Value("${spring.redis.max.idle.connections:500}")
    private int maxIdleConnections;

    @Value("${spring.redis.min.idle.connections:500}")
    private int minIdleConnections;

    @Value("${spring.redis.max.total.connections:500}")
    private int maxTotalConnections;

    @Value("${spring.redis.max.wait.secs:5}")
    private int maxWaitTimeInSecs;

    @Value("${spring.redis.share.native.connection:false}")
    private boolean shareNativeConnection;

    @Value("${spring.redis.cluster.mode:true}")
    private boolean redisClusterMode;

    RedisConnectionFactory redisConnectionFactory;

    @Autowired
    DefaultClientResources defaultClientResources;

    @Bean
    public RedisConnectionFactory redisConnectionFactory() {
        log.info("Factory method called with cluster mode as {}", redisClusterMode);

        LettuceConnectionFactory factory;
        if (redisClusterMode) {
            RedisClusterConfiguration configuration = new RedisClusterConfiguration(clusterNodes);

            if (username != null && !username.trim().isEmpty()) {
                configuration.setUsername(username);
            }

            if (password != null && !password.trim().isEmpty()) {
                configuration.setPassword(new String(Base64.getDecoder().decode(password), StandardCharsets.UTF_8));
            }

            factory = new LettuceConnectionFactory(configuration, lettucePoolConfig());
        } else {
            RedisStandaloneConfiguration configuration = new RedisStandaloneConfiguration();

            if (username != null && !username.trim().isEmpty()) {
                configuration.setUsername(username);
            }

            if (password != null && !password.trim().isEmpty()) {
                configuration.setPassword(new String(Base64.getDecoder().decode(password), StandardCharsets.UTF_8));
            }

            String[] hostAndPort = clusterNodes.get(0).split(":");

            configuration.setHostName(hostAndPort[0]);
            configuration.setPort(Integer.parseInt(hostAndPort[1]));

            factory = new LettuceConnectionFactory(configuration, lettucePoolConfig());
        }

        if (sslEnabled) {
            factory.isUseSsl();
        }

        factory.setShareNativeConnection(shareNativeConnection);
        factory.setValidateConnection(false);
        factory.afterPropertiesSet();

        this.redisConnectionFactory = factory;
        return factory;
    }

    @Bean
    LettucePoolingClientConfiguration lettucePoolConfig() {
        log.debug("Lettuce pool config method called.");
        GenericObjectPoolConfig<Object> poolConfig = new GenericObjectPoolConfig<>();
        poolConfig.setMaxIdle(maxIdleConnections);
        poolConfig.setMinIdle(minIdleConnections);
        poolConfig.setMaxWait(Duration.ofSeconds(maxWaitTimeInSecs));
        poolConfig.setMaxTotal(maxTotalConnections);

        ClientOptions clientOptions;
        if (redisClusterMode) {
            clientOptions = ClusterClientOptions.builder()
                    .autoReconnect(true)
                    .protocolVersion(ProtocolVersion.RESP3)
                    .validateClusterNodeMembership(true)
                    .maxRedirects(5)
                    .cancelCommandsOnReconnectFailure(true)
                    .pingBeforeActivateConnection(true)
                    .topologyRefreshOptions(ClusterTopologyRefreshOptions.builder()
                            .enablePeriodicRefresh()
                            .build())
                    .build();
        } else {
            clientOptions = ClusterClientOptions.builder()
                    .autoReconnect(true)
                    .protocolVersion(ProtocolVersion.RESP2)
                    .cancelCommandsOnReconnectFailure(true)
                    .pingBeforeActivateConnection(true)
                    .build();
        }


        if (sslEnabled) {
            return LettucePoolingClientConfiguration.builder()
                    .poolConfig(poolConfig)
                    .commandTimeout(Duration.ofSeconds(maxWaitTimeInSecs))
                    .clientOptions(clientOptions)
                    .useSsl()
                    .build();
        }

        return LettucePoolingClientConfiguration.builder()
                .poolConfig(poolConfig)
                .commandTimeout(Duration.ofSeconds(maxWaitTimeInSecs))
                .clientOptions(clientOptions)
                .build();
    }

    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);
        template.setKeySerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new StringRedisSerializer());
        template.setEnableTransactionSupport(false);
        template.afterPropertiesSet();
        return template;
    }

    @PreDestroy
    public void shutdown() {
        if (redisConnectionFactory != null) {
            if (redisClusterMode)
                redisConnectionFactory.getClusterConnection().shutdown();
            else
                redisConnectionFactory.getConnection().shutdown();
        }

        defaultClientResources.shutdown();
    }
}
