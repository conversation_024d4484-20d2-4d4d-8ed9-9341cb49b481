package com.heal.controlcenter.config;

import com.heal.controlcenter.util.Constants;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 */
@Configuration
public class HealWebMvcConfiguration implements WebMvcConfigurer {
    //TODO test this by allowing only frontend url like : https://localhost:5173
    @Value("${header.access.control.allow.origins:*}")
    String frontendUrl;
    @Value("${header.access.control.allow.headers:Content-Type,Authorization,X-Requested-With,Content-Length,Accept,Origin}")
    String headers;
    @Value("${header.access.control.allow.methods:GET,POST,PUT,DELETE,OPTIONS,HEAD,PATCH}")
    String methods;
    @Value("${header.access.control.allow.credentials:false}")
    boolean allowCredentials;

    @Override
    public void addCorsMappings(CorsRegistry corsRegistry) {
        String[] origins = frontendUrl.split(Constants.COMMA);
        String[] headerArray = headers.split(Constants.COMMA);
        String[] methodArray = methods.split(Constants.COMMA);

        corsRegistry.addMapping("/**")
                .allowedOriginPatterns(origins) // Use patterns to support wildcards
                .allowedHeaders(headerArray)
                .allowedMethods(methodArray)
                .allowCredentials(allowCredentials)
                .maxAge(3600); // Cache preflight for 1 hour
    }

}
