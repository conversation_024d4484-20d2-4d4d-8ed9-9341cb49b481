package com.heal.controlcenter.util;

import com.heal.configuration.pojos.*;
import com.heal.controlcenter.dao.redis.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Repository
@Slf4j
public class CacheWrapper {

    @Autowired
    MasterDataRepo masterDataRepo;
    @Autowired
    AccountRepo accountRepo;
    @Autowired
    TenantRepo tenantRepo;
    @Autowired
    ServiceRepo serviceRepo;
    @Autowired
    UserRepo userRepo;

    //Don't delete 'mode' field. It is getting used inside the Cacheable condition
    @Value("${redis.cache.mode:0}")
    public int mode;
    //Don't delete 'perconaCacheMode' field. It is getting used inside the Cacheable condition
    @Value("${percona.cache.mode:0}")
    public int perconaCacheMode;
    public static String defaultSplitValue = "___@#@HeAl@#@___";

    @Cacheable(value = Constants.ACCOUNTS_ID_TENANTS, key = "#accountIdentifier",
            condition = "#accountIdentifier != null && #accountIdentifier.length() > 0", unless = "#result == null || #result.isEmpty()")
    public List<TenantOpenSearchDetails> getTenantOpenSearchDetails(String accountIdentifier) {
        log.info("Populating tenant information for account: {}", accountIdentifier);

        Account account = accountRepo.getAccount(accountIdentifier);
        if (account == null) {
            log.error("Account details not found for accountIdentifier: {}", accountIdentifier);
            return null;
        }

        TenantDetails tenantDetails = account.getTenantDetails();
        if (tenantDetails == null) {
            log.error("Tenant details not found for accountIdentifier: {}", accountIdentifier);
            return null;
        }

        List<TenantOpenSearchDetails> tenantOpenSearchDetailsList = tenantRepo.getTenantOpenSearchDetails(tenantDetails.getTenantIdentifier());
        if (tenantOpenSearchDetailsList.isEmpty()) {
            log.error("OpenSearch mapping not found for tenantId: {}, account:{}", tenantDetails.getTenantId(), accountIdentifier);
            return null;
        }

        return tenantOpenSearchDetailsList;
    }

    @Cacheable(value = Constants.HEAL_INDEX_ZONES, key = "'heal_index_zones'", condition = "#root.target.mode == 0",
            unless = "#result == null || #result.isEmpty()")
    public List<OSIndexZoneDetails> getHealIndexZones() {
        log.trace("OSIndexZoneDetails are fetched from redis for the first time and will be stored in cache before it expires");
        return masterDataRepo.getHealOSIndexToZoneMapping();
    }

    @Cacheable(value = Constants.VIEW_TYPES, key = "'view_types_id_map'", unless = "#result == null || #result.isEmpty()")
    public Map<String, List<ViewTypes>> getAllViewTypesIdMap() {
        log.trace("ViewTypes are fetched from redis for the first time and will be stored in cache before it expires");
        List<ViewTypes> allViewTypes = masterDataRepo.getViewTypes();
        return allViewTypes.stream().collect(Collectors.groupingBy(ViewTypes::getTypeName));
    }

    @Cacheable(value = Constants.SERVICE_CACHE, key = "#accountIdentifier + ':' + #serviceIdentifier",
            condition = "#accountIdentifier != null && #accountIdentifier.length() > 0 && #root.target.mode == 0",
            unless = "#result == null || #result.isEmpty()")
    public Service getServiceDetails(String accountIdentifier, String serviceIdentifier) {
        log.trace("Service details are fetched from redis for the first time and will be stored in cache before it expires");
        return serviceRepo.getServiceConfigurationByIdentifier(accountIdentifier, serviceIdentifier);
    }

    /**
     * TODO: After Percona update, revisit this method for user access details caching.
     * Retrieves user access details from Redis for the given user identifier.
     *
     * @param userIdentifier the unique identifier of the user
     * @return List of UserAccessDetails if found, otherwise null or empty
     */
   /*@Cacheable(value = Constants.USERS_ID_ACCESSDETAILS, key = "#userIdentifier",
           condition = "#root.target.mode == 0 && #userIdentifier != null && #userIdentifier.length() > 0", unless = "#result == null || #result.isEmpty()")
   public List<UserAccessDetails> getUserAccessDetails(String userIdentifier) {
       log.trace("UserAccessDetails are being fetched from Redis for the first time and will be stored in cache before it expires, " +
               "userIdentifier: [{}]", userIdentifier);
       return userRepo.getUserAccessDetails(userIdentifier);
   }*/

    /**
     * TODO: After Percona update, revisit this method for GraphQL query access profile caching.
     * Retrieves GraphQL query access profiles from Redis for the given profile ID.
     *
     * @param profileId the profile ID to fetch queries for
     * @return List of AccessProfileGqlQueries if found, otherwise null or empty
     */
   /*@Cacheable(value = Constants.USERS_PROFILES_ID_GQLQUERIES, key = "#profileId",
           condition = "#profileId > 0 && #root.target.mode == 0", unless = "#result == null || #result.isEmpty()")
   public List<AccessProfileGqlQueries> getAccessProfileGqlQueries(int profileId) {
       log.trace("AccessProfileGqlQueries are being fetched from Redis for the first time and will be stored in cache before it expires, " +
               "profileId: [{}]", profileId);
       return userRepo.getAccessProfileGqlQueries(profileId);
   }*/
}
