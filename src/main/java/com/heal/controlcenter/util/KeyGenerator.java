package com.heal.controlcenter.util;


import com.heal.controlcenter.exception.HealControlCenterException;
import org.bouncycastle.jce.ECNamedCurveTable;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.jce.spec.ECParameterSpec;
import org.bouncycastle.util.encoders.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.security.*;
import java.util.Objects;

/**
 * <AUTHOR> : 7/2/19
 */
public class KeyGenerator {
    private static final Logger LOGGER = LoggerFactory.getLogger(KeyGenerator.class);

    public static KeyPair generateKeys() throws HealControlCenterException {
        try {
            Security.removeProvider(Constants.BC_PROVIDER_NAME);
            Security.addProvider(new BouncyCastleProvider());
            ECParameterSpec ecSpec = ECNamedCurveTable.getParameterSpec("B-571");
            KeyPairGenerator g = KeyPairGenerator.getInstance("ECDSA", Constants.BC_PROVIDER_NAME);
            g.initialize(ecSpec, new SecureRandom());
            return g.generateKeyPair();
        } catch (NoSuchAlgorithmException | NoSuchProviderException | InvalidAlgorithmParameterException e) {
            LOGGER.error("Error while generating key pair.", e);
            throw new HealControlCenterException(e, "Error generating the key pair for account.");
        }
    }

    public static String getPublicKey(String publicKeyPath, KeyPair pair) {
        try {
            byte[] bytes = Base64.encode(pair.getPublic().getEncoded());
            if (Objects.nonNull(publicKeyPath)) {
                Files.write(Paths.get(publicKeyPath), bytes, StandardOpenOption.CREATE);
            }
            return new String(bytes);
        } catch (IOException e) {
            LOGGER.error("Error while saving public key" + e.getMessage(), e);
        }
        return null;
    }

    public static String getPrivateKey(String privateKeyPath, KeyPair pair) {
        try {
            byte[] bytes = Base64.encode(pair.getPrivate().getEncoded());
            if (Objects.nonNull(privateKeyPath)) {
                Files.write(Paths.get(privateKeyPath), bytes, StandardOpenOption.CREATE);
            }
            return new String(bytes);
        } catch (IOException e) {
            LOGGER.error("Error while saving private key" + e.getMessage(), e);
        }
        return null;
    }
}
