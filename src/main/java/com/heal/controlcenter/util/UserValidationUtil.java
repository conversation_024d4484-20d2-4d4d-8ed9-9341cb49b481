package com.heal.controlcenter.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.HealControlCenterException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Component
public class UserValidationUtil {

    @Autowired
    private ControllerDao controllerDao;
    @Autowired
    private AccountsDao accountDao;
    @Autowired
    UserDao userDao;
    @Autowired
    ObjectMapper objectMapper;

    public UserAccessDetails getUserAccessDetails(String userIdentifier, String accountIdentifier) throws HealControlCenterException {
        UserAccessBean accessDetails = userDao.fetchUserAccessDetailsUsingIdentifier(userIdentifier);

        if (accessDetails == null) {
            log.error("User access bean unavailable for user [{}] and account [{}]", userIdentifier, accountIdentifier);
            return null;
        }

        UserAccessDetails userAccessDetails = getUserAccessibleApplicationsServices(accessDetails.getAccessDetails(), userIdentifier, accountIdentifier);

        if (userAccessDetails == null) {
            log.error("User access details unavailable for user [{}] and account [{}]", userIdentifier, accountIdentifier);
            return null;
        }

        return userAccessDetails;
    }

    private UserAccessDetails getUserAccessibleApplicationsServices(String accessDetails, String userIdentifier, String accountIdentifier) throws HealControlCenterException {
        UserAccessDetails userAccessDetails = null;

        AccessDetailsBean accessDetailsBean;
        try {
            accessDetailsBean = objectMapper.readValue(accessDetails, AccessDetailsBean.class);
        } catch (Exception e) {
            log.error("Error occurred while mapping userAccessDetails Json to java object of AccessDetailsBean class");
            return null;
        }

        if (accessDetailsBean != null && accessDetailsBean.getAccounts() != null) {
            if (accessDetailsBean.getAccounts().contains(accountIdentifier)) {
                int accountId = accountDao.getAccountByIdentifier(accountIdentifier).getId();

                Map<String, AccessDetailsBean.Application> accessibleApplications = accessDetailsBean.getAccountMapping();
                if (accessibleApplications == null || accessibleApplications.isEmpty()) {
                    log.error("There no applications mapped to account [{}] and user [{}]",
                            accountIdentifier, userIdentifier);
                    return null;
                }

                AccessDetailsBean.Application applicationIdentifiers = accessibleApplications.get(accountIdentifier);
                List<ControllerBean> applicationControllerList = controllerDao.getApplicationsList(accountId);

                if (!applicationIdentifiers.getApplications().contains("*")) {
                    applicationControllerList = applicationControllerList.parallelStream()
                            .filter(app -> (applicationIdentifiers.getApplications().contains(app.getIdentifier())))
                            .collect(Collectors.toList());

                }
                userAccessDetails = populateUserAccessDetails(accountId, applicationControllerList);

            } else if (accessDetailsBean.getAccounts().contains("*")) {
                int accountId = accountDao.getAccountByIdentifier(accountIdentifier).getId();
                List<ControllerBean> applicationControllerList = controllerDao.getApplicationsList(accountId);
                userAccessDetails = populateUserAccessDetails(accountId, applicationControllerList);
            }
        }

        return userAccessDetails;
    }

    private UserAccessDetails populateUserAccessDetails(int accountId, List<ControllerBean> applicationControllerList) {
        UserAccessDetails userAccessDetails = new UserAccessDetails();
        userAccessDetails.setApplicationIds(new ArrayList<>());
        userAccessDetails.setServiceIds(new ArrayList<>());
        userAccessDetails.setServiceIdentifiers(new ArrayList<>());
        userAccessDetails.setTransactionIds(new ArrayList<>());
        userAccessDetails.setAgents(new ArrayList<>());
        userAccessDetails.setApplicationIdentifiers(new ArrayList<>());

        if (applicationControllerList.isEmpty()) {
            return userAccessDetails;
        }

        List<ViewApplicationServiceMappingBean> beans = getServicesMappedToApplications(accountId, applicationControllerList);
        userAccessDetails.setApplicationServiceMappingBeans(beans);
        userAccessDetails.setApplicationIds(beans.parallelStream().map(ViewApplicationServiceMappingBean::getApplicationId).distinct().collect(Collectors.toList()));
        userAccessDetails.setApplicationIdentifiers(beans.parallelStream().map(ViewApplicationServiceMappingBean::getApplicationIdentifier).distinct().collect(Collectors.toList()));
        userAccessDetails.setServiceIds(beans.parallelStream().map(ViewApplicationServiceMappingBean::getServiceId).filter(serviceId -> serviceId != null && serviceId > 0).distinct().collect(Collectors.toList()));
        userAccessDetails.setServiceIdentifiers(beans.parallelStream().map(ViewApplicationServiceMappingBean::getServiceIdentifier).distinct().collect(Collectors.toList()));

        return userAccessDetails;
    }

    private List<ViewApplicationServiceMappingBean> getServicesMappedToApplications(int accountId, List<ControllerBean> applicationControllerList) {
        if (applicationControllerList.isEmpty()) {
            return Collections.emptyList();
        }

        return applicationControllerList.parallelStream()
                .map(controller -> {
                    List<ViewApplicationServiceMappingBean> services = null;
                        services = controllerDao.getServicesMappedToApplication(accountId, controller.getIdentifier());
                    if (!services.isEmpty()) {
                        return services;
                    }
                    return Collections.singleton(ViewApplicationServiceMappingBean.builder()
                            .applicationId(controller.getId())
                            .applicationIdentifier(controller.getIdentifier())
                            .applicationName(controller.getName())
                            .build());
                })
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    /**
     * Retrieves a paginated list of applications accessible to a user,
     * optionally filtered by a search term and application-level access control.
     *
     * @param userIdentifier    the user's identifier
     * @param accountIdentifier the account's identifier
     * @param searchTerm        optional search term to filter by name or identifier
     * @param pageable          pagination information
     * @return paginated list of accessible applications
     */
    public List<ControllerBean> getAccessibleApplicationsForUserPaginated(String userIdentifier,
                                                                          String accountIdentifier, String searchTerm,
                                                                          Pageable pageable) {

        List<String> accessibleApps = getAccessibleApplicationIdentifiers(userIdentifier, accountIdentifier);
        if (accessibleApps.isEmpty()) return Collections.emptyList();

        int accountId = accountDao.getAccountByIdentifier(accountIdentifier).getId();

        try {
            if (accessibleApps.contains("*")) {
                return controllerDao.getApplicationsList(accountId, searchTerm, pageable);
            } else {
                return controllerDao.getApplicationsListWithIdentifiers(accountId, searchTerm, accessibleApps, pageable);
            }
        } catch (Exception e) {
            log.error("Error fetching accessible applications for userIdentifier={}, accountIdentifier={}, searchTerm={}",
                    userIdentifier, accountIdentifier, searchTerm, e);
            return Collections.emptyList();
        }
    }

    /**
     * Retrieves all applications accessible to a user for a given account.
     * Access is determined based on user's access details. No pagination is applied.
     *
     * @param userIdentifier    The unique identifier of the user
     * @param accountIdentifier The unique identifier of the account
     * @return List of ControllerBean objects representing accessible applications
     */
    public List<ControllerBean> getAccessibleApplicationsForUser(String userIdentifier, String accountIdentifier) throws HealControlCenterException {
        List<String> accessibleApps = getAccessibleApplicationIdentifiers(userIdentifier, accountIdentifier);
        if (accessibleApps.isEmpty()) return Collections.emptyList();

        int accountId = accountDao.getAccountByIdentifier(accountIdentifier).getId();

        return accessibleApps.contains("*")
                ? controllerDao.getApplicationsList(accountId)
                : controllerDao.getApplicationsListWithIdentifiers(accountId, accessibleApps);
    }

    /**
     * Returns the count of applications accessible to a user for a given account,
     * optionally filtered by a search term. This helps in paginating results on the UI.
     *
     * @param userIdentifier    The unique identifier of the user
     * @param accountIdentifier The unique identifier of the account
     * @param searchTerm        Optional search term to filter application name/identifier
     * @return Total number of accessible applications that match the filter
     */
    public long getAccessibleApplicationsCountForUser(String userIdentifier, String accountIdentifier, String searchTerm) throws HealControlCenterException {
        List<String> accessibleApps = getAccessibleApplicationIdentifiers(userIdentifier, accountIdentifier);
        if (accessibleApps.isEmpty()) return 0;

        int accountId = accountDao.getAccountByIdentifier(accountIdentifier).getId();

        return accessibleApps.contains("*")
                ? controllerDao.countApplications(accountId, searchTerm)
                : controllerDao.countApplicationsWithIdentifiers(accountId, searchTerm, accessibleApps);
    }

    /**
     * Retrieves the list of application identifiers that a user has access to,
     * based on their access details fetched from the database.
     *
     * <p>This method performs the following:
     * <ul>
     *   <li>Fetches user access details using the provided user identifier.</li>
     *   <li>Deserializes the access details JSON into an AccessDetailsBean.</li>
     *   <li>Checks if the user has access to the given account or wildcard access.</li>
     *   <li>Returns the list of accessible application identifiers or an empty list if access is not allowed.</li>
     * </ul>
     *
     * @param userIdentifier    Unique identifier of the user.
     * @param accountIdentifier Unique identifier of the account being queried.
     * @return A list of accessible application identifiers. Returns a list with "*" if wildcard access is granted.
     *         Returns an empty list if access is denied or access details are invalid.
     */
    private List<String> getAccessibleApplicationIdentifiers(String userIdentifier, String accountIdentifier) {
        UserAccessBean accessDetails = userDao.fetchUserAccessDetailsUsingIdentifier(userIdentifier);
        if (accessDetails == null) {
            log.error("User access not found for user [{}] and account [{}]", userIdentifier, accountIdentifier);
            return Collections.emptyList();
        }

        AccessDetailsBean accessDetailsBean;
        try {
            accessDetailsBean = objectMapper.readValue(accessDetails.getAccessDetails(), AccessDetailsBean.class);
        } catch (Exception e) {
            log.error("Failed to parse user access details JSON for user [{}]: {}", userIdentifier, e.getMessage());
            return Collections.emptyList();
        }

        if (accessDetailsBean.getAccounts().contains(accountIdentifier)) {
            Map<String, AccessDetailsBean.Application> accountMapping = accessDetailsBean.getAccountMapping();
            if (accountMapping == null || accountMapping.isEmpty()) {
                log.error("No applications mapped to account [{}] for user [{}]", accountIdentifier, userIdentifier);
                return Collections.emptyList();
            }

            AccessDetailsBean.Application appMapping = accountMapping.get(accountIdentifier);
            return appMapping != null ? appMapping.getApplications() : Collections.emptyList();

        } else if (accessDetailsBean.getAccounts().contains("*")) {
            return Collections.singletonList("*");
        }

        return Collections.emptyList();
    }
}
