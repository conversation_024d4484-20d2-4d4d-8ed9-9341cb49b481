package com.heal.controlcenter.util;

import com.appnomic.appsone.common.util.ConfProperties;

public class UIMessages {

    public static final String AUTH_KEY_INVALID = "Invalid authorization token. Reason: It is either null or empty.";
    public static final String AUTH_KEY_EXCEPTION_MESSAGE = "Invalid authorization token";
    public static final String ACCOUNT_IDENTIFIER_INVALID = "Invalid account identifier. Reason: It is either null or empty.";
    public static final String USERID_EXTRACTION_EXCEPTION_MESSAGE = "Error occurred while fetching userId from authorization token";
    public static final String REQUEST_BODY_NULL = "Invalid request. Reason: It is either null or empty.";
    public static final String REQUEST_BODY_NULL_EXCEPTION_MESSAGE = "Invalid request";
    public static final String SERVICE_IDENTIFIER_INVALID = "Invalid service identifier. Reason: It is either null or empty.";
    public static final String INVALID_SERVICE_NAME = "Invalid Service name : Name can not be empty and it should have 1 - 128 characters only.";
    public static final String INVALID_SERVICE_IDENTIFIER = "Invalid Service identifier : Identifier should have 1 - 128 characters only.";
    public static final String INVALID_SERVICE_TYPE = "Invalid Service Type : It should be 'Kubernetes'.";
    public static final String SERVICE_ID_INVALID = "Invalid serviceId. Reason: It is either null or empty.";
    public static final String SERVICE_ID_INVALID_EXCEPTION_MESSAGE = "Invalid serviceId";
    public static final String SERVICE_ID_INVALID_INTEGER = "Invalid serviceId [%s]. Reason: It is not a valid integer.";
    public static final String KPI_TYPE_INVALID = "Invalid kpiType. Reason: It should not be null or empty.";
    public static final String THRESHOLD_TYPE_INVALID = "Invalid thresholdType. Reason: It should not be null or empty.";
    public static final String INVALID_INSTANCE_ID = "Invalid instanceId [%s]. Reason: instanceId is invalid in the request.";
    public static final String INVALID_KPI_ID = "Invalid kpiId [%d]. Reason: kpiId is invalid in the request.";
    public static final String INSTANCE_IDENTIFIER_INVALID = "Invalid instance identifier";
    public static final String TYPE_NAME_INVALID = "Type name is required";
    public static final String ALLOWED_NAME_CHARACTERS = ConfProperties.getString("allowed.name.characters.message", "{} should be A-Z,a-z,0-9,underscore,period,hyphen and space characters.");
    public static final String ALLOWED_IDENTIFIER_CHARACTERS = ConfProperties.getString("allowed.identifier.characters.message", "{} should be A-Z,a-z,0-9,underscore,period and hyphen characters.");

    /**
     * Server validations related
     */
    public static final String ACCOUNT_IDENTIFIER_UNAVAILABLE = "Account with identifier [%s] is unavailable";
    public static final String USER_NOT_ALLOWED_TO_ACCESS_SERVICE = "User is not allowed to access service [%s]. Reason: User does not have access to this service.";
    public static final String USER_ACCESS_DETAILS_NOT_FOUND = "User access details not found for user [%s].";


    /** Permission-related messages */
    public static final String USER_NOT_ALLOWED_CREATE_APP =
            "User is not allowed to create an application. Only 'Super Admin' and 'Heal Admin' can create applications.";
    /**
     * Server validations related
     */
    public static final String SERVICE_IDENTIFIER_UNAVAILABLE = "Service with identifier [%s] is unavailable";
    public static final String INVALID_USER_ACCESS_DETAILS= "access details is not available";
    public static final String INVALID_KPI_TYPE ="Invalid kpiType. Reason: kpiType is undefined in the request.";
    public static final String ACCOUNT_NAME_ALREADY_EXISTS = "Account name [%s] already exists.";
    public static final String CONTROLLER_ADD_UPDATE_FAILED = "Failed to add/update %s controller: %s for account %s";
    public static final String CONTROLLER_CREATION_UPDATE_FAILED = "Controller creation/update failed";
    public static final String NO_RECORDS_FOR_TYPE_NAME = "No records for the given type name";
    public static final String INVALID_REQUEST_BODY = "Invalid request body";
    public static final String INSUFFICIENT_PARAMETERS = "Insufficient parameters. Expected accountIdentifier and applicationId.";
    public static final String APPLICATION_NOT_FOUND = "Application not found.";
    public static final String APPLICATION_PERCENTILE_NOT_FOUND = "Application percentile not found.";
    public static final String INVALID_PERCENTILE_VALUE = "Invalid percentile value. Must be one of the allowed values.";

}
