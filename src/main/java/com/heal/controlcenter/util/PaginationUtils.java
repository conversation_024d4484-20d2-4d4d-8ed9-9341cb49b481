package com.heal.controlcenter.util;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class PaginationUtils {

    /**
     * Applies pagination to any query with standard parameters
     */
    public static String applyPagination(String baseQuery, Pageable pageable) {
        StringBuilder query = new StringBuilder(baseQuery);

        // Add sorting
        if (pageable.getSort().isSorted()) {
            query.append(" ORDER BY ");
            pageable.getSort().forEach(order -> {
                query.append(order.getProperty())
                        .append(" ")
                        .append(order.getDirection().name())
                        .append(", ");
            });
            // Remove trailing comma
            query.setLength(query.length() - 2);
        }

        // Add pagination
        query.append(" LIMIT ? OFFSET ?");
        return query.toString();
    }

    /**
     * Builds pagination parameters for JDBC queries
     */
    public static List<Object> buildPaginationParams(List<Object> existingParams, Pageable pageable) {
        List<Object> params = new ArrayList<>(existingParams);
        params.add(pageable.getPageSize());
        params.add(pageable.getOffset());
        return params;
    }

    /**
     * Creates a Page object from results and total count
     */
    public static <T> Page<T> createPage(List<T> content, Pageable pageable, long total) {
        return new PageImpl<>(content, pageable, total);
    }

    /**
     * Validates pagination parameters
     */
    public static void validatePagination(Pageable pageable) {
        if (pageable.getPageSize() > 1000) {
            throw new IllegalArgumentException("Page size cannot exceed 1000");
        }
        if (pageable.getPageNumber() < 0) {
            throw new IllegalArgumentException("Page number cannot be negative");
        }
    }

}

