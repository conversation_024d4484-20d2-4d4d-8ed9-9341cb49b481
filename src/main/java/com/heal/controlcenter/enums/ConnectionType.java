package com.heal.controlcenter.enums;

import java.util.HashMap;
import java.util.Map;

public enum ConnectionType {

    CONN_TCP("TCP"),
    CONN_UDP("UDP"),
    CONN_RAW("RAW"),
    CONN_UNIX("UNIX");

    private static final Map<String, ConnectionType> lookup = new HashMap<>();

    static {
        for (ConnectionType d : ConnectionType.values()) {
            lookup.put(d.getDisplayName(), d);
        }
    }

    private final String displayName;

    ConnectionType(String displayName) {
        this.displayName = displayName;
    }

    public static ConnectionType get(String displayName) {
        return lookup.get(displayName);
    }

    public String getDisplayName() {
        return displayName;
    }
}
