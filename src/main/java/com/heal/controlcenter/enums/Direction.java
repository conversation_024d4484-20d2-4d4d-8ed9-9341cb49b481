package com.heal.controlcenter.enums;

import java.util.HashMap;
import java.util.Map;

public enum Direction {

    INCOMING_CONNECTION("Incoming"),
    OUTGOING_CONNECTION("Outgoing");

    private static final Map<String, Direction> lookup = new HashMap<>();

    static {
        for (Direction d : Direction.values()) {
            lookup.put(d.getDisplayName(), d);
        }
    }

    private final String displayName;

    Direction(String displayName) {
        this.displayName = displayName;
    }

    public static Direction get(String displayName) {
        return lookup.get(displayName);
    }

    public String getDisplayName() {
        return displayName;
    }
}
