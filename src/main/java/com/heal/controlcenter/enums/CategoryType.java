package com.heal.controlcenter.enums;

/**
 * Enum representing the types of categories.
 */
public enum CategoryType {
    WORKLOAD("Workload"),
    INFO("Info"),
    NON_INFO("Non-info");

    private final String type;

    CategoryType(String type) {
        this.type = type;
    }

    /**
     * Gets the type as a string.
     * @return the type string
     */
    public String getType() {
        return type;
    }

    @Override
    public String toString() {
        return type;
    }
}
