package com.heal.controlcenter.enums;

public enum Environment {
    DR("1"),
    PROD("2"),
    STAND_BY("3"),
    UAT("4"),
    DC("5"),
    NA("0");


    private String value;

    Environment(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    private void setValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return value;
    }
}
