package com.heal.controlcenter.beans;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class AgentBean {

    private int id;
    private String uniqueToken;
    private String name;
    private int agentTypeId;
    private String createdTime;
    private String updatedTime;
    private String lastModifiedBy;
    private int status;
    private String hostAddress;
    private String mode;
    private String description;
    private Integer compInstanceId;
    private int physicalAgentId;
    private boolean forensicsEnabled;
    private String version;
    private String physicalAgentIdentifier;
    private String userDetailsId;

}
