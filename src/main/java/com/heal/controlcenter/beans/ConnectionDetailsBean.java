package com.heal.controlcenter.beans;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConnectionDetailsBean {

    private int id;
    private int sourceId;
    private String sourceRefObject;
    private String sourceName;
    private String sourceIdentifier;
    private int destinationId;
    private String destinationRefObject;
    private String destinationName;
    private String destinationIdentifier;
    private String createdTime;
    private String updatedTime;
    private int accountId;
    private String userDetailsId;
    private int isDiscovery;

}
