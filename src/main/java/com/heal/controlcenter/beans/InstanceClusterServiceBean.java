package com.heal.controlcenter.beans;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Bean for instance cluster service details.
 * Used for validating cluster component consistency at service level.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InstanceClusterServiceBean {
    private int clusterComponentId;
    private int clusterComponentTypeId;
    private int clusterCommonVersionId;
}
