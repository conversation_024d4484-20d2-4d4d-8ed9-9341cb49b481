package com.heal.controlcenter.beans;

import lombok.Data;

import java.util.Objects;

@Data
public class MasterComponentTypeBean {

    private int id;
    private String name;
    private String description;
    private int isCustom;
    private int status;
    private String createdTime;
    private String updatedTime;
    private String userDetailsId;
    private int accountId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MasterComponentTypeBean that = (MasterComponentTypeBean) o;
        return Objects.equals(name, that.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name);
    }

}
