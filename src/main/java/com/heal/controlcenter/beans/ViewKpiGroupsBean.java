package com.heal.controlcenter.beans;

import lombok.Data;

@Data
public class ViewKpiGroupsBean {
    private int accountId;
    private int clusterAggregationType;
    private String clusterOperation;
    private String dataType;
    private boolean discovery;
    private String groupIdentifier;
    private int groupid;
    private String groupname;
    private boolean groupstatus;
    private String identifier;
    private int instanceAggregationType;
    private boolean isCustom;
    private int kpiid;
    private String kpiname;
    private boolean kpistatus;
    private String kpitype;
    private String measureUnits;
    private String rollupOperation;
    private String valueType;
}
