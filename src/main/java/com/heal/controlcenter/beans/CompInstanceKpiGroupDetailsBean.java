package com.heal.controlcenter.beans;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> : 17/1/19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CompInstanceKpiGroupDetailsBean {
    private int id;
    private String attributeValue = "ALL";
    private String aliasName;
    private int status;
    private String createdTime;
    private String updatedTime;
    private String userDetailsId;
    private int compInstanceId;
    private int mstProducerKpiMappingId;
    private int collectionInterval;
    private int mstKpiDetailsId;
    private int isDiscovery;
    private String kpiGroupName;
    private int mstKpiGroupId;
    private int mstProducerId;
    private int notification;
    private int attributeStatus;
    private int isGroup;
    private String producerName;
}