package com.heal.controlcenter.beans;

import com.heal.controlcenter.pojo.ActionsEnum;
import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class KpiMaintenanceStatusBean {

    private int compInstanceId;
    private int kpiId;
    private int kpiGroupId;
    @Builder.Default
    @EqualsAndHashCode.Exclude
    private String attributeValue = "ALL";
    private int accountId;
    @EqualsAndHashCode.Exclude
    private String userDetailsId;
    @Builder.Default
    @EqualsAndHashCode.Exclude
    private int persistence = -1;
    @Builder.Default
    @EqualsAndHashCode.Exclude
    private int suppression = -1;
    @EqualsAndHashCode.Exclude
    private int isMaintenanceExcluded;
    @EqualsAndHashCode.Exclude
    ActionsEnum actionForUpdate;
    @EqualsAndHashCode.Exclude
    private String createdTime;
    @EqualsAndHashCode.Exclude
    private String updatedTime;
    @EqualsAndHashCode.Exclude
    private String accountIdentifier;
    @EqualsAndHashCode.Exclude
    private int status;
}
