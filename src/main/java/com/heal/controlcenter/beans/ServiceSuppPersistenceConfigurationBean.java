package com.heal.controlcenter.beans;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceSuppPersistenceConfigurationBean {
    private int id;
    private int serviceId;
    private int accountId;
    private int applicationId;
    private int startCollectionInterval;
    private int endCollectionInterval;
    private int lowPersistence;
    private int lowSuppression;
    private int mediumSuppression;
    private int mediumPersistence;
    private int highPersistence;
    private int highSuppression;
    private int closingWindow;
    private int maxDataBreaks;
    private boolean lowEnable;
    private boolean highEnable;
    private boolean mediumEnable;
    private String jaegerServiceId;
    private String userDetailsId;
    private String createdTime;
    private String updatedTime;
}