package com.heal.controlcenter.beans;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceCommandArgumentBean {

    private int id;
    private int serviceId;
    private int agentTypeId;
    private int commandId;
    private int commandArgumentId;
    private String argumentValue;
    private String lastModifiedBy;
    private String createdTime;
    private String updatedTime;

}
