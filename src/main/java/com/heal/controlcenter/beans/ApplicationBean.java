package com.heal.controlcenter.beans;

import com.heal.configuration.pojos.ParentApplication;
import com.heal.controlcenter.pojo.ApplicationTags;
import com.heal.controlcenter.pojo.LinkedEnvironment;
import com.heal.controlcenter.pojo.SeverityLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplicationBean {

    private int id;
    private String name;
    private String identifier;
    private String environment;
    private List<ControllerBean> addServices;
    private TimezoneBean timezone;
    private List<Integer> deleteServiceIds;
    private int accountId;
    private String userId;
    private List<ApplicationTags> tags;
    private List<ParentApplication> parentApplications;
    private List<SeverityLevel> severity;
    private LinkedEnvironment linkedEnvironment;
    private int status;
}
