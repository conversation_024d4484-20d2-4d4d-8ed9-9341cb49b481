package com.heal.controlcenter.beans;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ForensicActionBean {

    private int actionId;
    private String actionName;
    private String actionType;
    private String commandName;
    private String commandIdentifier;
    private int commandId;
    private int commandTimeoutInSeconds;
    private int supCtrlTimeoutInSeconds;
    private int supCtrlRetryCount;
    private int actionStatus;
    private String lastModifiedBy;
    private String lastModifiedOn;
    private int categoryId;
    private String categoryName;
    private String categoryIdentifier;
    private int isCustomCategory;
    private int status;
}
