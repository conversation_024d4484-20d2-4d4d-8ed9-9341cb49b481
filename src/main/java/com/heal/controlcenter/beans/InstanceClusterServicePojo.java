package com.heal.controlcenter.beans;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * POJO for instance cluster service details.
 * Used for cluster component validation at service level.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InstanceClusterServicePojo {
    private int clusterComponentId;
    private int clusterComponentTypeId;
    private int clusterCommonVersionId;
}
