package com.heal.controlcenter.beans;

import com.appnomic.appsone.common.enums.DiscoveryStatus;
import lombok.Data;

@Data
public class AutoDiscoveryHostBean {

    private String hostname;
    private String hostIdentifier;
    private String operatingSystem;
    private String operatingSystemVersion;
    private String platform;
    private int environment;
    private String lastDiscoveryRunTime;
    private String lastUpdatedTime;
    private DiscoveryStatus discoveryStatus;
    private String ignoredBy;
    private int isIgnored;
    private Integer accountId;

}
