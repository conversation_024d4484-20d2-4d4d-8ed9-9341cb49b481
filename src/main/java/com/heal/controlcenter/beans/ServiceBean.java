package com.heal.controlcenter.beans;

import com.heal.controlcenter.pojo.ServiceGroupPojo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceBean {

    private int id;
    private String name;
    private String identifier;
    private int accountId;
    private String accountIdentifier;
    private String userId;
    private String layer;
    private String appIdentifier;
    private int appId;
    private List<Integer> appIds;
    private String mappedServiceIdentifier;
    private String type;
    private int isUpdate;
    private int status;
    private boolean entryPointService;
    private String  environment;
    private ServiceGroupPojo serviceGroup;
    private String mappedServiceIdentifiers;
    private String linkedIdentifier;
}