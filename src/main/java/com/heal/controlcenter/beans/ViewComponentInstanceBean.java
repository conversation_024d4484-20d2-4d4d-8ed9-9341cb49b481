package com.heal.controlcenter.beans;

import lombok.Data;

@Data
public class ViewComponentInstanceBean {
    private int id;
    private String name;
    private int status;
    private String identifier;
    private int hostId;
    private String hostName;
    private int isCluster;
    private int accountId;
    private String userDetailsId;
    private int discovery;
    private String hostAddress;
    private int isDR;
    private int mstComponentId;
    private String mstComponentName;
    private int mstComponentTypeId;
    private String mstComponentTypeName;
    private int mstComponentVersionId;
    private String componentVersionName;
    private int commonVersionId;
    private String commonVersionName;
    private String createdTime;
    private String updatedTime;
    private int parentInstanceId;
    private int supervisorId;
}
