package com.heal.controlcenter.beans;

import lombok.*;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommandDetailsBean {

    private int id;
    @NonNull
    private String name;
    private String identifier;
    @NonNull
    private String commandName;
    private int timeOutInSecs;
    private int outputTypeId;
    private int commandTypeId;
    private int actionId;
    private String lastModifiedBy;
    private String createdTime;
    private String updatedTime;
    private int isDefault;
    private int producerTypeId;
    private List<CommandDetailArgumentBean> commandArguments;

}
