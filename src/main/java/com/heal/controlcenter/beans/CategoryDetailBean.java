package com.heal.controlcenter.beans;

import lombok.*;

/**
 * Bean representing category details for persistence and business logic.
 * Includes validation and builder support.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CategoryDetailBean {
    private int id;
    private int accountId;
    private int status;
    private int isWorkLoad;
    private int isInformative;
    private boolean infoModified;
    private int isCustom;
    @NonNull
    private String name;
    private String description;
    private String createdTime;
    private String updatedTime;
    private String userDetailsId;
    private String identifier;
    private String accountIdentifier;
}
