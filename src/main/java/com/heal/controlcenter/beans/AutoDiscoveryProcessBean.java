package com.heal.controlcenter.beans;

import com.appnomic.appsone.common.enums.DiscoveryStatus;
import lombok.Data;
import org.boon.json.annotations.JsonInclude;

@Data
public class AutoDiscoveryProcessBean {

    private String processIdentifier;
    private String hostIdentifier;
    private String processName;
    private String processArgs;
    private String pid;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String lastDiscoveryRunTime;
    private String lastUpdatedTime;
    private int isBlacklisted;
    private int componentId;
    private int componentVersionId;
    private int componentTypeId;
    private String processCurrentWorkingDirectory;
    private DiscoveryStatus discoveryStatus;
    private int isIgnored;
    private String ignoredBy;
    private Integer accountId;

}
