package com.heal.controlcenter.businesslogic.utility;

import com.heal.configuration.pojos.KpiDetails;
import com.heal.configuration.pojos.KpiViolationConfig;
import com.heal.configuration.pojos.ThresholdConfig;
import com.heal.controlcenter.dao.redis.ServiceRepo;
import com.heal.controlcenter.pojo.StaticThresholdRules;
import com.heal.controlcenter.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ThresholdSyncService {

    private final ServiceRepo serviceRepo;

    public ThresholdSyncService(ServiceRepo serviceRepo) {
        this.serviceRepo = serviceRepo;
    }

    /**
     * A simple record to use as a robust, immutable key for maps.
     */
    private record KpiViolationKey(int kpiId, String attributeValue, int severityId, String applicableTo) {}

    @Async
    public void updateServiceKpiThresholdsInRedis(String accountId, String serviceId, List<StaticThresholdRules> rules,
                                                  Timestamp echoMilli, int lowSeverityId, int mediumSeverityId, int highSeverityId) {
        log.info("Async update of Redis cache started for account: {}, service: {}", accountId, serviceId);
        try {
            for (StaticThresholdRules rule : rules) {
                KpiDetails serviceKPI = serviceRepo.getServiceKPI(accountId, serviceId, Integer.parseInt(rule.getKpiId()));
                if (serviceKPI == null) {
                    log.error("Service KPI Details for account {} - service {} - KPI ID {} does not exist in redis. Skipping.",
                            accountId, serviceId, rule.getKpiId());
                    continue;
                }

                List<KpiViolationConfig> newConfigs = buildKPIViolationConfigObject(echoMilli, rule,
                        serviceKPI.getKpiViolationConfig(), lowSeverityId, mediumSeverityId, highSeverityId);

                Map<String, List<KpiViolationConfig>> kpiViolationMapByAttr = newConfigs.stream()
                        .collect(Collectors.groupingBy(KpiViolationConfig::getAttributeValue));

                serviceKPI.setKpiViolationConfig(kpiViolationMapByAttr);
                serviceRepo.updateServiceKpiById(accountId, serviceId, serviceKPI.getId(), serviceKPI);
                serviceRepo.updateServiceKpiByIdentifier(accountId, serviceId, serviceKPI.getIdentifier(), serviceKPI);
            }
            log.info("Successfully completed async update of Redis cache for account: {}, service: {}", accountId, serviceId);
        } catch (Exception e) {
            log.error("Error during async Redis update for account: {}, service: {}", accountId, serviceId, e);
        }
    }

    private List<KpiViolationConfig> buildKPIViolationConfigObject(Timestamp echoMilli, StaticThresholdRules rule,
                                                                   Map<String, List<KpiViolationConfig>> existingConfigMap,
                                                                   int lowId, int medId, int highId) {
        SimpleDateFormat formatter = new SimpleDateFormat(Constants.DATE_TIME);
        formatter.setTimeZone(TimeZone.getTimeZone("UTC"));
        List<KpiViolationConfig> newConfigs = new ArrayList<>();

        if (rule.getLowThreshold() != null) {
            KpiViolationConfig config = getKpiViolationConfig(echoMilli, rule, formatter, rule.getLowThreshold());
            config.setThresholdSeverityId(lowId);
            config.setThresholdSeverity(Constants.THRESHOLD_SEVERITY_TYPE_LOW);
            newConfigs.add(config);
        }
        if (rule.getWarningThreshold() != null) {
            KpiViolationConfig config = getKpiViolationConfig(echoMilli, rule, formatter, rule.getWarningThreshold());
            config.setThresholdSeverityId(medId);
            config.setThresholdSeverity(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
            newConfigs.add(config);
        }
        if (rule.getErrorThreshold() != null) {
            KpiViolationConfig config = getKpiViolationConfig(echoMilli, rule, formatter, rule.getErrorThreshold());
            config.setThresholdSeverityId(highId);
            config.setThresholdSeverity(Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
            newConfigs.add(config);
        }

        List<KpiViolationConfig> existingConfigs = existingConfigMap.values().stream().flatMap(List::stream).toList();
        return mergeKpiViolationConfigs(newConfigs, existingConfigs);
    }

    private KpiViolationConfig getKpiViolationConfig(Timestamp echoMilli, StaticThresholdRules rule, SimpleDateFormat formatter, ThresholdConfig threshold) {
        return KpiViolationConfig.builder()
                .startTime(formatter.format(echoMilli.getTime()))
                .attributeValue(rule.getKpiAttribute())
                .kpiId(Integer.parseInt(rule.getKpiId()))
                .applicableTo(rule.getKpiLevel())
                .definedBy(rule.isUserDefinedSOR() ? Constants.THRESHOLD_DEFINED_BY_USER : Constants.THRESHOLD_DEFINED_BY_SYSTEM)
                .coverageWindow(rule.getCoverageWindow())
                .minThreshold(threshold.getMin())
                .maxThreshold(threshold.getMax())
                .operation(threshold.getOperationType())
                .status(threshold.getStatus())
                .build();
    }

    private List<KpiViolationConfig> mergeKpiViolationConfigs(List<KpiViolationConfig> newConfigs, List<KpiViolationConfig> existingConfigs) {
        if (existingConfigs.isEmpty()) {
            return newConfigs;
        }
        // Use a Map with a proper key object to merge, ensuring new configs overwrite old ones.
        Map<KpiViolationKey, KpiViolationConfig> configMap = existingConfigs.stream()
                .collect(Collectors.toMap(c -> new KpiViolationKey(c.getKpiId(), c.getAttributeValue(), c.getThresholdSeverityId(), c.getApplicableTo()),
                        Function.identity()));

        newConfigs.forEach(newConfig -> {
            KpiViolationKey key = new KpiViolationKey(newConfig.getKpiId(), newConfig.getAttributeValue(), newConfig.getThresholdSeverityId(), newConfig.getApplicableTo());
            configMap.put(key, newConfig);
        });

        return new ArrayList<>(configMap.values());
    }
}