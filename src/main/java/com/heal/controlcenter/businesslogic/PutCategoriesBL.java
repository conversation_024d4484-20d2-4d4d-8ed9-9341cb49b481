package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.Category;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import com.heal.controlcenter.beans.CategoryDetailBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.CategoryDao;
import com.heal.controlcenter.dao.redis.CategoryRepo;
import com.heal.controlcenter.enums.CategoryType;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.CategoryDetails;
import com.heal.controlcenter.pojo.IdPojo;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.DateTimeUtil;
import com.heal.controlcenter.util.ServerValidationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class PutCategoriesBL implements BusinessLogic<List<CategoryDetails>, UtilityBean<List<CategoryDetailBean>>, List<IdPojo>> {

    private final CategoryDao categoryDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;
    private final CategoryRepo categoryRepo;

    public PutCategoriesBL(CategoryDao categoryDao, ClientValidationUtils clientValidationUtils,
                           ServerValidationUtils serverValidationUtils, CategoryRepo categoryRepo) {
        this.categoryDao = categoryDao;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
        this.categoryRepo = categoryRepo;
    }

    /**
     * Validates the input category list and account identifier on the client side.
     * Checks for empty/null values and field-level validation errors.
     *
     * @param categories    List of CategoryDetails objects to validate
     * @param requestParams Request parameters (account identifier)
     * @return UtilityBean containing validated categories and request metadata
     * @throws ClientException if validation fails
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<List<CategoryDetails>> clientValidation(List<CategoryDetails> categories, String... requestParams) throws ClientException {
        log.debug("Starting clientValidation for PUT categories: {} and requestParams: {}", categories, requestParams);
        if (categories == null || categories.isEmpty()) {
            log.error("Request body is empty for PUT categories");
            throw new ClientException("Request body cannot be empty");
        }
        String accountIdentifier = requestParams[0];
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);
        Map<String, String> error = new HashMap<>();
        for (CategoryDetails category : categories) {
            String categoryIdentifier = category.getIdentifier();
            if (categoryIdentifier == null || categoryIdentifier.trim().isEmpty()) {
                log.error("Category identifier is empty or null for category: {}", category);
                error.put("categoryIdentifier", "Category identifier cannot be empty or null.");
            }
            Map<String, String> validationErrors = category.validate();
            if (!validationErrors.isEmpty()) {
                log.error("Validation errors for category {}: {}", category, validationErrors);
                error.putAll(validationErrors);
            }
        }
        if (!error.isEmpty()) {
            log.error("Client validation failed for PUT categories: {}", error);
            throw new ClientException(error.toString());
        }
        Map<String, String> requestParamsMap = new HashMap<>();
        requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        log.debug("Client validation successful for PUT categories");
        return UtilityBean.<List<CategoryDetails>>builder()
                .requestParams(requestParamsMap)
                .pojoObject(categories)
                .metadata(new HashMap<>())
                .build();
    }

    /**
     * Performs server-side validation for category update requests.
     * Validates account, category existence, uniqueness, status change, and standard category restrictions.
     * Uses Spring JDBC for DB operations.
     *
     * @param utilityBean UtilityBean containing client-validated category update data
     * @return UtilityBean with prepared CategoryDetailBean list for update
     * @throws ServerException if validation fails
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<List<CategoryDetailBean>> serverValidation(UtilityBean<List<CategoryDetails>> utilityBean) throws ServerException {
        log.debug("[serverValidation] Starting for PUT categories: {}", utilityBean);
        try {
            String updatedTime = DateTimeUtil.getCurrentTimestampInGMT() != null ? DateTimeUtil.getCurrentTimestampInGMT().toString() : DateTimeUtil.getTimeInGMT(System.currentTimeMillis());
            List<CategoryDetails> categoriesToUpdate = utilityBean.getPojoObject();

            String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
            Account account = serverValidationUtils.accountValidation(accountIdentifier);
            int accountId = account.getId();
            ArrayList<CategoryDetailBean> beans = new ArrayList<>();

            for (CategoryDetails categoryUpdateDetails : categoriesToUpdate) {
                log.debug("[serverValidation] Validating category for update: {}", categoryUpdateDetails);
                CategoryDetailBean category = null;
                try {
                    category = categoryDao.getCategoryByAccountIdAndIdentifier(accountId, categoryUpdateDetails.getIdentifier());
                } catch (HealControlCenterException e) {
                    log.error("[serverValidation] Error fetching category by accountId and identifier: {}", e.getMessage(), e);
                    throw new ServerException("Error fetching category by accountId and identifier: " + e.getMessage());
                }
                if (category == null) {
                    log.error("[serverValidation] Category is not present for the specified account. Identifier: {}", categoryUpdateDetails.getIdentifier());
                    throw new ServerException("Category is not present in the specified account.");
                }

                boolean updateStatus = category.getStatus() != categoryUpdateDetails.getStatus();
                List<CategoryDetailBean> allCategories;
                try {
                    allCategories = categoryDao.getCategoriesForAccount(accountId);
                } catch (HealControlCenterException e) {
                    log.error("[serverValidation] Error fetching all categories for account: {}", e.getMessage(), e);
                    throw new ServerException("Error fetching all categories for account: " + e.getMessage());
                }

                // Custom category validation
                if (category.getIsCustom() == 1) {
                    validateCustomCategory(category, categoryUpdateDetails, allCategories, accountIdentifier, updateStatus);
                }

                // KPI count validation
                int kpiCount = fetchAndSetKpiCount(category, categoryUpdateDetails);
                if (updateStatus && kpiCount != 0) {
                    log.error("[serverValidation] Category status can only be modified if no KPIs are mapped to the category. CategoryId: {}", category.getId());
                    throw new ServerException("Category status can only be modified if no KPIs are mapped to the category.");
                }

                // Standard category validation
                if (category.getIsCustom() == 0) {
                    validateStandardCategory(category, categoryUpdateDetails, updateStatus);
                    beans.add(CategoryDetailBean.builder()
                            .id(category.getId())
                            .name(categoryUpdateDetails.getName())
                            .identifier(categoryUpdateDetails.getIdentifier())
                            .isCustom(category.getIsCustom())
                            .description(categoryUpdateDetails.getDescription())
                            .updatedTime(updatedTime)
                            .status(categoryUpdateDetails.getStatus())
                            .isInformative(category.getIsInformative())
                            .isWorkLoad(category.getIsWorkLoad())
                            .accountIdentifier(accountIdentifier)
                            .build());
                }

                // Informative flag calculation and bean addition
                int isInformative = (categoryUpdateDetails.getSubType().trim().equalsIgnoreCase(CategoryType.INFO.getType())) ? 1 : 0;
                beans.add(CategoryDetailBean.builder()
                        .id(category.getId())
                        .name(categoryUpdateDetails.getName())
                        .identifier(categoryUpdateDetails.getIdentifier())
                        .isCustom(category.getIsCustom())
                        .description(categoryUpdateDetails.getDescription())
                        .updatedTime(updatedTime)
                        .status(categoryUpdateDetails.getStatus())
                        .isInformative(isInformative)
                        .infoModified(isInformative != category.getIsInformative())
                        .isWorkLoad(category.getIsWorkLoad())
                        .accountIdentifier(accountIdentifier)
                        .build());
            }
            log.debug("[serverValidation] Server validation successful for PUT categories");
            return UtilityBean.<List<CategoryDetailBean>>builder()
                    .pojoObject(beans)
                    .requestParams(utilityBean.getRequestParams())
                    .metadata(new HashMap<>(utilityBean.getMetadata()))
                    .build();
        } catch (Exception e) {
            log.error("[serverValidation] Exception: {}", e.getMessage(), e);
            throw new ServerException("Server validation failed: " + e.getMessage());
        }
    }


    /**
     * Processes the validated category update requests.
     * Updates categories in the database and Redis, and handles info modification for mapped KPIs.
     *
     * @param utilityBean UtilityBean containing validated CategoryDetailBean list
     * @return List of IdPojo representing updated category IDs
     * @throws DataProcessingException if update fails
     */
    @Override
    @LogExecutionAnnotation
    public List<IdPojo> process(UtilityBean<List<CategoryDetailBean>> utilityBean) throws DataProcessingException {
        log.debug("Starting process for PUT categories: {}", utilityBean);
        List<CategoryDetailBean> beans = utilityBean.getPojoObject();
        ArrayList<IdPojo> result = new ArrayList<>();
        for (CategoryDetailBean bean : beans) {
            try {
                log.debug("Updating category in DB: {}", bean);
                int id = categoryDao.updateCategory(bean);
                if (bean.isInfoModified()) {
                    List<Integer> kpiIds = categoryDao.getKpiIdsForCategory(bean.getId());
                    log.debug("Info modified for categoryId {}. KPI IDs: {}", bean.getId(), kpiIds);
                    if (!kpiIds.isEmpty()) {
                        categoryDao.updateInfoForMappedKPIs(kpiIds, bean.getIsInformative(), bean.getUpdatedTime());
                        log.debug("Updated is_informative for mapped KPIs: {}", kpiIds);
                    }
                }
                result.add(IdPojo.builder()
                        .id(id)
                        .name(bean.getName())
                        .identifier(bean.getIdentifier())
                        .build());
                updateCategoryInRedis(bean, id);
                log.debug("Category updated successfully in DB and Redis: {}", bean.getIdentifier());
            } catch (Exception e) {
                log.error("Failed to update category: {}. Reason: {}", bean.getName(), e.getMessage(), e);
                throw new DataProcessingException("Failed to update category: " + bean.getName() + ", reason: " + e.getMessage());
            }
        }
        log.debug("Process completed for PUT categories. Updated IDs: {}", result);
        return result;
    }

    /**
     * Updates the category details in Redis for the given account and category.
     * Ensures the category list and individual category entry are updated atomically.
     *
     * @param categoryDetailBean Category details bean
     * @param id                 Category ID
     * @throws DataProcessingException if Redis update fails
     */
    public void updateCategoryInRedis(CategoryDetailBean categoryDetailBean, int id) throws DataProcessingException {
        log.debug("Updating category in Redis for accountIdentifier: {}, categoryIdentifier: {}", categoryDetailBean.getAccountIdentifier(), categoryDetailBean.getIdentifier());
        List<Category> existingCategoryDetails;
        try {
            existingCategoryDetails = categoryRepo.getCategoryDetails(categoryDetailBean.getAccountIdentifier());
            log.debug("Fetched existing categories from Redis for accountIdentifier: {}. Count: {}", categoryDetailBean.getAccountIdentifier(), existingCategoryDetails.size());
        } catch (Exception e) {
            log.error("Failed to fetch existing categories from Redis for accountIdentifier: {}. Reason: {}", categoryDetailBean.getAccountIdentifier(), e.getMessage(), e);
            existingCategoryDetails = new ArrayList<>();
        }
        Category updatedCategory = Category.builder()
                .id(id)
                .name(categoryDetailBean.getName())
                .identifier(categoryDetailBean.getIdentifier())
                .status(categoryDetailBean.getStatus())
                .updatedTime(categoryDetailBean.getUpdatedTime())
                .lastModifiedBy(categoryDetailBean.getUserDetailsId())
                .workload(categoryDetailBean.getIsWorkLoad())
                .informative(categoryDetailBean.getIsInformative())
                .description(categoryDetailBean.getDescription())
                .build();
        existingCategoryDetails.add(updatedCategory);
        try {
            categoryRepo.updateCategoryDetails(categoryDetailBean.getAccountIdentifier(), existingCategoryDetails);
            categoryRepo.updateCategory(categoryDetailBean.getAccountIdentifier(), updatedCategory);
            log.debug("Successfully updated Redis for category: {} in account: {}", categoryDetailBean.getIdentifier(), categoryDetailBean.getAccountIdentifier());
        } catch (Exception e) {
            log.error("Failed to update Redis for category: {} in account: {}. Reason: {}", categoryDetailBean.getIdentifier(), categoryDetailBean.getAccountIdentifier(), e.getMessage(), e);
            throw new DataProcessingException("Failed to update Redis for category: " + categoryDetailBean.getIdentifier() + ". Reason: " + e.getMessage());
        }
    }

    /**
     * Determines the sub-type of a category based on its properties.
     *
     * @param category CategoryDetailBean
     * @return CategoryType enum value
     */
    CategoryType getSubType(CategoryDetailBean category) {
        if (category.getIsWorkLoad() == 1) {
            return CategoryType.WORKLOAD;
        }

        if (category.getIsInformative() == 1) {
            return CategoryType.INFO;
        }
        return CategoryType.NON_INFO;
    }

    /**
     * Validates custom category update logic.
     * Ensures status is not updated and name is unique within the account.
     *
     * @param category              CategoryDetailBean being updated
     * @param categoryUpdateDetails CategoryDetails from request
     * @param allCategories         List of all categories for the account
     * @param accountIdentifier     Account identifier
     * @param updateStatus          True if status is being updated
     * @throws ServerException if validation fails
     */
    private void validateCustomCategory(CategoryDetailBean category, CategoryDetails categoryUpdateDetails, List<CategoryDetailBean> allCategories, String accountIdentifier, boolean updateStatus) throws ServerException {
        if (updateStatus) {
            log.error("Status update is not allowed for custom categories. CategoryId: {}", category.getId());
            throw new ServerException("Status update is not allowed for custom categories.");
        }
        boolean isNameChanged = !category.getName().equalsIgnoreCase(categoryUpdateDetails.getName());
        boolean isDuplicateName = allCategories.stream()
                .filter(c -> c.getId() != categoryUpdateDetails.getId())
                .anyMatch(c -> c.getName().equalsIgnoreCase(categoryUpdateDetails.getName()));
        if (isNameChanged && isDuplicateName) {
            log.error("Custom category name must be unique within the account. Attempted name: '{}', Account: '{}', CategoryId: '{}'.", categoryUpdateDetails.getName(), accountIdentifier, categoryUpdateDetails.getId());
            throw new ServerException("Category name should be unique in the specified account.");
        }
    }

    /**
     * Validates standard category update logic.
     * Ensures only description can be modified, and other fields remain unchanged.
     *
     * @param category              CategoryDetailBean being updated
     * @param categoryUpdateDetails CategoryDetails from request
     * @param updateStatus          True if status is being updated
     * @throws ServerException if validation fails
     */
    private void validateStandardCategory(CategoryDetailBean category, CategoryDetails categoryUpdateDetails, boolean updateStatus) throws ServerException {
        boolean isSubTypeChanged = categoryUpdateDetails.getSubType() != null &&
                !categoryUpdateDetails.getSubType().isEmpty() &&
                !categoryUpdateDetails.getSubType().trim().equalsIgnoreCase(getSubType(category).getType());
        boolean isNameChanged = !categoryUpdateDetails.getName().trim().equalsIgnoreCase(category.getName());
        if (isSubTypeChanged || updateStatus || isNameChanged) {
            log.error("Modification not allowed except for description in standard category. Details: [CategoryId: {}," +
                    " SubTypeChanged: {}, StatusChanged: {}, NameChanged: {}]", category.getId(), isSubTypeChanged, updateStatus, isNameChanged);
            throw new ServerException("Only category description can be modified for Standard categories.");
        }
        boolean isDescriptionInvalid = categoryUpdateDetails.getDescription() == null || categoryUpdateDetails.getDescription().isEmpty();
        if (isDescriptionInvalid) {
            log.error("Description provided is null or empty for the standard category. CategoryId: {}", category.getId());
            throw new ServerException("Description provided is null or empty for the standard category.");
        }
    }

    /**
     * Fetches and sets the KPI count for a category, updating the CategoryDetails object.
     *
     * @param category              CategoryDetailBean
     * @param categoryUpdateDetails CategoryDetails to update
     * @return KPI count
     * @throws ServerException if fetch fails
     */
    private int fetchAndSetKpiCount(CategoryDetailBean category, CategoryDetails categoryUpdateDetails) throws ServerException {
        int kpiCount = 0;
        try {
            kpiCount = categoryDao.getKpiCountForCategory(category.getId());
            categoryUpdateDetails.setKpiCount(kpiCount);
            log.debug("Fetched KPI count for categoryId {}: {}", category.getId(), kpiCount);
        } catch (Exception e) {
            log.error("Failed to fetch KPI count for category: {}. Reason: {}", category.getId(), e.getMessage(), e);
            throw new ServerException("Failed to fetch KPI count for category: " + category.getId());
        }
        return kpiCount;
    }
}