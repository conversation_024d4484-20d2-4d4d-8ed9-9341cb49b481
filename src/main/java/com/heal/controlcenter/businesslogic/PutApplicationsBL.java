package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.AnomalyConfiguration;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.*;
import com.heal.controlcenter.dao.redis.ApplicationRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.Application;
import com.heal.controlcenter.pojo.ApplicationAnomalyConfiguration;
import com.heal.controlcenter.pojo.IdPojo;
import com.heal.controlcenter.pojo.TagMappingDetails;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Slf4j
@Service
public class PutApplicationsBL implements BusinessLogic<List<Application>, UtilityBean<List<ApplicationBean>>, List<IdPojo>> {

    private final ControllerDao controllerDao;
    private final TagsDao tagsDao;
    private final MasterDataDao masterDataDao;
    private final AccountsDao accountsDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;
    private final UserDao userDao;
    private final ApplicationRepo applicationRepo;

    public PutApplicationsBL(ControllerDao controllerDao, TagsDao tagsDao, MasterDataDao masterDataDao,
                             AccountsDao accountsDao, ClientValidationUtils clientValidationUtils,
                             ServerValidationUtils serverValidationUtils, UserDao userDao, ApplicationRepo applicationRepo) {
        this.controllerDao = controllerDao;
        this.tagsDao = tagsDao;
        this.masterDataDao = masterDataDao;
        this.accountsDao = accountsDao;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
        this.userDao = userDao;
        this.applicationRepo = applicationRepo;
    }

    /**
     * Performs client-side validation for a list of Application objects.
     * Validates the application list and account identifier, and checks each application for required fields and correctness.
     * Throws ClientException if validation fails.
     *
     * @param requestBody   List of Application objects to be validated.
     * @param requestParams Variable arguments, where the first element is expected to be the account identifier.
     * @return UtilityBean containing the validated application list, request parameters, and empty metadata.
     * @throws ClientException if validation fails for the request body, account identifier, or any application fields.
     */
    @LogExecutionAnnotation
    public UtilityBean<List<Application>> clientValidation(List<Application> requestBody, String... requestParams) throws ClientException {
        log.info("[ClientValidation] Validating applications for accountIdentifier: {}", requestParams[0]);

        String applicationIdentifier;
        String accountIdentifier = requestParams[0];
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);

        Map<String, String> error = new HashMap<>();
        int index = 0;
        for (Application app : requestBody) {
            log.debug("[ClientValidation] Validating application: name='{}', identifier='{}'", app.getName(), app.getIdentifier());

            applicationIdentifier = app.getIdentifier();
            clientValidationUtils.applicationIdentifierValidation(applicationIdentifier);

            Map<String, String> appErrors = app.validate();
            if (!appErrors.isEmpty()) {
                for (Map.Entry<String, String> entry : appErrors.entrySet()) {
                    error.put("applications[" + index + "]." + entry.getKey(), entry.getValue());
                }
            }
            index++;
        }
        if (!error.isEmpty()) {
            String errorMessage = error.toString();
            log.error("[ClientValidation] Application validation failed: {}", errorMessage);
            throw new ClientException(errorMessage);
        }

        HashMap<String, String> requestParamsMap = new HashMap<>();
        requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        log.info("[ClientValidation] All applications validated successfully for accountIdentifier: {}", accountIdentifier);
        return UtilityBean.<List<Application>>builder()
                .requestParams(requestParamsMap)
                .pojoObject(requestBody)
                .metadata(new HashMap<>())
                .build();
    }

    /**
     * Performs server-side validation for a list of incoming Application objects.
     * Validates account, user, user profile, authorization, and timezone.
     * Throws ServerException if any validation fails.
     *
     * @param utilityBean UtilityBean containing the list of raw Application objects, request parameters, and metadata.
     * @return UtilityBean with validated ApplicationBean list, updated metadata, and request parameters.
     * @throws ServerException if validation fails for account, user, authorization, or timezone.
     */
    @LogExecutionAnnotation
    public UtilityBean<List<ApplicationBean>> serverValidation(UtilityBean<List<Application>> utilityBean) throws ServerException {
        log.info("[ServerValidation] Starting server validation for applications");

        List<Application> appList = utilityBean.getPojoObject();

        String userId = utilityBean.getMetadata().get(Constants.USER_ID_KEY).toString();

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        Account account = serverValidationUtils.accountValidation(accountIdentifier);
        int accountId = account.getId();

        UserInfoBean userInfoBean;
        try {
            userInfoBean = userDao.getUserDetails(userId);
        } catch (HealControlCenterException e) {
            throw new ServerException(e.getMessage());
        }

        UserProfileBean userProfileBean;
        try {
            userProfileBean = userDao.getUserProfile(userInfoBean.getProfileId());
        } catch (HealControlCenterException e) {
            throw new ServerException(e.getMessage());
        }

        if (!Constants.SUPER_ADMIN.equalsIgnoreCase(userProfileBean.getUserProfileName()) && !Constants.HEAL_ADMIN.equalsIgnoreCase(userProfileBean.getUserProfileName())) {
            throw new ServerException(UIMessages.USER_NOT_ALLOWED_CREATE_APP + " " + userId);
        }

        List<ApplicationBean> applicationList = new ArrayList<>();

        TimezoneBean timezone = null;
        for (Application app : appList) {
            log.debug("[ServerValidation] Validating application: name='{}', identifier='{}'", app.getName(), app.getIdentifier());
            //Here do exists by identifier logs
            boolean exists;
            try {
                exists = controllerDao.existsByApplicationIdentifier(app.getIdentifier());
            } catch (HealControlCenterException e) {
                throw new ServerException(e.getMessage());
            }
            if (exists) {
                log.info("[ServerValidation] Application identifier [{}] is valid for update", app.getIdentifier());
            }

            try {
                timezone = masterDataDao.getTimeZoneWithId(app.getTimezoneId());
            } catch (HealControlCenterException e) {
                log.error("Invalid timezone [{}]", app.getTimezoneId());
                throw new ServerException(e.getMessage());
            }

            applicationList.add(ApplicationBean.builder()
                    .name(app.getName().trim())
                    .identifier(app.getIdentifier())
                    .accountId(accountId)
                    .environment(app.getEnvironment())
                    .severity(app.getSeverity())
                    .linkedEnvironment(app.getLinkedEnvironment())
                    .tags(app.getTags())
                    .userId(userId)
                    .build());
        }
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID_KEY, userId);
        metadata.put(Constants.TIME_ZONE_TAG, timezone);

        log.info("[ServerValidation] All applications validated successfully for userId: {}", userId);
        return UtilityBean.<List<ApplicationBean>>builder()
                .pojoObject(applicationList)
                .requestParams(utilityBean.getRequestParams())
                .metadata(metadata)
                .build();
    }

    /**
     * Processes a list of ApplicationBean objects by updating them as controllers, handling tags, anomaly configs, and updating Redis cache.
     * Returns a list of updated application IDs. Throws DataProcessingException if any step fails.
     *
     * @param utilityBean UtilityBean containing the application list, metadata, and request parameters.
     * @return List of IdPojo representing the successfully updated applications.
     * @throws DataProcessingException if any part of the process (update, configuration, or Redis update) fails.
     */
    @LogExecutionAnnotation
    @Transactional(rollbackFor = {Exception.class})
    public List<IdPojo> process(UtilityBean<List<ApplicationBean>> utilityBean) throws DataProcessingException {
        log.info("[Process] Starting application update process");

        String updatedTime = Objects.requireNonNull(DateTimeUtil.getCurrentTimestampInGMT()).toString();
        String userId = utilityBean.getMetadata().get(Constants.USER_ID_KEY).toString();
        TimezoneBean timezoneBean = (TimezoneBean) utilityBean.getMetadata().get(Constants.TIME_ZONE_TAG);
        List<ApplicationBean> appBeanList = utilityBean.getPojoObject();
        List<IdPojo> idPojoList = new ArrayList<>();

        for (ApplicationBean appBean : appBeanList) {
            log.info("[Process] Updating application [{}] (identifier: {})", appBean.getName(), appBean.getIdentifier());
            // Fetch the existing application by identifier
            ControllerBean existingApp = null;
            try {
                existingApp = controllerDao.getFullApplicationDetailsByIdentifier(appBean.getIdentifier());
            } catch (HealControlCenterException e) {
                log.error("[Process] Error fetching application for update [{}]", appBean.getIdentifier(), e);
                throw new DataProcessingException("Error fetching application for update: " + appBean.getIdentifier());
            }
            if (existingApp == null) {
                log.error("[Process] Application with identifier [{}] not found for update", appBean.getIdentifier());
                throw new DataProcessingException("Application with identifier not found: " + appBean.getIdentifier());
            }
            // Update fields
            existingApp.setName(appBean.getName() + "_" + appBean.getEnvironment().toLowerCase());
            existingApp.setLastModifiedBy(userId);
            existingApp.setUpdatedTime(updatedTime);
            // Update in DB
            try {
                controllerDao.updateApplication(existingApp);
            } catch (HealControlCenterException e) {
                log.error("[Process] Error updating application [{}]", appBean.getIdentifier(), e);
                throw new DataProcessingException("Error updating application: " + appBean.getIdentifier());
            }

            String mappedIdentifier = null;
            // --- Linked Environment/alias update logic ---
            if (appBean.getLinkedEnvironment() != null) {
                log.info("[Process] Handling linked environment for application [{}]", appBean.getName());
                String identifier = appBean.getIdentifier();
                String environment = appBean.getEnvironment();
                ApplicationAliases alias = null;
                // Determine if identifier is for DC or DR
                boolean isDc = identifier.endsWith("_dc");
                boolean isDr = identifier.endsWith("_dr");
                try {
                    if (isDc) {
                        alias = controllerDao.getApplicationAliasByDcIdentifier(identifier);
                    } else if (isDr) {
                        alias = controllerDao.getApplicationAliasByDrIdentifier(identifier);
                    }
                } catch (Exception e) {
                    log.error("Error fetching application alias for identifier [{}]", identifier, e);
                }

                if (alias != null) {
                    String mappedToAppName = appBean.getLinkedEnvironment().getMappedToApplication();
                    String linkedEnv = appBean.getLinkedEnvironment().getEnvironment();
                    try {

                        ControllerBean mappedApp = controllerDao.getApplicationIdByName(mappedToAppName);
                        if (mappedApp != null) {
                            mappedIdentifier = mappedApp.getIdentifier();
                            if (mappedIdentifier.equals(alias.getDcApplicationIdentifier())) {
                                // Identifiers are equal, update common_name only
                                alias.setCommonName(appBean.getName());
                            } else {
                                // Identifiers are not equal, update dc_application_identifier and common_name
                                alias.setDcApplicationIdentifier(mappedIdentifier);
                                alias.setCommonName(appBean.getName());
                            }
                            alias.setUpdatedTime(updatedTime);
                            alias.setUserDetailsId(userId);
                            controllerDao.updateApplicationAlias(alias);
                        }
                    } catch (Exception e) {
                        log.error("Error updating application alias for linked environment [{}]", linkedEnv, e);
                    }
                }
            } else {
                // udpate only common name in the alias table
                try {
                    ApplicationAliases alias = controllerDao.getApplicationAliasByDcIdentifier(appBean.getIdentifier());
                    alias.setCommonName(appBean.getName());
                    alias.setUpdatedTime(updatedTime);
                    alias.setUserDetailsId(userId);
                    controllerDao.updateApplicationAlias(alias);
                } catch (Exception e) {
                    log.error("[Process] Error updating application alias for linked environment [{}]", appBean.getIdentifier(), e);
                }
            }


            // Get the application id to fetch the tag mapping from id
            int applicationId = existingApp.getId();

            // --- Timezone tag mapping update logic ---
            if (timezoneBean != null) {
                try {
                    TagMappingDetails timezoneTag = null;
                        timezoneTag = tagsDao.getTimezoneTagMappingByObjectId(applicationId, Constants.CONTROLLER);
                        log.error("[Process] Error fetching timezone tag mapping for applicationId [{}]", applicationId);
                    if (timezoneTag != null) {
                        // Update existing tag mapping
                        timezoneTag.setTagKey(String.valueOf(timezoneBean.getId()));
                        timezoneTag.setTagValue(String.valueOf(timezoneBean.getOffset()));
                        timezoneTag.setUpdatedTime(updatedTime);
                        timezoneTag.setAccountId(appBean.getAccountId());
                        timezoneTag.setUserDetailsId(userId);
                        tagsDao.updateTagMappingDetails(timezoneTag);
                    }
                } catch (Exception e) {
                    log.error("[Process] Timezone mapping to application failed: {}", e.getMessage(), e);
                    throw new DataProcessingException("Timezone mapping to application failed: " + e.getMessage());
                }
            }

            // update anomoly configuration
            // Fetch account anomaly config for this application
            try {
                // Get account anomaly config defaults
                int closingWindow = 0;
                int maxDataBreaks = 0;
                try {
                    AnomalyConfiguration config = accountsDao.getAccountAnomalyConfiguration(appBean.getAccountId());
                    if (config != null) {
                        closingWindow = config.getClosingWindow();
                        maxDataBreaks = config.getMaxDataBreaks();
                    }
                } catch (Exception e) {
                    log.warn("Could not fetch account anomaly config for accountId {}: {}", appBean.getAccountId(), e.getMessage());
                }
                // Update anomaly config if severity is present
                if (appBean.getSeverity() != null) {
                    boolean low = false, medium = false, high = false;
                    for (var severityType : appBean.getSeverity()) {
                        if ("low".equalsIgnoreCase(severityType.getType())) {
                            low = severityType.getStatus() == 1;
                        } else if ("medium".equalsIgnoreCase(severityType.getType())) {
                            medium = severityType.getStatus() == 1;
                        } else if ("high".equalsIgnoreCase(severityType.getType())) {
                            high = severityType.getStatus() == 1;
                        }
                    }
                    ApplicationAnomalyConfiguration anomalyConfig = ApplicationAnomalyConfiguration.builder()
                            .userDetailsId(userId)
                            .updatedTime(updatedTime)
                            .lowEnable(low)
                            .mediumEnable(medium)
                            .highEnable(high)
                            .closingWindow(closingWindow)
                            .maxDataBreaks(maxDataBreaks)
                            .applicationId(applicationId)
                            .build();
                    try {
                        controllerDao.updateApplicationAnomalyConfigurations(anomalyConfig);
                        log.info("[Process] Updated anomaly configuration for application [{}]", appBean.getName());
                    } catch (HealControlCenterException e) {
                        log.error("[Process] Failed to update anomaly configuration for application [{}]: {}", appBean.getName(), e.getMessage());
                        throw new DataProcessingException(e.getMessage());
                    }
                }
                //update redis part below
                try {
                    String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
                    List<com.heal.configuration.pojos.Application> redisApplications = applicationRepo.getApplicationsForAccount(accountIdentifier);
                    if (redisApplications != null && !redisApplications.isEmpty()) {
                        for (com.heal.configuration.pojos.Application redisApp : redisApplications) {
                            if (redisApp.getId() == applicationId) {
                                redisApp.setName(appBean.getName() + "_" + appBean.getEnvironment().toLowerCase());
                                redisApp.setIdentifier(appBean.getIdentifier() + "_" + appBean.getEnvironment().toLowerCase());
                                redisApp.setLastModifiedBy(userId);
                                redisApp.setUpdatedTime(updatedTime);
                                redisApp.setStatus(existingApp.getStatus());
                                // Update tags if needed
                                if (appBean.getTags() != null) {
                                    redisApp.setTags(appBean.getTags().stream().map(tag -> com.heal.configuration.pojos.Tags.builder()
                                            .key(tag.getKey())
                                            .type(tag.getType())
                                            .value(tag.getValue())
                                            .build()).toList());
                                }
                                applicationRepo.updateApplication(accountIdentifier, redisApp);
                                break;
                            }
                        }
                        applicationRepo.updateApplicationDetailsForAccount(accountIdentifier, redisApplications);
                        log.info("[Process] Updated Redis cache for application [{}] in account [{}]", appBean.getName(), accountIdentifier);
                    }
                } catch (Exception e) {
                    log.error("[Process] Error updating Redis cache for application [{}]: {}", appBean.getName(), e.getMessage());
                    throw new DataProcessingException("Error updating Redis cache: " + e.getMessage());
                }
            } catch (Exception e) {
                log.error("[Process] Error updating anomaly configuration for application [{}]: {}", appBean.getName(), e.getMessage());
                throw new DataProcessingException("Error updating anomaly configuration: " + e.getMessage());
            }

            idPojoList.add(IdPojo.builder()
                    .id(existingApp.getId())
                    .name(existingApp.getName())
                    .identifier(existingApp.getIdentifier())
                    .build());
            log.info("[Process] Application [{}] updated and added to result list.", appBean.getName());
        }
        log.info("[Process] All applications updated successfully.");
        return idPojoList;
    }
}
