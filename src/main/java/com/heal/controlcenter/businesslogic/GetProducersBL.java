package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.beans.ProducerValidationBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.ProducersDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.GetProducerPojo;
import com.heal.controlcenter.pojo.ProducerDetailsPojo;
import com.heal.controlcenter.pojo.ProducerKPIMappingDetailsPojo;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.DateTimeUtil;
import com.heal.controlcenter.util.ServerValidationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GetProducersBL implements BusinessLogic<String, UtilityBean<ProducerValidationBean>, Page<GetProducerPojo>> {

    private final ProducersDao producersDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;

    public GetProducersBL(ProducersDao producersDao,
                          ClientValidationUtils clientValidationUtils,
                          ServerValidationUtils serverValidationUtils) {
        this.producersDao = producersDao;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
    }

    /**
     * Validates the client request parameters.
     *
     * @param requestBody The request body.
     * @param requestParams The request parameters.
     * @return A UtilityBean containing the validated parameters.
     * @throws ClientException if the request parameters are invalid.
     */
    @Override
    public UtilityBean<String> clientValidation(String requestBody, String... requestParams) throws ClientException {
        String accountIdentifier = requestParams[0];
        String name = requestParams[1];
        String status = requestParams[2];
        String producerType = requestParams[3];
        String isCustom = requestParams[4];
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);
        Map<String, String> params = new HashMap<>();
        params.put("name", name);
        params.put("status", status);
        params.put("producerType", producerType);
        params.put("isCustom", isCustom);
        params.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        return UtilityBean.<String>builder()
                .requestParams(params)
                .metadata(new HashMap<>()) // Ensure metadata is always initialized
                .build();
    }

    /**
     * Validates the server-side entities.
     *
     * @param utilityBean The UtilityBean containing the request parameters.
     * @return A UtilityBean containing the validated entities.
     * @throws ServerException if the server-side entities are invalid.
     */
    @Override
    public UtilityBean<ProducerValidationBean> serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        String accountIdentifier = utilityBean.getRequestParams().get("accountIdentifier");
        Account account = serverValidationUtils.accountValidation(accountIdentifier);
        String isCustom = utilityBean.getRequestParams().get("isCustom");
        if (isCustom == null || isCustom.trim().isEmpty()) {
            throw new ServerException("isCustom parameter is required.");
        }
        try {
            int isCustomInt = Integer.parseInt(isCustom);
            if (isCustomInt != 0 && isCustomInt != 1) {
                throw new ServerException("isCustom parameter must be 0 or 1.");
            }
        } catch (NumberFormatException e) {
            throw new ServerException("isCustom parameter must be an integer.");
        }
        ProducerValidationBean validationBean = ProducerValidationBean.builder()
                .account(account)
                .name(utilityBean.getRequestParams().get("name"))
                .status(utilityBean.getRequestParams().get("status"))
                .producerType(utilityBean.getRequestParams().get("producerType"))
                .isCustom(Integer.parseInt(isCustom))
                .build();
        return UtilityBean.<ProducerValidationBean>builder()
                .pojoObject(validationBean)
                .pageable(utilityBean.getPageable())
                .build();
    }

    /**
     * Processes the request to fetch a list of producers.
     *
     * @param utilityBean The UtilityBean containing the validated entities.
     * @return A list of GetProducerPojo objects.
     * @throws DataProcessingException if there is an error processing the request.
     */
    @Override
    public Page<GetProducerPojo> process(UtilityBean<ProducerValidationBean> utilityBean) throws DataProcessingException {
        Account account = utilityBean.getPojoObject().getAccount();
        String name = utilityBean.getPojoObject().getName();
        String status = utilityBean.getPojoObject().getStatus();
        String producerType = utilityBean.getPojoObject().getProducerType();
        int isCustom = utilityBean.getPojoObject().getIsCustom();
        Pageable pageable = utilityBean.getPageable();
        try {
            int totalProducers = producersDao.getProducersCount(account.getId(), name, status, producerType, isCustom);
            List<ProducerDetailsPojo> producerDetailsList = producersDao.getProducerDetailsWithAccId(account.getId(),
                    null, name, status, producerType, isCustom, pageable);

            if (producerDetailsList.isEmpty()) {
                log.info("Producers unavailable for account [{}]", account.getId());
                return new PageImpl<>(Collections.emptyList(), pageable, 0);
            }

            List<ProducerKPIMappingDetailsPojo> producerKpiMappingData =
                    producersDao.getProducerKPIMappingDetails(account.getId(), null);
            Map<Integer, Long> producerKpiMapping = producerKpiMappingData.parallelStream()
                    .collect(Collectors.groupingBy(ProducerKPIMappingDetailsPojo::getProducerId, Collectors.counting()));

            List<GetProducerPojo> producerPojos = producerDetailsList.parallelStream()
                    .map(c -> GetProducerPojo.builder()
                            .id(c.getId())
                            .name(c.getName())
                            .description(c.getDescription())
                            .producerType(c.getProducerTypeName())
                            .isCustom(c.getIsCustom())
                            .status(c.getStatus())
                            .isGroupKPI(c.getIsKpiGroup() == 1)
                            .kpiMapped(producerKpiMapping.getOrDefault(c.getId(), 0L) > 0)
                            .createdOn(DateTimeUtil.getGMTToEpochTime(String.valueOf(c.getCreatedTime())))
                            .lastModifiedBy(c.getUserName())
                            .lastModifiedOn(DateTimeUtil.getGMTToEpochTime(String.valueOf(c.getUpdatedTime())))
                            .build()).collect(Collectors.toList());

            return new PageImpl<>(producerPojos, pageable, totalProducers);
        } catch (Exception e) {
            log.error("Error while fetching producers for account [{}]. Details: ", account.getId(), e);
            throw new DataProcessingException("Error while fetching producers for account " + account.getName());
        }
    }
}