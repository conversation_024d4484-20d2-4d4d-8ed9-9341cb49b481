package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import com.heal.controlcenter.beans.ControllerBean;
import com.heal.controlcenter.beans.TimezoneBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.beans.ViewApplicationServiceMappingBean;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.dao.mysql.TimeZoneDao;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ApplicationPojo;
import com.heal.controlcenter.pojo.ClusterComponentDetails;
import com.heal.controlcenter.pojo.ServiceClusterDetails;
import com.heal.controlcenter.util.*;
import io.micrometer.common.lang.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GetApplicationsBL implements BusinessLogic<String, UtilityBean<Account>, Page<ApplicationPojo>> {

    private final UserValidationUtil userValidationUtil;
    private final ControllerDao controllerDao;
    private final UserDao userDao;
    private final TimeZoneDao timeZoneDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;

    private boolean clusterDataRequired;

    public GetApplicationsBL(UserValidationUtil userValidationUtil, ControllerDao controllerDao,
                             UserDao userDao, TimeZoneDao timeZoneDao, ClientValidationUtils clientValidationUtils,
                             ServerValidationUtils serverValidationUtils) {
        this.userValidationUtil = userValidationUtil;
        this.controllerDao = controllerDao;
        this.userDao = userDao;
        this.timeZoneDao = timeZoneDao;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
    }

    /**
     * Performs client-side validation on request parameters such as authentication token,
     * account identifier, cluster data requirement flag, and search term.
     *
     * @param requestBody   the request body (not used here)
     * @param requestParams request parameters: authKey, accountIdentifier, clusterDataRequired, searchTerm
     * @return UtilityBean containing validated parameters and metadata
     * @throws ClientException if validation fails
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<String> clientValidation(String requestBody, String... requestParams) throws ClientException {
        String accountIdentifier = requestParams[0];
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);

        clusterDataRequired = Boolean.parseBoolean(requestParams[1]);
        String searchTerm = requestParams.length > 2 ? requestParams[2] : "";
        if (searchTerm != null) {
            searchTerm = searchTerm.trim();
            if (searchTerm.isEmpty()) {
                log.warn("Search term is empty after trimming. Defaulting to empty string.");
                searchTerm = "";
            }
        } else {
            log.warn("Search term is null in requestParams. Defaulting to empty string.");
            searchTerm = "";
        }

        if (searchTerm.length() > 100) {
            log.error("Search term exceeds maximum length of 100 characters.");
            throw new ClientException("Search term exceeds maximum length of 100 characters.");
        }

        log.info("Client validation successful for accountIdentifier: {}, searchTerm: '{}', clusterDataRequired: {}",
                accountIdentifier, searchTerm, clusterDataRequired);

        HashMap<String, String> requestParamsMap = new HashMap<>();
        requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        requestParamsMap.put(Constants.SEARCH_TERM_KEY, searchTerm);

        return UtilityBean.<String>builder()
                .requestParams(requestParamsMap)
                .metadata(new HashMap<>())
                .build();
    }

    /**
     * Performs server-side validation using the client-validated data.
     * It validates the authentication key, fetches the user ID, and verifies the account.
     *
     * @param utilityBean the validated parameters from the client
     * @return UtilityBean containing account info and metadata
     * @throws ServerException if validation fails
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<Account> serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        String userId = utilityBean.getMetadata().get(Constants.USER_ID_KEY).toString();
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        Account account = serverValidationUtils.accountValidation(accountIdentifier);

        return UtilityBean.<Account>builder()
                .pojoObject(account)
                .requestParams(utilityBean.getRequestParams())
                .metadata(Map.of(Constants.USER_ID_KEY, userId))
                .pageable(utilityBean.getPageable())
                .build();
    }


    /**
     * Retrieves and constructs a paginated list of ApplicationPojo objects based on the user's access rights.
     * It applies optional search and sorting, and includes cluster data if requested.
     *
     * @param utilityBean contains validated account and user metadata
     * @return paginated list of ApplicationPojo
     * @throws DataProcessingException if application data processing fails
     */
    @Override
    @LogExecutionAnnotation
    public Page<ApplicationPojo> process(UtilityBean<Account> utilityBean) throws DataProcessingException {
        try {
            Account account = utilityBean.getPojoObject();
            String userId = utilityBean.getMetadata().get(Constants.USER_ID_KEY).toString();
            Pageable pageable = utilityBean.getPageable();
            String searchTerm = utilityBean.getRequestParams().get(Constants.SEARCH_TERM_KEY);

            PaginationUtils.validatePagination(pageable);
            log.debug("Fetching applications for userId: {}, accountId: {}, searchTerm: '{}', pageable: {}",
                    userId, account.getIdentifier(), searchTerm, pageable);

            // Get total count first
            long totalCount = userValidationUtil.getAccessibleApplicationsCountForUser(
                    userId, account.getIdentifier(), searchTerm);
            if (totalCount == 0) {
                log.warn("No accessible applications found for user [{}] and account [{}]", userId, account.getIdentifier());
                return Page.empty();
            }

            // Get paginated applications from database
            List<ControllerBean> pagedApps = userValidationUtil.getAccessibleApplicationsForUserPaginated(
                    userId, account.getIdentifier(), searchTerm, pageable);
            if (pagedApps == null || pagedApps.isEmpty()) {
                return new PageImpl<>(Collections.emptyList(), pageable, totalCount);
            }

            List<Integer> appIds = pagedApps.stream().map(ControllerBean::getId).collect(Collectors.toList());
            List<String> userIdentifiers = pagedApps.stream().map(ControllerBean::getLastModifiedBy).filter(Objects::nonNull).distinct().collect(Collectors.toList());

            Map<Integer, TimezoneBean> timezoneMap = timeZoneDao.getApplicationTimezoneDetailsForApps(appIds);
            Map<String, String> usernameMap = userDao.getUsernamesFromIdentifiers(userIdentifiers);

            List<String> applicationIdentifiers = pagedApps.stream()
                    .map(ControllerBean::getIdentifier)
                    .collect(Collectors.toList());

            List<ViewApplicationServiceMappingBean> mappedServices =
                    controllerDao.getServicesMappedToApplications(account.getId(), applicationIdentifiers);

            List<ApplicationPojo> applications = new ArrayList<>();
            for (ControllerBean app : pagedApps) {
                try {
                    log.debug("Building ApplicationPojo for application: {}", app.getIdentifier());
                    TimezoneBean timezoneBean = timezoneMap.get(app.getId());
                    if (timezoneBean == null) {
                        log.warn("Timezone not found for application [{}]. Using default.", app.getIdentifier());
                    }

                    List<ViewApplicationServiceMappingBean> appServices = mappedServices != null ?
                            mappedServices.stream()
                                    .filter(s -> s.getApplicationIdentifier().equals(app.getIdentifier()))
                                    .collect(Collectors.toList()) : Collections.emptyList();

                    ApplicationPojo applicationPojo = ApplicationPojo.builder()
                            .id(app.getId())
                            .identifier(app.getIdentifier())
                            .name(app.getName())
                            .environment(app.getEnvironment())
                            .lastModifiedBy(usernameMap.get(app.getLastModifiedBy()))
                            .lastModifiedOn(DateTimeUtil.getGMTToEpochTime(app.getUpdatedTime()))
                            .services(getMappedServices(appServices))
                            .timezoneMilli(timezoneBean != null ? timezoneBean.getOffset() : 0)
                            .timeZoneString(timezoneBean != null ? timezoneBean.getTimeZoneId() : "UTC")
                            .build();

                    applications.add(applicationPojo);
                    log.info("ApplicationPojo built successfully for: {}", app.getIdentifier());
                } catch (Exception e) {
                    log.error("Error building ApplicationPojo for application: {}", app.getIdentifier(), e);
                }
            }

            if (pageable.getSort().isSorted() && !applications.isEmpty()) {
                log.debug(LogMessages.APPLYING_IN_MEMORY_SORTING, pageable.getSort());
                Comparator<ApplicationPojo> comparator = getApplicationComparator(pageable);
                if (comparator != null) {
                    applications.sort(comparator);
                    log.debug(LogMessages.SORTING_COMPLETE, applications.get(0).getIdentifier());
                }
            }

            return new PageImpl<>(applications, pageable, totalCount);

        } catch (Exception e) {
            log.error("Error processing applications", e);
            throw new DataProcessingException("Failed to process applications: " + e.getMessage());
        }
    }

    /**
     * Builds the list of {@link ServiceClusterDetails} for each mapped service.
     * Includes host and component cluster details only if clusterDataRequired is true.
     *
     * @param mappedServices list of services mapped to an application
     * @return list of service-cluster details
     */
    private List<ServiceClusterDetails> getMappedServices(List<ViewApplicationServiceMappingBean> mappedServices) {
        long start = System.currentTimeMillis();
        log.debug("Start getMappedServices() at {}", start);
        try {
            if (!clusterDataRequired) {
                log.debug("Cluster data not required. Returning basic service details.");
                return mappedServices.stream().map(s -> ServiceClusterDetails.builder()
                        .id(s.getServiceId())
                        .name(s.getServiceName())
                        .identifier(s.getServiceIdentifier())
                        .build()).collect(Collectors.toList());
            }

            log.debug("Cluster data required. Fetching host and component cluster details.");
            List<String> serviceIdentifiers = mappedServices.stream()
                    .map(ViewApplicationServiceMappingBean::getServiceIdentifier)
                    .collect(Collectors.toList());

            List<ClusterComponentDetails> allHostDetails = controllerDao.getHostClusterComponentDetailsForServices(serviceIdentifiers);
            List<ClusterComponentDetails> allComponentDetails = controllerDao.getComponentClusterComponentDetailsForServices(serviceIdentifiers);

            Map<String, List<ClusterComponentDetails>> hostDetailsByService = allHostDetails.stream()
                    .collect(Collectors.groupingBy(ClusterComponentDetails::getServiceIdentifier));
            Map<String, List<ClusterComponentDetails>> componentDetailsByService = allComponentDetails.stream()
                    .collect(Collectors.groupingBy(ClusterComponentDetails::getServiceIdentifier));

            return mappedServices.stream().map(service -> {
                List<ClusterComponentDetails> host = hostDetailsByService.getOrDefault(service.getServiceIdentifier(), Collections.emptyList());
                List<ClusterComponentDetails> component = componentDetailsByService.getOrDefault(service.getServiceIdentifier(), Collections.emptyList());
                return ServiceClusterDetails.builder()
                        .id(service.getServiceId())
                        .name(service.getServiceName())
                        .identifier(service.getServiceIdentifier())
                        .hostCluster(host)
                        .componentCluster(component)
                        .build();
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Error in getMappedServices", e);
            return Collections.emptyList();
        } finally {
            long end = System.currentTimeMillis();
            log.debug("End getMappedServices() at {}, duration: {} ms", end, (end - start));
        }
    }

    /**
     * Returns a comparator to sort {@link ControllerBean} list based on pageable sort orders.
     *
     * @param pageable contains sorting information
     * @return comparator for ControllerBean or null if unsupported
     */
    @Nullable
    private static Comparator<ApplicationPojo> getApplicationComparator(Pageable pageable) {
        Comparator<ApplicationPojo> comparator = null;

        for (Sort.Order order : pageable.getSort()) {
            Comparator<ApplicationPojo> currentComparator = null;
            if (order.getProperty().equalsIgnoreCase("lastModifiedBy")) {
                currentComparator = Comparator.comparing(
                        ApplicationPojo::getLastModifiedBy,
                        Comparator.nullsLast(String::compareToIgnoreCase)
                );
            } else if (order.getProperty().equalsIgnoreCase("lastModifiedOn")) {
                currentComparator = Comparator.comparing(
                        ApplicationPojo::getLastModifiedOn,
                        Comparator.nullsLast(Comparator.naturalOrder())
                );
            }

            if (currentComparator != null) {
                if (!order.isAscending()) {
                    currentComparator = currentComparator.reversed();
                }
                comparator = comparator == null ? currentComparator : comparator.thenComparing(currentComparator);
            }
        }

        return comparator;
    }
}
