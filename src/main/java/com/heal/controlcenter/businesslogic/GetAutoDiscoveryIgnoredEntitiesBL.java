package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.AutoDiscoveryDao;
import com.heal.controlcenter.enums.Entity;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.AutoDiscoveryIgnoredEntitiesPojo;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.ServerValidationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GetAutoDiscoveryIgnoredEntitiesBL implements BusinessLogic<Object, Object, List<AutoDiscoveryIgnoredEntitiesPojo>> {

    private final AccountsDao accountDao;
    private final AutoDiscoveryDao autoDiscoveryDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;

    public GetAutoDiscoveryIgnoredEntitiesBL(AccountsDao accountDao, AutoDiscoveryDao autoDiscoveryDao,
                                             ClientValidationUtils clientValidationUtils,
                                             ServerValidationUtils serverValidationUtils) {
        this.accountDao = accountDao;
        this.autoDiscoveryDao = autoDiscoveryDao;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
    }

    @Override
    public UtilityBean<Object> clientValidation(Object requestBody, String... requestParams) throws ClientException {
        String authKey = requestParams[0];
        clientValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = requestParams[1];
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);

        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(authKey, accountIdentifier);

        return UtilityBean.builder()
                .requestParams(requestParamsMap)
                .build();
    }

    @Override
    public Object serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        serverValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        serverValidationUtils.accountValidation(accountIdentifier);

        return null;
    }

    @Override
    public List<AutoDiscoveryIgnoredEntitiesPojo> process(Object bean) throws DataProcessingException {
        List<AutoDiscoveryHostBean> ignoredHostsList;
        List<AutoDiscoveryProcessBean> ignoredProcessesList;
        Map<String, List<String>> hostIdentifierWiseNetworkInterfaceMap;
        Map<String, List<AutoDiscoveryDiscoveredAttributesBean>> hostIdentifierWiseApplicableDiscoveredAttributes;
        try {
            // fetch ignored hosts
            ignoredHostsList = autoDiscoveryDao.getIgnoredHosts();
            // fetch ignored processes
            ignoredProcessesList = autoDiscoveryDao.getIgnoredProcesses();
            hostIdentifierWiseNetworkInterfaceMap = autoDiscoveryDao.getNetworkInterfacesList()
                    .parallelStream().collect(Collectors.groupingBy(NetworkInterfaceBean::getHostIdentifier,
                            Collectors.mapping(NetworkInterfaceBean::getInterfaceIP, Collectors.toList())));

            hostIdentifierWiseApplicableDiscoveredAttributes = autoDiscoveryDao.getDiscoveredAttributesList()
                    .parallelStream()
                    .filter(attr -> attr.getEntityType().toString().equalsIgnoreCase("Host") &&
                            (attr.getAttributeName().equalsIgnoreCase("SshPort") ||
                                    attr.getAttributeName().equalsIgnoreCase("MonitorPort") ||
                                    attr.getAttributeName().equalsIgnoreCase("JMXPort")))
                    .collect(Collectors.groupingBy(AutoDiscoveryDiscoveredAttributesBean::getDiscoveredAttributesIdentifier,
                            Collectors.mapping(Function.identity(), Collectors.toList())));
        } catch (HealControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
        List<AutoDiscoveryIgnoredEntitiesPojo> ignoredHosts = ignoredHostsList.parallelStream().map(host -> {
            AutoDiscoveryIgnoredEntitiesPojo temp = new AutoDiscoveryIgnoredEntitiesPojo();
            temp.setName(host.getHostname());
            temp.setIdentifier(host.getHostIdentifier());
            temp.setEntityType(Entity.Host);
            temp.setLastDiscoveryRunTime(host.getLastDiscoveryRunTime());
            temp.setLastUpdatedTime(host.getLastUpdatedTime());
            temp.setIgnoredBy(host.getIgnoredBy());
            temp.setHostAddress(hostIdentifierWiseNetworkInterfaceMap.getOrDefault(host.getHostIdentifier(), new ArrayList<>()));

            List<AutoDiscoveryDiscoveredAttributesBean> entityAttributes = hostIdentifierWiseApplicableDiscoveredAttributes
                    .getOrDefault(host.getHostIdentifier(), new ArrayList<>());

            ArrayList<String> attributes = new ArrayList<>();
            for (AutoDiscoveryDiscoveredAttributesBean attr : entityAttributes) {
                attributes.add(attr.getAttributeName() + ":" + attr.getAttributeValue());
            }
            temp.setDiscoveredEntities(attributes);

            return temp;
        }).toList();

        List<AutoDiscoveryIgnoredEntitiesPojo> ignoredProcesses = ignoredProcessesList.parallelStream().map(process -> {
            AutoDiscoveryIgnoredEntitiesPojo temp = new AutoDiscoveryIgnoredEntitiesPojo();
            temp.setName(process.getProcessName());
            temp.setIdentifier(process.getProcessIdentifier());
            temp.setEntityType(Entity.CompInstance);
            temp.setIgnoredBy(process.getIgnoredBy());
            temp.setLastUpdatedTime(process.getLastUpdatedTime());
            temp.setLastDiscoveryRunTime(process.getLastDiscoveryRunTime());
            temp.setHostAddress(hostIdentifierWiseNetworkInterfaceMap.getOrDefault(process.getHostIdentifier(), new ArrayList<>()));

            List<AutoDiscoveryDiscoveredAttributesBean> entityAttributes = hostIdentifierWiseApplicableDiscoveredAttributes
                    .getOrDefault(process.getProcessIdentifier(), new ArrayList<>());

            ArrayList<String> attributes = new ArrayList<>();
            for (AutoDiscoveryDiscoveredAttributesBean attr : entityAttributes) {
                attributes.add(attr.getAttributeName() + ":" + attr.getAttributeValue());
            }
            temp.setDiscoveredEntities(attributes);

            return temp;
        }).collect(Collectors.toList());

        List<AutoDiscoveryIgnoredEntitiesPojo> finalIgnoredEntities = new ArrayList<>();
        finalIgnoredEntities.addAll(ignoredHosts);
        finalIgnoredEntities.addAll(ignoredProcesses);

        finalIgnoredEntities.sort(Comparator.comparing(AutoDiscoveryIgnoredEntitiesPojo::getLastDiscoveryRunTime, Comparator.reverseOrder())
                .thenComparing(AutoDiscoveryIgnoredEntitiesPojo::getName, Comparator.comparingInt(o -> Character.toLowerCase(o.charAt(0)))));

        return finalIgnoredEntities;
    }
}
