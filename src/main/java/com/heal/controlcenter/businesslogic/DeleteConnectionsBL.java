package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.aop.LogExecutionAnnotation;
import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.ConnectionDetailsBean;
import com.heal.controlcenter.beans.ControllerBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.ConnectionDetailsDao;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.dao.redis.ConnectionRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ConnectionDetailsPojo;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Slf4j
@Service
public class DeleteConnectionsBL implements BusinessLogic<List<ConnectionDetailsPojo>, UtilityBean<List<ConnectionDetailsBean>>, String> {

    private final AccountsDao accountsDao;
    private final ConnectionDetailsDao connectionDetailsDao;
    private final ControllerDao controllerDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ConnectionRepo connectionRepo;

    public DeleteConnectionsBL(AccountsDao accountsDao, ConnectionDetailsDao connectionDetailsDao, ControllerDao controllerDao,
                               ClientValidationUtils clientValidationUtils, ConnectionRepo connectionRepo) {
        this.accountsDao = accountsDao;
        this.connectionDetailsDao = connectionDetailsDao;
        this.controllerDao = controllerDao;
        this.clientValidationUtils = clientValidationUtils;
        this.connectionRepo = connectionRepo;
    }


    /**
     * Validates the request body and parameters for deleting connections.
     *
     * @param body        List of ConnectionDetailsPojo objects from the request body.
     * @param requestParams Account identifier.
     * @return UtilityBean containing the validated data and request parameters.
     * @throws ClientException if the request is invalid.
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<List<ConnectionDetailsPojo>> clientValidation(List<ConnectionDetailsPojo> body, String... requestParams) throws ClientException {
        String accountIdentifier = (requestParams.length > 0) ? requestParams[0] : "";
        accountIdentifier = (accountIdentifier != null) ? accountIdentifier.trim() : "";
        log.debug("[clientValidation] Received accountIdentifier: {}", accountIdentifier);
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);
        if (body == null || body.isEmpty()) throw new ClientException("Connection data is empty or null");
        Map<String, String> errors = new HashMap<>();
        Set<ConnectionDetailsPojo> connSet = new HashSet<>(body);
        if (connSet.size() < body.size()) {
            log.warn("[clientValidation] Duplicate connections found in request for accountIdentifier: {}", accountIdentifier);
            errors.put("duplicateConnections", "Duplicate connections found");
        }
        for (int i = 0; i < body.size(); i++) {
            ConnectionDetailsPojo conn = body.get(i);
            if (conn == null) {
                log.error("[clientValidation] Connection object at index {} is null for accountIdentifier: {}", i, accountIdentifier);
                errors.put("connections[" + i + "]", "Connection object is null");
                continue;
            }
            Map<String, String> connErrors = conn.validate();
            for (Map.Entry<String, String> entry : connErrors.entrySet()) {
                errors.put("connections[" + i + "]." + entry.getKey(), entry.getValue());
            }
        }
        if (!errors.isEmpty()) {
            log.error("[clientValidation] Connection validation failed: {}", errors);
            throw new ClientException(errors.toString());
        }
        HashMap<String, String> requestParamsMap = new HashMap<>();
        requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        return UtilityBean.<List<ConnectionDetailsPojo>>builder()
                .requestParams(requestParamsMap)
                .pojoObject(body)
                .metadata(new HashMap<>())
                .build();
    }

    /**
     * Performs server-side validation for deleting connections.
     *
     * @param utilityBean UtilityBean containing the request data.
     * @return UtilityBean containing the processed data.
     * @throws ServerException if there is a server-side error.
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<List<ConnectionDetailsBean>> serverValidation(UtilityBean<List<ConnectionDetailsPojo>> utilityBean) throws ServerException {
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        AccountBean accountBean;
        try {
            accountBean = accountsDao.getAccountDetailsForIdentifier(accountIdentifier);
        } catch (HealControlCenterException e) {
            log.error("[serverValidation] Error fetching account details for identifier: {}", accountIdentifier, e);
            throw new ServerException("Account identifier is invalid");
        }
        if (accountBean == null) throw new ServerException("Account identifier is invalid");
        int accountId = accountBean.getId();
        String userId = String.valueOf(utilityBean.getMetadata().get(Constants.USER_ID_KEY));
        List<ControllerBean> serviceList;
        try {
            serviceList = controllerDao.getServicesList(accountId);
        } catch (HealControlCenterException e) {
            log.error("[serverValidation] Error fetching services for accountId: {}", accountId, e);
            throw new ServerException("Error fetching services for account");
        }
        List<ConnectionDetailsBean> connectionDetailsBeanList = new ArrayList<>();
        for (ConnectionDetailsPojo connection : utilityBean.getPojoObject()) {
            ControllerBean srcController = serviceList.stream().filter(c -> c.getIdentifier().equals(connection.getSourceServiceIdentifier().trim()) && c.getStatus() == 1).findAny().orElse(null);
            if (srcController == null)
                throw new ServerException("Source Service Identifier '[" + connection.getSourceServiceIdentifier() + "]' does not exist.");
            ControllerBean destController = serviceList.stream().filter(c -> c.getIdentifier().equals(connection.getDestinationServiceIdentifier().trim()) && c.getStatus() == 1).findAny().orElse(null);
            if (destController == null)
                throw new ServerException("Destination Service Identifier '[" + connection.getDestinationServiceIdentifier() + "]' does not exist.");
            connectionDetailsBeanList.add(ConnectionDetailsBean.builder()
                    .accountId(accountId)
                    .userDetailsId(userId)
                    .sourceId(srcController.getId())
                    .destinationId(destController.getId())
                    .build());
        }
        Map<String, Object> metadata = utilityBean.getMetadata();
        metadata.put(Constants.ACCOUNT, accountBean);
        return UtilityBean.<List<ConnectionDetailsBean>>builder()
                .requestParams(utilityBean.getRequestParams())
                .metadata(metadata)
                .pojoObject(connectionDetailsBeanList)
                .build();
    }

    /**
     * Processes the request to delete connections.
     *
     * @param utilityBean UtilityBean containing the request data.
     * @return A success message.
     * @throws DataProcessingException if there is an error during data processing.
     */
    @Override
    @LogExecutionAnnotation
    @Transactional(rollbackFor = {Exception.class})
    public String process(UtilityBean<List<ConnectionDetailsBean>> utilityBean) throws DataProcessingException {
        log.info("[process] Deleting connections for accountIdentifier: {}", utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        try {
            List<ConnectionDetailsBean> beans = utilityBean.getPojoObject();
            for (ConnectionDetailsBean bean : beans) {
                int res = connectionDetailsDao.deleteConnectionBySrcDest(bean.getSourceId(), bean.getDestinationId(), bean.getAccountId());
                if (res == -1) {
                    String msg = String.format("Unable to remove connection between Source Service id '[%s]' and Destination Service id '[%s]' from connection_details table.", bean.getSourceId(), bean.getDestinationId());
                    log.error("[process] {}", msg);
                    throw new DataProcessingException(msg);
                }
                if (res == 0) {
                    String msg = String.format("No connection found between Source Service id '[%s]' and Destination Service id '[%s]' in connection_details table.", bean.getSourceId(), bean.getDestinationId());
                    log.error("[process] {}", msg);
                    throw new DataProcessingException(msg);
                }
            }
            AccountBean accountBean = (AccountBean) utilityBean.getMetadata().get(Constants.ACCOUNT);
            updateRedisCache(accountBean);
            log.info("[process] Successfully deleted connections for accountIdentifier: {}", accountIdentifier);
            return "Connection(s) deleted successfully.";
        } catch (DataProcessingException e) {
            log.error("[process] DataProcessingException while deleting connections for accountIdentifier [{}]: {}", accountIdentifier, e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("[process] Unexpected error while deleting connections for accountIdentifier [{}]: {}", accountIdentifier, e.getMessage(), e);
            throw new DataProcessingException(e.getMessage());
        }
    }

    /**
     * Updates the Redis cache with the latest connection information.
     *
     * @param accountBean The account for which the cache needs to be updated.
     * @throws DataProcessingException if there is an error while updating the cache.
     */
    private void updateRedisCache(AccountBean accountBean) throws DataProcessingException {
        try {
            String accountIdentifier = accountBean.getIdentifier();
            int accountId = accountBean.getId();
            List<ControllerBean> servicesList = controllerDao.getAllServicesByAccountId(accountId);
            if (servicesList.isEmpty()) {
                log.debug("[updateRedisCache] No service details exist for account. Account identifier: {}", accountIdentifier);
            } else {
                log.info("[updateRedisCache] Fetched {} services for accountIdentifier: {}", servicesList.size(), accountIdentifier);
                List<com.heal.configuration.entities.ConnectionBean> connectionsList = connectionDetailsDao.getServiceConnectionBeansForAccount(accountId);
                log.info("[updateRedisCache] Fetched {} connections for accountId: {}", connectionsList.size(), accountId);
                if (connectionsList.isEmpty()) {
                    log.warn("[updateRedisCache] No connections found for accountId: {}. Redis will be updated with empty data.",
                            accountId);
                }
                connectionRepo.updateServiceConnections(accountIdentifier, connectionsList, servicesList);
                connectionRepo.updateNeighbours(accountBean, connectionsList, servicesList);
                connectionRepo.updateOutbounds(accountIdentifier, connectionsList);
                connectionRepo.updateInbounds(accountIdentifier, connectionsList);
                log.info("[updateRedisCache] Redis cache update complete for accountIdentifier: {}", accountIdentifier);
            }
        } catch (Exception e) {
            log.error("[updateRedisCache] Failed to update connection details for account in redis cache. Account identifier: {}",
                    accountBean.getIdentifier(), e);
            throw new DataProcessingException("Failed to update connection details for account in redis cache.");
        }
    }
}
