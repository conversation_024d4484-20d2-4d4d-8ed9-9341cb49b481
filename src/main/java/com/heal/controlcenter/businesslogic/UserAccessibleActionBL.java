package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.UserAccessibleActions;
import com.heal.controlcenter.beans.UserAttributesBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.ServerValidationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

@Slf4j
@Component
public class UserAccessibleActionBL implements BusinessLogic<String, UserAttributesBean, UserAccessibleActions> {

    private final UserDao userAccessDataDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;

    public UserAccessibleActionBL(UserDao userAccessDataDao, ClientValidationUtils clientValidationUtils, ServerValidationUtils serverValidationUtils) {
        this.userAccessDataDao = userAccessDataDao;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
    }

    @Override
    public UtilityBean<String> clientValidation(String requestBody, String... requestParams) throws ClientException {
        String authKey = requestParams[0];
        clientValidationUtils.authKeyValidation(authKey);
        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(authKey, null);

        return UtilityBean.<String>builder()
                .requestParams(requestParamsMap)
                .build();
    }

    @Override
    public UserAttributesBean serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        String userId = serverValidationUtils.authKeyValidation(authKey);

        UserAttributesBean userAttributesBean;
        try {
            userAttributesBean = userAccessDataDao.getRoleProfileInfoForUserId(userId);
        } catch (HealControlCenterException e) {
            throw new ServerException(e.getMessage());
        }

        if(null == userAttributesBean) {
            log.error("User details unavailable for userId [{}]", userId);
            throw new ServerException("User details unavailable");
        }

        return userAttributesBean;
    }

    @Override
    public UserAccessibleActions process(UserAttributesBean userAttributesBean) throws DataProcessingException {
        List<String> allowedActions;
        try {
            allowedActions = userAccessDataDao.getUserAccessibleActions(userAttributesBean.getAccessProfileId());
        } catch (HealControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }

        return UserAccessibleActions.builder()
                .profileId(userAttributesBean.getAccessProfileId())
                .profile(userAttributesBean.getAccessProfileName())
                .roleId(userAttributesBean.getRoleId())
                .role(userAttributesBean.getRoleName())
                .isActiveDirectory(0)
                .allowedActions(allowedActions)
                .build();
    }
}
