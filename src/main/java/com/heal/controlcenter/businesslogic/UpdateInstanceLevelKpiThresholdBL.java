package com.heal.controlcenter.businesslogic;

import com.google.common.base.Throwables;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.CompInstKpiEntity;
import com.heal.configuration.pojos.KpiViolationConfig;
import com.heal.controlcenter.beans.KpiMaintenanceStatusBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.CompInstanceDao;
import com.heal.controlcenter.dao.mysql.ThresholdDao;
import com.heal.controlcenter.dao.mysql.entity.InstKpiAttrPersistenceSuppressionBean;
import com.heal.controlcenter.dao.mysql.entity.InstanceKpiAttributeThresholdBean;
import com.heal.controlcenter.dao.opensearch.InstanceKpiThresholdRepo;
import com.heal.controlcenter.dao.redis.InstanceRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ActionsEnum;
import com.heal.controlcenter.pojo.CompInstanceVsKpi;
import com.heal.controlcenter.pojo.InstanceKpiThresholdDetails;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UpdateInstanceLevelKpiThresholdBL implements BusinessLogic<InstanceKpiThresholdDetails, UtilityBean<List<InstanceKpiAttributeThresholdBean>>, String> {

    private final ThresholdDao thresholdDao;
    private final InstanceRepo instanceRedisRepo;
    private final CompInstanceDao compInstanceDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;
    private final InstanceLevelKpiThresholdUtil instanceLevelKpiThresholdUtil;
    private final InstanceKpiThresholdRepo instanceKpiThresholdOpensearchRepo;

    public UpdateInstanceLevelKpiThresholdBL(ThresholdDao thresholdDao, InstanceRepo instanceRedisRepo, CompInstanceDao compInstanceDao, ClientValidationUtils clientValidationUtils, ServerValidationUtils serverValidationUtils, InstanceLevelKpiThresholdUtil instanceLevelKpiThresholdUtil, InstanceKpiThresholdRepo instanceKpiThresholdOpensearchRepo) {
        this.thresholdDao = thresholdDao;
        this.instanceRedisRepo = instanceRedisRepo;
        this.compInstanceDao = compInstanceDao;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
        this.instanceLevelKpiThresholdUtil = instanceLevelKpiThresholdUtil;
        this.instanceKpiThresholdOpensearchRepo = instanceKpiThresholdOpensearchRepo;
    }

    @Override
    public UtilityBean<InstanceKpiThresholdDetails> clientValidation(InstanceKpiThresholdDetails requestBody, String... requestParams) throws ClientException {
        try {
            clientValidationUtils.nullRequestBodyCheck(requestBody, UIMessages.REQUEST_BODY_NULL);

            String authKey = requestParams[0];
            clientValidationUtils.nullOrEmptyCheck(authKey, UIMessages.AUTH_KEY_INVALID);

            String accountIdentifier = requestParams[1];
            clientValidationUtils.nullOrEmptyCheck(accountIdentifier, UIMessages.ACCOUNT_IDENTIFIER_INVALID);

            instanceLevelKpiThresholdUtil.validateInstanceKpiThresholdDetails(requestBody, false);

            HashMap<String, String> requestParamsMap = new HashMap<>();
            requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, requestParams[1]);
            requestParamsMap.put(Constants.AUTH_KEY, requestParams[0]);

            return UtilityBean.<InstanceKpiThresholdDetails>builder()
                    .requestParams(requestParamsMap)
                    .pojoObject(requestBody)
                    .build();
        } catch (HealControlCenterException e) {
            throw new ClientException(e.getMessage());
        }
    }

    @Override
    public UtilityBean<List<InstanceKpiAttributeThresholdBean>> serverValidation(UtilityBean<InstanceKpiThresholdDetails> utilityBean) throws ServerException {
        try {
            String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
            String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
            Map<String, Object> metadata = utilityBean.getMetadata();

            String userId = serverValidationUtils.authKeyValidation(authKey);
            Account account = serverValidationUtils.accountValidation(accountIdentifier);
            metadata.put(Constants.ACCOUNT, account);

            InstanceKpiThresholdDetails details = utilityBean.getPojoObject();

            Map<Integer, String> compInstIdToIdentifierMap = instanceLevelKpiThresholdUtil.verifyInstanceIdKpiAndGroupKpi(details, account.getId());
            List<InstanceKpiAttributeThresholdBean> inputThresholdBeans = instanceLevelKpiThresholdUtil.kpiAndCompInstanceValidation(details, compInstIdToIdentifierMap,
                    userId, account.getId(), accountIdentifier, false);

            return UtilityBean.<List<InstanceKpiAttributeThresholdBean>>builder()
                    .pojoObject(inputThresholdBeans)
                    .metadata(metadata)
                    .build();
        } catch (HealControlCenterException e) {
            throw new ServerException(e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String process(UtilityBean<List<InstanceKpiAttributeThresholdBean>> utilityBean) throws DataProcessingException {
        try {
            Account account = (Account) utilityBean.getMetadata().get(Constants.ACCOUNT);
            List<InstanceKpiAttributeThresholdBean> inputThresholdBeans = utilityBean.getPojoObject();
            String accountIdentifier = account.getIdentifier();
            int accountId = account.getId();

            Map<Boolean, List<InstanceKpiAttributeThresholdBean>> partitionBasedOnAddAction = inputThresholdBeans.stream()
                    .collect(Collectors.partitioningBy(c -> c.getActionForUpdate().equals(ActionsEnum.ADD)));

            List<InstanceKpiAttributeThresholdBean> thresholdsToBeUpdated = partitionBasedOnAddAction.get(false);
            Map<Boolean, List<InstanceKpiAttributeThresholdBean>> partitionBasedOnDeleteAction = thresholdsToBeUpdated.stream()
                    .collect(Collectors.partitioningBy(c -> c.getActionForUpdate().equals(ActionsEnum.DELETE)));

            List<InstanceKpiAttributeThresholdBean> newThresholdsToBeAdded = partitionBasedOnAddAction.get(true);
            List<InstanceKpiAttributeThresholdBean> thresholdsToBeDeleted = partitionBasedOnDeleteAction.get(true);
            List<InstanceKpiAttributeThresholdBean> thresholdsToBeModified = partitionBasedOnDeleteAction.get(false);

            insertArrtributeThresholdIntoPerconaAndOS(newThresholdsToBeAdded);
            updateAttributeThresholdsIntoPerconaAndOS(thresholdsToBeModified, accountIdentifier);
            deleteAttributeThresholdsFromPerconaAndOS(thresholdsToBeDeleted);

            instanceLevelKpiThresholdUtil.addInstanceKpiAttributeLevelThresholdsInRedis(newThresholdsToBeAdded);
            updateThresholdsForInstanceKpiInRedis(inputThresholdBeans, accountIdentifier, accountId);
            instanceLevelKpiThresholdUtil.deleteThresholdsForInstanceKpiInRedis(thresholdsToBeDeleted);
        } catch (Exception e) {
            log.error("Unable to update KPI thresholds. Details: ", e);
            throw new DataProcessingException(e.getMessage());
        }

        return "Instance Level Thresholds for attributes updated successfully";
    }

    private void insertArrtributeThresholdIntoPerconaAndOS(List<InstanceKpiAttributeThresholdBean> newThresholdsToBeAdded) throws HealControlCenterException {
        if (newThresholdsToBeAdded == null || newThresholdsToBeAdded.isEmpty()) {
            log.info("No new thresholds to be added");
            return;
        }

        log.info("Inserting instance level KPI attribute thresholds into Percona: total size {}", newThresholdsToBeAdded.size());

        int[] ids = thresholdDao.addInstanceKpiAttributeLevelThresholds(newThresholdsToBeAdded);
        if (ids == null || ids.length == 0) {
            log.error("Error while adding attribute level thresholds");
            throw new HealControlCenterException("Error while adding attribute level thresholds");
        }

        try {
            log.info("Inserting instance level KPI attribute thresholds into OpenSearch: total size {}", newThresholdsToBeAdded.size());
            instanceKpiThresholdOpensearchRepo.addInstanceKpiThresholdInBulk(newThresholdsToBeAdded);
        } catch (HealControlCenterException e) {
            log.error("Error while adding the thresholds to OpenSearch");
            throw new HealControlCenterException("Error while adding the thresholds to OpenSearch");
        }
    }

    private void updateAttributeThresholdsIntoPerconaAndOS(List<InstanceKpiAttributeThresholdBean> inputThresholdBeans, String accountIdentifier) throws HealControlCenterException {
        if (inputThresholdBeans == null || inputThresholdBeans.isEmpty()) {
            log.info("No thresholds to be updated");
            return;
        }

        log.info("Updating instance level thresholds for kpi attributes into persona : total size {}", inputThresholdBeans.size());

        List<Integer> instanceIdsToFetch = inputThresholdBeans.stream()
                .map(InstanceKpiAttributeThresholdBean::getCompInstanceId).distinct().collect(Collectors.toList());

        Map<Integer, List<InstanceKpiAttributeThresholdBean>> existingThresholdBeans = thresholdDao.fetchCompInstanceKpiAttrThresholds(instanceIdsToFetch)
                .parallelStream().collect(Collectors.groupingBy(InstanceKpiAttributeThresholdBean::getKpiId));

        Map<Boolean, List<InstanceKpiAttributeThresholdBean>> severityChangePartitions = inputThresholdBeans.stream()
                .collect(Collectors.partitioningBy(t -> checkForGenAnomalyChange(existingThresholdBeans, t)));

        updateOnlySeverityParameterChange(severityChangePartitions);
        updateMultipleKpiThresholdParameterChanges(accountIdentifier, severityChangePartitions);
    }

    private void deleteAttributeThresholdsFromPerconaAndOS(List<InstanceKpiAttributeThresholdBean> thresholdsToBeDeleted) throws HealControlCenterException {
        if (thresholdsToBeDeleted == null || thresholdsToBeDeleted.isEmpty()) {
            log.info("No thresholds to be deleted");
            return;
        }

        log.info("Deleting instance level KPI attribute thresholds from Percona: total size {}", thresholdsToBeDeleted.size());

        int[] ids = thresholdDao.deleteInstanceKpiAttributeLevelThresholds(thresholdsToBeDeleted);

        if (ids == null || ids.length == 0) {
            log.error("Error while deleting attribute level thresholds");
            throw new HealControlCenterException("Error while deleting attribute level thresholds");
        }

        try {
            instanceKpiThresholdOpensearchRepo.closeExistingThresholds(thresholdsToBeDeleted);
        } catch (HealControlCenterException e) {
            log.error("Error while updating endTime for the thresholds to OpenSearch");
            throw new HealControlCenterException("Error while updating endTime for the thresholds to OpenSearch");
        }
    }

    private boolean checkForGenAnomalyChange(Map<Integer, List<InstanceKpiAttributeThresholdBean>> kpiVsExistingThresholdBeans,
                                             InstanceKpiAttributeThresholdBean inputThresholdBean) {
        List<InstanceKpiAttributeThresholdBean> existingThresholds = kpiVsExistingThresholdBeans.getOrDefault(inputThresholdBean.getKpiId(), new ArrayList<>());
        boolean retVal = existingThresholds.stream()
                .anyMatch(existingThreshold -> existingThreshold.getOperationId() == inputThresholdBean.getOperationId()
                        && existingThreshold.getMaxThreshold().equals(inputThresholdBean.getMaxThreshold())
                        && existingThreshold.getMinThreshold().equals(inputThresholdBean.getMinThreshold())
                        && existingThreshold.getStatus() != inputThresholdBean.getStatus());
        if (retVal) {
            log.info("Change in only Generate Anomaly flag. Hence OpenSearch data will not be updated");
        }
        return retVal;
    }

    public void updateThresholdsForInstanceKpiInRedis(List<InstanceKpiAttributeThresholdBean> inputThresholdBeans, String accountIdentifier, int accountId) {
        //group thresholdBeans by CompInstanceVsKpi object in hashmap of object and list of List<InstanceKpiAttributeThresholdBean>
        if (inputThresholdBeans == null || inputThresholdBeans.isEmpty()) {
            log.info("No thresholds to be updated in Redis");
            return;
        }

        log.info("Updating instance level thresholds for kpi attributes: total size {}", inputThresholdBeans.size());

        Map<CompInstanceVsKpi, List<InstanceKpiAttributeThresholdBean>> compInstanceVsKpiMap = inputThresholdBeans.stream()
                .collect(Collectors.groupingBy(t -> CompInstanceVsKpi.builder().compInstanceId(t.getCompInstanceId()).kpiId(t.getKpiId()).build()));

        List<Integer> kpiIds = inputThresholdBeans.stream().map(InstanceKpiAttributeThresholdBean::getKpiId)
                .filter(kpiId -> Objects.nonNull(kpiId)).distinct().toList();

        List<InstKpiAttrPersistenceSuppressionBean> allExistingConfigBeans = thresholdDao.fetchCompInstanceKpiPerSupValuesByAccountIdAndKpiIds(accountId, kpiIds);
        Map<Pair<Integer, Integer>, List<InstKpiAttrPersistenceSuppressionBean>> existingConfigBeansMap = allExistingConfigBeans.stream()
                .collect(Collectors.groupingBy(threshold -> Pair.of(threshold.getCompInstanceId(), threshold.getKpiId())));

        compInstanceVsKpiMap.forEach((compInstanceVsKpi, groupedThresholdBeans) -> {
            InstanceKpiAttributeThresholdBean thresholdBean = groupedThresholdBeans.get(0);
            List<CompInstKpiEntity> instanceWiseKpis = instanceRedisRepo.getInstanceWiseKpis(thresholdBean.getAccountIdentifier(), thresholdBean.getCompInstanceIdentifier());

            CompInstKpiEntity kpiEntity = instanceWiseKpis.stream().filter(entity -> entity.getId() == thresholdBean.getKpiId() && thresholdBean.getKpiGroupId() == entity.getGroupId()).findAny().orElse(null);
            if (kpiEntity == null) {
                log.error("The kpi details not found for the kpi [{}] and instance [{}]", thresholdBean.getKpiId(), thresholdBean.getCompInstanceId());
                return;
            }

            if (kpiEntity.getKpiViolationConfig() == null || kpiEntity.getKpiViolationConfig().isEmpty()) {
                log.debug("The threshold details for the attribute value: [{}] not found.", kpiEntity.getKpiViolationConfig().keySet());
            }

            Map<String, List<KpiViolationConfig>> violationConfigMap = new HashMap<>();

            groupedThresholdBeans.forEach(t -> {
                KpiViolationConfig violationConfig = instanceLevelKpiThresholdUtil.buildKpiViolationConfig(t, existingConfigBeansMap);
                violationConfigMap.getOrDefault(t.getAttributeValue(), new ArrayList<>()).add(violationConfig);
            });

            Map<String, List<KpiViolationConfig>> existingKpiViolationConfig = new HashMap<>(kpiEntity.getKpiViolationConfig());

            if (existingKpiViolationConfig.isEmpty()) {
                kpiEntity.setKpiViolationConfig(violationConfigMap);
            } else {
                violationConfigMap.forEach((key, value) -> {
                    if (existingKpiViolationConfig.containsKey(key)) {
                        existingKpiViolationConfig.replace(key, value);
                    } else {
                        existingKpiViolationConfig.put(key, value);
                    }
                });
                kpiEntity.setKpiViolationConfig(existingKpiViolationConfig);
            }

            instanceRedisRepo.updateKpiDetailsForKpiId(accountIdentifier, thresholdBean.getCompInstanceIdentifier(), kpiEntity);
            instanceRedisRepo.updateKpiDetailsForKpiIdentifier(accountIdentifier, thresholdBean.getCompInstanceIdentifier(), kpiEntity);
            instanceRedisRepo.updateKpiDetails(accountIdentifier, thresholdBean.getCompInstanceIdentifier(), instanceWiseKpis);
        });
    }

    private void updateMultipleKpiThresholdParameterChanges(String accountIdentifier, Map<Boolean, List<InstanceKpiAttributeThresholdBean>> severityChangePartitions) throws HealControlCenterException {
        List<InstanceKpiAttributeThresholdBean> multipleChangesThresholds = severityChangePartitions.get(false);
        if (!multipleChangesThresholds.isEmpty()) {
            log.info("Updating instance level thresholds with multiple changes: total size {}", multipleChangesThresholds.size());
            int[] ids = thresholdDao.updateInstanceKpiAttributeLevelThresholds(multipleChangesThresholds);
            if (ids == null || ids.length == 0) {
                log.error("Error while updating attribute level thresholds");
                throw new HealControlCenterException("Error while updating attribute level thresholds");
            }

            List<KpiMaintenanceStatusBean> kpiNotificationBean = new ArrayList<>();
            multipleChangesThresholds.forEach(instanceKpiAttributeThresholdBean -> kpiNotificationBean.add(KpiMaintenanceStatusBean.builder()
                    .compInstanceId(instanceKpiAttributeThresholdBean.getCompInstanceId())
                    .kpiId(instanceKpiAttributeThresholdBean.getKpiId())
                    .kpiGroupId(instanceKpiAttributeThresholdBean.getKpiGroupId())
                    .status(instanceKpiAttributeThresholdBean.getStatus())
                    .build()));

            List<KpiMaintenanceStatusBean> nonGroup = kpiNotificationBean.stream().filter(x -> x.getKpiGroupId() == 0).collect(Collectors.toList());
            compInstanceDao.updateNonGroupInstanceKpiAnomaly(nonGroup);

            List<KpiMaintenanceStatusBean> group = kpiNotificationBean.stream().filter(x -> x.getKpiGroupId() != 0).collect(Collectors.toList());
            compInstanceDao.updateGroupInstanceKpiAnomaly(group);

            try {
                log.info("Updating instance level thresholds changes for account {} in OpenSearch: total size {}", accountIdentifier, multipleChangesThresholds.size());
                instanceKpiThresholdOpensearchRepo.updateInstanceKpiThresholdsInBulk(accountIdentifier, multipleChangesThresholds);
            } catch (HealControlCenterException e) {
                log.error("Error while updating the thresholds to OpenSearch");
                throw new HealControlCenterException("Error while updating the thresholds to OpenSearch");
            }
        }
    }

    private void updateOnlySeverityParameterChange(Map<Boolean, List<InstanceKpiAttributeThresholdBean>> severityChangePartitions) throws HealControlCenterException {
        List<InstanceKpiAttributeThresholdBean> severityChangeThresholds = severityChangePartitions.get(true);
        if (!severityChangeThresholds.isEmpty()) {
            log.info("Updating instance level thresholds severity and Generate Anomaly: total size {}", severityChangeThresholds.size());
            int[] ids = thresholdDao.updateInstanceKpiAttributeLevelThresholdSeverityAndGenAnomaly(severityChangeThresholds);

            if (ids == null || ids.length == 0) {
                log.error("Error while updating instance level thresholds severity and Generate Anomaly");
                throw new HealControlCenterException("Error while updating instance level thresholds severity and Generate Anomaly");
            }
        }
    }
}