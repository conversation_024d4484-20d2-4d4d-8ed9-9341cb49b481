package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.*;
import com.heal.controlcenter.beans.ServiceSuppPersistenceConfigurationBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.ServiceConfigurationDao;
import com.heal.controlcenter.dao.redis.ServiceRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ServiceSuppPersistenceConfigPojo;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.DateTimeUtil;
import com.heal.controlcenter.util.ServerValidationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class UpdateServiceAnomalyConfigBL implements BusinessLogic<Map<String, ServiceSuppPersistenceConfigPojo>, UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>>, Object> {

    private final ServiceConfigurationDao serviceConfigurationDao;
    private final ServiceRepo serviceRepo;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;

    public UpdateServiceAnomalyConfigBL(ServiceConfigurationDao serviceConfigurationDao, ServiceRepo serviceRepo,
                                        ClientValidationUtils clientValidationUtils, ServerValidationUtils serverValidationUtils) {
        this.serviceConfigurationDao = serviceConfigurationDao;
        this.serviceRepo = serviceRepo;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
    }

    public UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>> clientValidation(Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetails, String... requestParams) throws ClientException {
        //Entry-Exit logging is handled in LoggingAspect
        String authKey = requestParams[0];
        clientValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = requestParams[1];
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);

        String serviceIdentifier = requestParams[2];
        clientValidationUtils.serviceIdentifierValidation(serviceIdentifier);

        if (serviceConfigDetails.isEmpty()) {
            log.error("List of service configuration details extracted from the request in empty.");
            throw new ClientException("Request object should contain service configuration details.");
        }

        try {
            for (ServiceSuppPersistenceConfigPojo configDetails : serviceConfigDetails.values()) {
                configDetails.validate();
            }
        } catch (HealControlCenterException e) {
            log.error("Error occurred while validating request body. Reason: ", e);
            throw new ClientException(e.getMessage());
        }

        HashMap<String, String> requestParamsMap = new HashMap<>();
        requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        requestParamsMap.put(Constants.SERVICE_IDENTIFIER, serviceIdentifier);
        requestParamsMap.put(Constants.AUTH_KEY, authKey);

        return UtilityBean.<Map<String, ServiceSuppPersistenceConfigPojo>>builder()
                .pojoObject(serviceConfigDetails)
                .requestParams(requestParamsMap)
                .build();
    }

    @Override
    public UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>> serverValidation(UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>> utilityBean) throws ServerException {
        //Entry-Exit logging is handled in LoggingAspect
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        String userId = serverValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        Account account = serverValidationUtils.accountValidation(accountIdentifier);
        int accountId = account.getId();

        com.heal.configuration.entities.UserAccessDetails userAccessDetails = serverValidationUtils.userAccessDetailsValidation(userId, accountIdentifier);
        String serviceIdentifier = utilityBean.getRequestParams().get(Constants.SERVICE_IDENTIFIER);

        Service service = serverValidationUtils.serviceValidation(userId, accountIdentifier, serviceIdentifier, userAccessDetails);

        List<ServiceSuppPersistenceConfigurationBean> serviceBeanList;
        try {
            serviceBeanList = serviceConfigurationDao.getServiceConfiguration(accountId, service.getId());
        } catch (HealControlCenterException e) {
            throw new ServerException(e.getMessage());
        }
        if (serviceBeanList == null || serviceBeanList.isEmpty()) {
            log.error("Service configuration is unavailable for service {} mapped to account {}", serviceIdentifier, accountIdentifier);
            throw new ServerException("Service anomaly configuration is unavailable");
        }

        for (ServiceSuppPersistenceConfigPojo configDetails : utilityBean.getPojoObject().values()) {
            try {
                configDetails.checkIfServiceConfigExists(serviceBeanList);
            } catch (HealControlCenterException e) {
                log.error("Service configuration validation failed for service id {}. Error:", service.getId(), e);
                throw new ServerException(e.getMessage());
            }
        }

        HashMap<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.ACCOUNT, account);
        metadata.put(Constants.SERVICE, service);
        metadata.put(Constants.USER_ID, userId);

        return UtilityBean.<Map<String, ServiceSuppPersistenceConfigPojo>>builder()
                .pojoObject(utilityBean.getPojoObject())
                .requestParams(utilityBean.getRequestParams())
                .metadata(metadata)
                .build();
    }

    @Transactional(rollbackFor = {Exception.class})
    @Override
    public Object process(UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>> utilityBean) throws DataProcessingException {
        //Entry-Exit logging is handled in LoggingAspect
        Map<String, Object> metadata = utilityBean.getMetadata();
        Service service = (Service) metadata.get(Constants.SERVICE);
        int serviceId = service.getId();
        String serviceIdentifier = service.getIdentifier();

        Account account = (Account) metadata.get(Constants.ACCOUNT);
        int accountId = account.getId();
        String accountIdentifier = account.getIdentifier();

        String userId = (String) metadata.get(Constants.USER_ID);

        Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetailsMap = utilityBean.getPojoObject();

        Service serviceConfiguration = serviceRepo.getServiceConfigurationByIdentifier(accountIdentifier, serviceIdentifier);
        if (serviceConfiguration == null) {
            log.error("Service configuration unavailable for service {} and account {}", serviceIdentifier, accountIdentifier);
            throw new DataProcessingException("Service configuration unavailable for service " + serviceIdentifier);
        }

        List<ServiceSuppPersistenceConfigurationBean> list = serviceConfigDetailsMap.values().stream()
                .map(pojo -> convertServiceSuppPeristencePojoToBean(pojo, serviceId, accountId, userId))
                .toList();

        try {
            serviceConfigurationDao.updateServiceConfigFlags(list);
            log.info("Updated service configuration in percona for service 'service_configurations' id {}", serviceId);

            serviceConfigurationDao.updateServiceAnomalyPsConfig(list);
            log.info("Updated service anomaly PS configuration in percona for service 'service_anomaly_ps_configurations' id {}", serviceId);

            ServiceSuppPersistenceConfigPojo gteConfigurationPojo = serviceConfigDetailsMap.get(Constants.OPERATOR_GREATER_THAN);
            ServiceSuppPersistenceConfigurationBean gteConfigurationBean = convertConfigPojoToBean(gteConfigurationPojo);

            ServiceSuppPersistenceConfigPojo ltConfigurationPojo = serviceConfigDetailsMap.get(Constants.OPERATOR_LESS_THAN);
            ServiceSuppPersistenceConfigurationBean ltConfigurationBean = convertConfigPojoToBean(ltConfigurationPojo);

            ServiceConfiguration newAnomalyConfiguration = serviceConfiguration.getServiceConfiguration();
            updateNewAnomalyConfigurations(ltConfigurationBean, gteConfigurationBean, newAnomalyConfiguration);
            serviceRepo.updateServiceConfigurationByServiceIdentifier(accountIdentifier, serviceIdentifier, serviceConfiguration);
            log.info("Updated service anomaly configuration in redis for service id {}", serviceId);

        } catch (HealControlCenterException e) {
            log.error("Persistence & suppression update failed for service id {}", serviceId, e);
            throw new DataProcessingException("Persistence & suppression update failed for service id " + serviceId);
        }
        return null;
    }

    private ServiceSuppPersistenceConfigurationBean convertConfigPojoToBean(ServiceSuppPersistenceConfigPojo serviceConfigDetailsGte) {
        ServiceSuppPersistenceConfigurationBean configurationBean = new ServiceSuppPersistenceConfigurationBean();
        configurationBean.setLowPersistence(serviceConfigDetailsGte.getLowPersistence());
        configurationBean.setLowSuppression(serviceConfigDetailsGte.getLowSuppression());
        configurationBean.setHighPersistence(serviceConfigDetailsGte.getHighPersistence());
        configurationBean.setHighSuppression(serviceConfigDetailsGte.getHighSuppression());
        configurationBean.setMediumPersistence(serviceConfigDetailsGte.getMediumPersistence());
        configurationBean.setMediumSuppression(serviceConfigDetailsGte.getMediumSuppression());
        configurationBean.setLowEnable(serviceConfigDetailsGte.isLowEnable());
        configurationBean.setHighEnable(serviceConfigDetailsGte.isHighEnable());
        configurationBean.setMediumEnable(serviceConfigDetailsGte.isMediumEnable());
        configurationBean.setClosingWindow(serviceConfigDetailsGte.getClosingWindow());
        configurationBean.setMaxDataBreaks(serviceConfigDetailsGte.getMaxDataBreaks());
        return configurationBean;
    }

    private void updateNewAnomalyConfigurations(ServiceSuppPersistenceConfigurationBean lt, ServiceSuppPersistenceConfigurationBean gte,  ServiceConfiguration newLt) {
        newLt.setLowEnable(lt.isLowEnable());
        newLt.setMediumEnable(lt.isMediumEnable());
        newLt.setHighEnable(lt.isHighEnable());
        newLt.setClosingWindow(lt.getClosingWindow());
        newLt.setMaxDataBreaks(lt.getMaxDataBreaks());

        PersistenceSuppressionConfiguration ltPersistenceSuppressionConfiguration = PersistenceSuppressionConfiguration.builder()
                .startCollectionInterval(lt.getStartCollectionInterval())
                .endCollectionInterval(lt.getEndCollectionInterval())
                .lowPersistence(lt.getLowPersistence())
                .lowSuppression(lt.getLowSuppression())
                .highPersistence(lt.getHighPersistence())
                .highSuppression(lt.getHighSuppression())
                .mediumPersistence(lt.getMediumPersistence())
                .mediumSuppression(lt.getMediumSuppression())
                .createdTime(lt.getCreatedTime())
                .updatedTime(lt.getUpdatedTime())
                .userDetailsId(lt.getUserDetailsId())
                .build();

        PersistenceSuppressionConfiguration gtePersistenceSuppressionConfiguration = PersistenceSuppressionConfiguration.builder()
                .startCollectionInterval(gte.getStartCollectionInterval())
                .endCollectionInterval(gte.getEndCollectionInterval())
                .lowPersistence(gte.getLowPersistence())
                .lowSuppression(gte.getLowSuppression())
                .highPersistence(gte.getHighPersistence())
                .highSuppression(gte.getHighSuppression())
                .mediumPersistence(gte.getMediumPersistence())
                .mediumSuppression(gte.getMediumSuppression())
                .createdTime(gte.getCreatedTime())
                .updatedTime(gte.getUpdatedTime())
                .userDetailsId(gte.getUserDetailsId())
                .build();
        newLt.setPersistenceSuppressionConfigurations(List.of(ltPersistenceSuppressionConfiguration, gtePersistenceSuppressionConfiguration));
    }

    private ServiceSuppPersistenceConfigurationBean convertServiceSuppPeristencePojoToBean(ServiceSuppPersistenceConfigPojo s, int serviceId, int accountId, String userId) {
        return ServiceSuppPersistenceConfigurationBean.builder()
                .id(s.getServiceConfigId())
                .serviceId(serviceId)
                .accountId(accountId)
                .userDetailsId(userId)
                .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .lowPersistence(s.getLowPersistence())
                .mediumPersistence(s.getMediumPersistence())
                .highPersistence(s.getHighPersistence())
                .lowSuppression(s.getLowSuppression())
                .mediumSuppression(s.getMediumSuppression())
                .highSuppression(s.getHighSuppression())
                .highEnable(s.isHighEnable())
                .lowEnable(s.isLowEnable())
                .mediumEnable(s.isMediumEnable())
                .closingWindow(s.getClosingWindow())
                .maxDataBreaks(s.getMaxDataBreaks())
                .build();
    }
}
