package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.ViewTypes;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.redis.MasterDataRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ViewTypeResponse;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.UIMessages;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Business logic class for fetching ViewTypes by typeName from Redis.
 */
@Slf4j
@Component
public class GetViewTypesByNameBL implements BusinessLogic<String, UtilityBean<String>, List<ViewTypeResponse>> {

    private final MasterDataRepo masterDataRepo;
    private final ClientValidationUtils clientValidationUtils;

    public GetViewTypesByNameBL(MasterDataRepo masterDataRepo, ClientValidationUtils clientValidationUtils) {
        this.masterDataRepo = masterDataRepo;
        this.clientValidationUtils = clientValidationUtils;
    }

    /**
     * Validates the client-side input for fetching ViewTypes.
     * Checks typeName format and required fields in the request.
     *
     * @param requestBody   typeName from the request payload.
     * @param requestParams Optional request parameters.
     * @return UtilityBean containing request data and metadata.
     * @throws ClientException if validation fails.
     */
    @Override
    public UtilityBean<String> clientValidation(String requestBody, String... requestParams) throws ClientException {
        clientValidationUtils.nullOrEmptyCheck(requestBody, UIMessages.TYPE_NAME_INVALID);
        return UtilityBean.<String>builder()
                .pojoObject(requestBody)
                .metadata(new java.util.HashMap<>())
                .build();
    }

    /**
     * Validates server-side constraints for fetching ViewTypes.
     *
     * @param utilityBean UtilityBean containing request parameters and typeName.
     * @return UtilityBean enriched with metadata.
     * @throws ServerException if any server-side validation fails.
     */
    @Override
    public UtilityBean<String> serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        // No server-side validation required for now
        return utilityBean;
    }

    /**
     * Fetches and processes ViewTypes from Redis for a given type name.
     * <p>
     * This method performs the following steps:
     * <ol>
     *   <li>Logs the initiation of the fetch operation for the provided type name.</li>
     *   <li>Retrieves the list of ViewTypes from the data repository using the type name.</li>
     *   <li>Maps the retrieved ViewTypes to ViewTypeResponse objects.</li>
     *   <li>Checks if the result is empty and logs a warning; throws DataProcessingException if no records are found.</li>
     *   <li>Logs the successful fetch operation with the count of results.</li>
     *   <li>Handles and logs any exceptions, rethrowing them as DataProcessingException.</li>
     * </ol>
     * <p>
     * This method is annotated for AOP logging and transactional safety.
     *
     * @param utilityBean Validated UtilityBean containing the type name and metadata.
     * @return List of ViewTypeResponse objects matching the type name.
     * @throws DataProcessingException if no records are found or any error occurs during processing.
     */
    @LogExecutionAnnotation
    @Transactional(rollbackFor = {Exception.class})
    @Override
    public List<ViewTypeResponse> process(UtilityBean<String> utilityBean) throws DataProcessingException {
        String typeName = utilityBean.getPojoObject();
        log.info("[Process] Started fetching ViewTypes for typeName: {}", typeName);
        try {
            List<ViewTypes> filteredTypes = masterDataRepo.getViewTypesByName(typeName);
            List<ViewTypeResponse> responseList = filteredTypes.stream()
                    .map(vt -> new ViewTypeResponse(vt.getSubTypeId(), vt.getSubTypeName()))
                    .collect(java.util.stream.Collectors.toList());
            if (responseList.isEmpty()) {
                log.warn("[Process] No records found for typeName '{}'.", typeName);
                throw new DataProcessingException(String.format("%s: %s", UIMessages.NO_RECORDS_FOR_TYPE_NAME, typeName));
            }
            log.info("[Process] Successfully fetched {} ViewTypeResponse(s) for typeName '{}'.", responseList.size(), typeName);
            return responseList;
        } catch (Exception e) {
            log.error("[Process] Exception while fetching ViewTypeResponse for typeName '{}': {}", typeName, e.getMessage(), e);
            throw new DataProcessingException("Error while fetching ViewTypeResponse: " + e.getMessage());
        }
    }
}
