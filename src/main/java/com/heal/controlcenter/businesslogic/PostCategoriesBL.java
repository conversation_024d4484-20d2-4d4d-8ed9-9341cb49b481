package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.Category;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import com.heal.controlcenter.beans.CategoryDetailBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.CategoryDao;
import com.heal.controlcenter.dao.redis.CategoryRepo;
import com.heal.controlcenter.enums.CategoryType;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.CategoryDetails;
import com.heal.controlcenter.pojo.IdPojo;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.DateTimeUtil;
import com.heal.controlcenter.util.ServerValidationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Business logic for adding categories to an account.
 * Handles validation, persistence, and Redis cache update.
 */
@Slf4j
@Service
public class PostCategoriesBL implements BusinessLogic<List<CategoryDetails>, UtilityBean<List<CategoryDetailBean>>, List<IdPojo>> {

    private final CategoryDao categoryDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;
    private final CategoryRepo categoryRepo;

    public PostCategoriesBL(CategoryDao categoryDao, ClientValidationUtils clientValidationUtils,
                            ServerValidationUtils serverValidationUtils, CategoryRepo categoryRepo) {
        this.categoryDao = categoryDao;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
        this.categoryRepo = categoryRepo;
    }

    /**
     * Validates the client request for adding categories.
     * Checks for empty request, account identifier validity, and each category's fields.
     * Logs errors for invalid fields.
     *
     * @param categories    List of category details from the client
     * @param requestParams Additional request parameters (e.g., account identifier)
     * @return UtilityBean containing validated request data
     * @throws ClientException if validation fails
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<List<CategoryDetails>> clientValidation(List<CategoryDetails> categories, String... requestParams) throws ClientException {
        log.debug("Starting clientValidation for categories: {} and requestParams: {}", categories, requestParams);
        if (categories == null || categories.isEmpty()) {
            log.error("Request body is empty in clientValidation.");
            throw new ClientException("Request body cannot be empty");
        }
        String accountIdentifier = requestParams[0];
        log.debug("Validating accountIdentifier: {}", accountIdentifier);
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);

        // Validate each CategoryDetails object and collect errors
        Map<String, String> error = new HashMap<>();
        for (CategoryDetails category : categories) {
            log.debug("Validating category: {}", category);
            Map<String, String> validationErrors = category.validate();
            if (!validationErrors.isEmpty()) {
                log.error("Validation errors for category {}: {}", category.getName(), validationErrors);
                error.putAll(validationErrors);
            }
        }
        if (!error.isEmpty()) {
            String err = error.toString();
            log.error("Category validation errors: {}", err);
            throw new ClientException(err);
        }
        log.debug("Client validation successful for accountIdentifier: {}", accountIdentifier);
        Map<String, String> requestParamsMap = new HashMap<>();
        requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        return UtilityBean.<List<CategoryDetails>>builder()
                .requestParams(requestParamsMap)
                .pojoObject(categories)
                .metadata(new HashMap<>())
                .build();
    }

    /**
     * Performs server-side validation and prepares CategoryDetailBean objects.
     * Checks for duplicate category identifier and name in the database.
     * Converts CategoryDetails to CategoryDetailBean for persistence.
     *
     * @param utilityBean UtilityBean containing client-validated data
     * @return UtilityBean with prepared CategoryDetailBean list
     * @throws ServerException if validation fails or duplicates are found
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<List<CategoryDetailBean>> serverValidation(UtilityBean<List<CategoryDetails>> utilityBean) throws ServerException {
        log.debug("Starting serverValidation for utilityBean: {}", utilityBean);
        String createdTime = Objects.requireNonNull(DateTimeUtil.getCurrentTimestampInGMT()).toString();
        String userId = String.valueOf(utilityBean.getMetadata().get(Constants.USER_ID_KEY));
        List<CategoryDetails> categories = utilityBean.getPojoObject();

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        log.debug("Validating accountIdentifier on server: {}", accountIdentifier);
        Account account = serverValidationUtils.accountValidation(accountIdentifier);
        int accountId = account.getId();

        ArrayList<CategoryDetailBean> beans = new ArrayList<>();
        for (CategoryDetails categoryDetails : categories) {
            log.debug("Checking existence for category identifier: {} and name: {}", categoryDetails.getIdentifier(), categoryDetails.getName());
            try {
                if (categoryDao.existsByIdentifier(accountId, categoryDetails.getIdentifier())) {
                    log.error("Category with identifier already exists: {}", categoryDetails.getIdentifier());
                    throw new ServerException("Category with identifier already exists: " + categoryDetails.getIdentifier());
                }
            } catch (HealControlCenterException e) {
                throw new ServerException(e.getMessage());
            }
            try {
                if (categoryDao.existsByName(accountId, categoryDetails.getName())) {
                    log.error("Category with name already exists: {}", categoryDetails.getName());
                    throw new ServerException("Category with name already exists: " + categoryDetails.getName());
                }
            } catch (HealControlCenterException e) {
                throw new ServerException(e.getMessage());
            }
            log.debug("Building CategoryDetailBean for category: {}", categoryDetails.getName());
            beans.add(CategoryDetailBean.builder()
                    .name(categoryDetails.getName())
                    .identifier(categoryDetails.getIdentifier())
                    .accountId(accountId)
                    .description(categoryDetails.getDescription())
                    .userDetailsId(userId)
                    .createdTime(createdTime)
                    .updatedTime(createdTime)
                    .status(Constants.STATUS_ACTIVE)
                    .isCustom((categoryDetails.getType() != null && categoryDetails.getType().trim().equalsIgnoreCase(Constants.CUSTOM)) ? Constants.STATUS_ACTIVE : Constants.STATUS_INACTIVE)
                    .isInformative((categoryDetails.getSubType() != null && categoryDetails.getSubType().trim()
                            .equalsIgnoreCase(CategoryType.INFO.getType())) ? Constants.STATUS_ACTIVE : Constants.STATUS_INACTIVE)
                    .isWorkLoad((categoryDetails.getSubType() != null && categoryDetails.getSubType().trim()
                            .equalsIgnoreCase(CategoryType.WORKLOAD.getType())) ? Constants.STATUS_ACTIVE : Constants.STATUS_INACTIVE)
                    .accountIdentifier(accountIdentifier)
                    .build());
        }
        log.debug("Server validation successful for accountIdentifier: {}. Beans prepared: {}", accountIdentifier, beans.size());
        return UtilityBean.<List<CategoryDetailBean>>builder()
                .pojoObject(beans)
                .requestParams(utilityBean.getRequestParams())
                .metadata(new HashMap<>(utilityBean.getMetadata()))
                .build();
    }

    /**
     * Processes the validated categories, inserts them into the database, and updates Redis cache.
     * Logs errors if insertion or Redis update fails.
     *
     * @param utilityBean UtilityBean containing validated CategoryDetailBean list
     * @return List of IdPojo with inserted category details
     * @throws DataProcessingException if insertion fails
     */
    @Override
    @LogExecutionAnnotation
    public List<IdPojo> process(UtilityBean<List<CategoryDetailBean>> utilityBean) throws DataProcessingException {
        log.debug("Starting process for utilityBean: {}", utilityBean);
        List<CategoryDetailBean> beans = utilityBean.getPojoObject();
        ArrayList<IdPojo> result = new ArrayList<>();
        for (CategoryDetailBean bean : beans) {
            log.debug("Inserting category: {} with identifier: {}", bean.getName(), bean.getIdentifier());
            try {
                int id = categoryDao.insertCategory(bean);
                log.debug("Inserted category: {} with id: {}", bean.getName(), id);
                result.add(IdPojo.builder()
                        .id(id)
                        .name(bean.getName())
                        .identifier(bean.getIdentifier())
                        .build());
                // Add to Redis
                log.debug("Adding category to Redis: {} with id: {}", bean.getName(), id);
                addCategoryInRedis(bean, id);
            } catch (Exception e) {
                log.error("Failed to insert category: {}. Reason: {}", bean.getName(), e.getMessage(), e);
                throw new DataProcessingException("Failed to insert category: " + bean.getName() + ", reason: " + e.getMessage());
            }
        }
        log.debug("Process completed. Categories inserted: {}", result.size());
        return result;
    }

    /**
     * Adds the given category to Redis cache for the account.
     * Logs error if Redis update fails.
     *
     * @param categoryDetailBean Category details bean
     * @param id                 Inserted category ID
     */
    public void addCategoryInRedis(CategoryDetailBean categoryDetailBean, int id) throws DataProcessingException {
        log.debug("Adding category to Redis for accountIdentifier: {} with id: {}", categoryDetailBean.getAccountIdentifier(), id);
        List<Category> existingCategoryDetails;
        try {
            existingCategoryDetails = categoryRepo.getCategoryDetails(categoryDetailBean.getAccountIdentifier());
            log.debug("Fetched existing categories from Redis for accountIdentifier: {}. Count: {}", categoryDetailBean.getAccountIdentifier(), existingCategoryDetails.size());
        } catch (Exception e) {
            log.error("Failed to fetch existing categories from Redis for accountIdentifier: {}. Reason: {}", categoryDetailBean.getAccountIdentifier(), e.getMessage(), e);
            existingCategoryDetails = new ArrayList<>();
        }
        Category newCategory = Category.builder()
                .id(id)
                .name(categoryDetailBean.getName())
                .identifier(categoryDetailBean.getIdentifier())
                .createdTime(categoryDetailBean.getCreatedTime())
                .updatedTime(categoryDetailBean.getUpdatedTime())
                .lastModifiedBy(categoryDetailBean.getUserDetailsId())
                .status(categoryDetailBean.getStatus())
                .workload(categoryDetailBean.getIsWorkLoad())
                .informative(categoryDetailBean.getIsInformative())
                .custom(categoryDetailBean.getIsCustom())
                .description(categoryDetailBean.getDescription())
                .build();
        log.debug("Adding new category to Redis list: {}", newCategory);
        existingCategoryDetails.add(newCategory);
        try {
            categoryRepo.updateCategoryDetails(categoryDetailBean.getAccountIdentifier(), existingCategoryDetails);
            categoryRepo.updateCategory(categoryDetailBean.getAccountIdentifier(), newCategory);
            log.debug("Successfully updated Redis for category: {}", categoryDetailBean.getIdentifier());
        } catch (Exception e) {
            log.error("Failed to update Redis for category: {}. Reason: {}", categoryDetailBean.getIdentifier(), e.getMessage(), e);
            throw new DataProcessingException("Failed to update Redis for category: " + categoryDetailBean.getIdentifier() + ". Reason: " + e.getMessage());
        }
    }
}
