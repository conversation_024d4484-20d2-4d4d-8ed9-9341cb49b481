package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import com.heal.controlcenter.beans.ServiceBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.AccountServiceDao;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.dao.redis.ApplicationRepo;
import com.heal.controlcenter.dao.redis.ServiceRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ServiceDeleteRequestPojo;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.ServerValidationUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class DeleteAccountServicesBL implements BusinessLogic<ServiceDeleteRequestPojo, UtilityBean<List<ServiceBean>>, String> {

    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;
    private final AccountServiceDao accountServiceDao;
    private final ControllerDao controllerDao;
    private final ServiceRepo serviceRepo;
    private final ApplicationRepo applicationRepo;

    public DeleteAccountServicesBL(ClientValidationUtils clientValidationUtils, ServerValidationUtils serverValidationUtils,
                                   AccountServiceDao accountServiceDao, ControllerDao controllerDao, ServiceRepo serviceRepo,
                                   ApplicationRepo applicationRepo) {
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
        this.accountServiceDao = accountServiceDao;
        this.controllerDao = controllerDao;
        this.serviceRepo = serviceRepo;
        this.applicationRepo = applicationRepo;
    }

    /**
     * Performs client-side validation for service deletion requests.
     * Ensures the account identifier is valid and the list of service identifiers is not empty.
     *
     * @param requestBody   The {@link ServiceDeleteRequestPojo} containing service identifiers and deletion type.
     * @param requestParams An array of request parameters, where the first element is the account identifier.
     * @return A {@link UtilityBean} containing the validated request body and metadata.
     * @throws ClientException If validation fails (e.g., empty service identifiers list).
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<ServiceDeleteRequestPojo> clientValidation(ServiceDeleteRequestPojo requestBody, String... requestParams) throws ClientException {
        String accountIdentifier = requestParams[0];
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);

        if (requestBody == null || CollectionUtils.isEmpty(requestBody.getServiceIdentifiers())) {
            throw new ClientException("Service identifiers list cannot be empty.");
        }
        // No validation needed for isHardDelete as it's a boolean and defaults to false if not provided.

        Map<String, String> requestParamsMap = CommonUtils.buildRequestParams(null, accountIdentifier);
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.HARD_DELETE, requestBody.isHardDelete());

        log.debug("[clientValidation] Validation successful for accountIdentifier: {}", accountIdentifier);
        log.info("[clientValidation] isHardDelete received: {}", requestBody.isHardDelete());
        return UtilityBean.<ServiceDeleteRequestPojo>builder()
                .requestParams(requestParamsMap)
                .pojoObject(requestBody)
                .metadata(metadata)
                .build();
    }

    /**
     * Performs server-side validation for service deletion requests.
     * Validates the account and retrieves the corresponding ServiceBean objects for the provided identifiers.
     *
     * @param utilityBean The {@link UtilityBean} containing the request body and metadata from client validation.
     * @return A {@link UtilityBean} containing a list of validated {@link ServiceBean} objects and updated metadata.
     * @throws ServerException If validation fails (e.g., account not found, service not found).
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<List<ServiceBean>> serverValidation(UtilityBean<ServiceDeleteRequestPojo> utilityBean) throws ServerException {
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        boolean hardDelete = (boolean) utilityBean.getMetadata().get(Constants.HARD_DELETE);
        log.info("[serverValidation] isHardDelete from metadata: {}", hardDelete);
        List<String> serviceIdentifiers = utilityBean.getPojoObject().getServiceIdentifiers();

        Account account = serverValidationUtils.accountValidation(accountIdentifier);
        int accountId = account.getId();

        List<ServiceBean> serviceBeansToDelete = new ArrayList<>();
        for (String serviceIdentifier : serviceIdentifiers) {
            ServiceBean serviceBean = null;
            try {
                serviceBean = controllerDao.getServiceBeanByIdentifierAndAccount(serviceIdentifier, accountId);
            } catch (HealControlCenterException e) {
                throw new ServerException(e.getMessage());
            }
            if (serviceBean == null) {
                throw new ServerException("Service with identifier '" + serviceIdentifier + "' not found for account '" + accountIdentifier + "'.");
            }
            serviceBeansToDelete.add(serviceBean);
        }

        Map<String, Object> metadata = utilityBean.getMetadata();
        metadata.put(Constants.USER_ID_KEY, utilityBean.getMetadata().get(Constants.USER_ID_KEY));
        metadata.put(Constants.HARD_DELETE, hardDelete);
        metadata.put(Constants.ACCOUNT_ID, accountId);

        return UtilityBean.<List<ServiceBean>>builder()
                .pojoObject(serviceBeansToDelete)
                .requestParams(utilityBean.getRequestParams())
                .metadata(metadata)
                .build();
    }

    /**
     * Processes the deletion of services based on the deletion type (hard or soft).
     * Iterates through the list of services and performs the appropriate deletion operation.
     *
     * @param utilityBean The {@link UtilityBean} containing the list of {@link ServiceBean} objects and metadata.
     * @return A success message string.
     * @throws DataProcessingException If an error occurs during the deletion process.
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogExecutionAnnotation
    public String process(UtilityBean<List<ServiceBean>> utilityBean) throws DataProcessingException {
        List<ServiceBean> serviceBeans = utilityBean.getPojoObject();
        boolean hardDelete = (boolean) utilityBean.getMetadata().get(Constants.HARD_DELETE);
        int accountId = (int) utilityBean.getMetadata().get(Constants.ACCOUNT_ID);
        String userId = (String) utilityBean.getMetadata().get(Constants.USER_ID_KEY);
        log.info("[process] isHardDelete before processing: {}", hardDelete);

        try {
            for (ServiceBean serviceBean : serviceBeans) {
                if (hardDelete) {
                    log.info("Performing hard delete for service [id: {}]", serviceBean.getId());
                    hardDeleteService(serviceBean, accountId);
                } else {
                    log.info("Performing soft delete for service [id: {}]", serviceBean.getId());
                    softDeleteService(serviceBean, accountId, userId);
                }
                // Invalidate Redis cache after DB operations for each service
                invalidateRedisCache(serviceBean, accountId);
            }
            return "Service(s) deleted successfully.";
        } catch (HealControlCenterException e) {
            log.error("Error during service deletion process. Details: ", e);
            throw new DataProcessingException("Failed to delete service: " + e.getMessage());
        }
    }

    /**
     * Performs a soft delete operation for a given service.
     * This involves updating the status of the service in the 'controller' and 'service_aliases' tables to 0 (inactive).
     *
     * @param serviceBean The {@link ServiceBean} object representing the service to be soft deleted.
     * @param accountId   The ID of the account to which the service belongs.
     * @param userId      The ID of the user performing the operation.
     * @throws HealControlCenterException If an error occurs during database update.
     */
    private void softDeleteService(ServiceBean serviceBean, int accountId, String userId) throws HealControlCenterException {
        // Update status in 'controller' table
        controllerDao.updateControllerStatus(serviceBean.getId(), 0, userId);
        log.info("Soft deleted service in controller table [id: {}]", serviceBean.getId());

        // Update status in 'service_aliases' table if exists
        accountServiceDao.updateServiceAliasStatus(serviceBean.getIdentifier(), 0, userId);
        log.info("Soft deleted service in service_aliases table [identifier: {}]", serviceBean.getIdentifier());

        // No status column in service_group_mapping, tag_mapping and service_anomaly_ps_configurations, so no soft delete action needed for them.
    }

    /**
     * Performs a hard delete operation for a given service.
     * This involves permanently deleting service-related entries from multiple database tables.
     *
     * @param serviceBean The {@link ServiceBean} object representing the service to be hard deleted.
     * @param accountId   The ID of the account to which the service belongs.
     * @throws HealControlCenterException If an error occurs during database deletion.
     */
    private void hardDeleteService(ServiceBean serviceBean, int accountId) throws HealControlCenterException {
        // Delete from 'service_group_mapping' table
        accountServiceDao.hardDeleteServiceGroupMappingByServiceId(serviceBean.getId());
        log.info("Hard deleted service from service_group_mapping table [service_id: {}]", serviceBean.getId());

        // Delete from 'tag_mapping' table
        log.info("Attempting to hard delete from tag_mapping for entityId: {} and objectRefTable: {}", serviceBean.getId(), Constants.CONTROLLER);
        accountServiceDao.deleteTagMappingByEntityId(serviceBean.getId(), Constants.CONTROLLER);
        log.info("Hard deleted service from tag_mapping table [entity_id: {}]", serviceBean.getId());

        // Delete from 'service_aliases' table
        accountServiceDao.deleteServiceAliasByIdentifier(serviceBean.getIdentifier());
        log.info("Hard deleted service from service_aliases table [identifier: {}]", serviceBean.getIdentifier());

        // Delete from 'service_anomaly_ps_configurations' table
        accountServiceDao.deleteAnomalySuppressionByServiceId(serviceBean.getId());
        log.info("Hard deleted service from service_anomaly_ps_configurations table [service_id: {}]", serviceBean.getId());

        // Delete from 'controller' table
        controllerDao.deleteController(serviceBean.getId());
        log.info("Hard deleted service from controller table [id: {}]", serviceBean.getId());

    }

    /**
     * Invalidates the Redis cache entries related to the deleted service.
     * This includes removing the service's configuration and rules, and updating application mappings.
     * Throws a RuntimeException if any error occurs during cache invalidation.
     *
     * @param serviceBean The {@link ServiceBean} object representing the deleted service.
     * @param accountId   The ID of the account to which the service belongs.
     */
    private void invalidateRedisCache(ServiceBean serviceBean, int accountId) throws HealControlCenterException {
        try {
            // Remove service from Redis cache
            serviceRepo.deleteService(serviceBean.getAccountIdentifier(), serviceBean.getIdentifier());
            log.info("Removed service from Redis cache [identifier: {}]", serviceBean.getIdentifier());

            // Remove service from application mappings in Redis
            applicationRepo.removeServiceFromAllApplications(serviceBean.getAccountIdentifier(), serviceBean.getId());
            log.info("Removed service from application mappings in Redis [service_id: {}]", serviceBean.getId());

        } catch (Exception e) {
            log.error("Error invalidating Redis cache for service [id: {}]. Details: ", serviceBean.getId(), e);
            throw new HealControlCenterException("Error invalidating Redis cache for service [id: " + serviceBean.getId() + "]");
        }
    }
}
