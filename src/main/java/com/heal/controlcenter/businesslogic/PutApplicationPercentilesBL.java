package com.heal.controlcenter.businesslogic;

import com.appnomic.appsone.common.util.ConfProperties;
import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import com.heal.controlcenter.beans.ApplicationPercentilesBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.ApplicationNotifAndPercentileDao;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ApplicationPercentilePojo;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.DateTimeUtil;
import com.heal.controlcenter.util.ServerValidationUtils;
import com.heal.controlcenter.util.UIMessages;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PutApplicationPercentilesBL implements BusinessLogic<List<ApplicationPercentilePojo>, UtilityBean<List<ApplicationPercentilePojo>>, Void> {

    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;
    private final ControllerDao controllerDao;
    private final ApplicationNotifAndPercentileDao applicationNotifAndPercentileDao;

    public PutApplicationPercentilesBL(ClientValidationUtils clientValidationUtils,
                                       ServerValidationUtils serverValidationUtils,
                                       ControllerDao controllerDao,
                                       ApplicationNotifAndPercentileDao applicationNotifAndPercentileDao) {
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
        this.controllerDao = controllerDao;
        this.applicationNotifAndPercentileDao = applicationNotifAndPercentileDao;
    }

    /**
     * Performs client-side validation for updating application percentiles.
     * Validates the request body, account identifier, and application identifier.
     *
     * @param percentilePojos The {@link ApplicationPercentilePojo} to be validated.
     * @param requestParams  Variable arguments: accountIdentifier, applicationId.
     * @return A UtilityBean containing the validated percentilePojo and request parameters.
     * @throws ClientException if validation fails.
     */
    @LogExecutionAnnotation
    @Override
    public UtilityBean<List<ApplicationPercentilePojo>> clientValidation(List<ApplicationPercentilePojo> percentilePojos, String... requestParams) throws ClientException {
        log.debug("[clientValidation] Start - percentilePojos: {}, requestParams: {}", percentilePojos, Arrays.toString(requestParams));
        if (percentilePojos == null) {
            log.error("[clientValidation] Request body is null");
            throw new ClientException(UIMessages.REQUEST_BODY_NULL);
        }
        if (requestParams.length < 2) {
            log.error("[clientValidation] Insufficient parameters. Expected accountIdentifier and applicationId.");
            throw new ClientException(UIMessages.INSUFFICIENT_PARAMETERS);
        }

        String accountIdentifier = requestParams[0];
        String applicationId = requestParams[1];

        clientValidationUtils.accountIdentifierValidation(accountIdentifier);
        clientValidationUtils.applicationIdValidation(applicationId);

        Map<String, String> errors = geErrorsMap(percentilePojos, applicationId);

        if (!errors.isEmpty()) {
            log.error("[clientValidation] Application percentile validation failed: {}", errors);
            throw new ClientException(errors.toString());
        }

        Map<String, String> requestParamsMap = new HashMap<>();
        requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        requestParamsMap.put(Constants.APPLICATION_ID, applicationId);

        log.info("[clientValidation] Client validation successful for accountIdentifier: {} and applicationId: {}", accountIdentifier, applicationId);
        return UtilityBean.<List<ApplicationPercentilePojo>>builder()
                .requestParams(requestParamsMap)
                .pojoObject(percentilePojos)
                .metadata(new HashMap<>())
                .build();
    }

    @NotNull
    private static Map<String, String> geErrorsMap(List<ApplicationPercentilePojo> percentilePojos, String applicationId) {
        Map<String, String> errors = new HashMap<>();
        for (int i = 0; i < percentilePojos.size(); i++) {
            ApplicationPercentilePojo percentilePojo = percentilePojos.get(i);
            if (percentilePojo == null) {
                errors.put("percentiles[" + i + "]", "Percentile object is null");
                continue;
            }
            if (percentilePojo.getApplicationId() != Integer.parseInt(applicationId)) {
                errors.put("percentiles[" + i + "].applicationId", "Application ID in path variable [" + applicationId + "] does not match application ID in request body [" + percentilePojo.getApplicationId() + "]");
            }
            // Add any other necessary client-side validations for individual percentilePojo fields here
        }
        return errors;
    }

    /**
     * Performs server-side validation for updating application percentiles.
     * Validates the account and application existence, and the existence of the percentile record.
     *
     * @param utilityBean UtilityBean containing percentilePojo, accountIdentifier, and applicationId.
     * @return UtilityBean with validated account and application IDs in metadata.
     * @throws ServerException if validation fails.
     */
    @LogExecutionAnnotation
    @Override
    public UtilityBean<List<ApplicationPercentilePojo>> serverValidation(UtilityBean<List<ApplicationPercentilePojo>> utilityBean) throws ServerException {
        log.debug("[serverValidation] Start - utilityBean: {}", utilityBean);
        List<ApplicationPercentilePojo> percentilePojos = utilityBean.getPojoObject();
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        String applicationId = utilityBean.getRequestParams().get(Constants.APPLICATION_ID);
        String userId = String.valueOf(utilityBean.getMetadata().get(Constants.USER_ID_KEY));

        Account account = serverValidationUtils.accountValidation(accountIdentifier);
        int accountId = account.getId();
        try {
            if (!controllerDao.isApplicationExists(Integer.parseInt(applicationId), accountId)) {
                log.error("[serverValidation] Application with id [{}] does not exist for account [{}]", applicationId, accountIdentifier);
                throw new ServerException(UIMessages.APPLICATION_NOT_FOUND);
            }

            List<ApplicationPercentilesBean> existingPercentiles = applicationNotifAndPercentileDao.getPercentilesForApplication(Integer.parseInt(applicationId));
            Map<Integer, ApplicationPercentilesBean> existingPercentilesMap = existingPercentiles.stream()
                    .collect(Collectors.toMap(ApplicationPercentilesBean::getId, p -> p));

            Map<Integer, Integer> percentileMapping =
                    parsePercentileMapping(ConfProperties.getString(Constants.APPLICATION_PERCENTILES_MAPPING));
            Map<Integer, Integer> reversePercentileMapping =
                    percentileMapping.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey));

            for (ApplicationPercentilePojo percentilePojo : percentilePojos) {
                if (percentilePojo.getId() != 0) { // Assuming 0 indicates a new percentile
                    if (!existingPercentilesMap.containsKey(percentilePojo.getId())) {
                        log.error("[serverValidation] Percentile record with ID [{}] not found for application [{}] and account [{}]", percentilePojo.getId(), applicationId, accountIdentifier);
                        throw new ServerException(UIMessages.APPLICATION_PERCENTILE_NOT_FOUND);
                    }
                }
                // Validate that the incoming percentileValue is one of the allowed values
                if (!reversePercentileMapping.containsKey(percentilePojo.getPercentileValue())) {
                    log.error("[serverValidation] Invalid percentile value [{}] for percentile record with ID [{}]", percentilePojo.getPercentileValue(), percentilePojo.getId());
                    throw new ServerException(UIMessages.INVALID_PERCENTILE_VALUE);
                }
            }
            utilityBean.getMetadata().put("existingPercentiles", existingPercentilesMap);

        } catch (HealControlCenterException e) {
            log.error("[serverValidation] Error during application/percentile existence check: {}", e.getMessage(), e);
            throw new ServerException(e.getMessage());
        }

        utilityBean.getMetadata().put(Constants.ACCOUNT_ID, accountId);
        utilityBean.getMetadata().put(Constants.APPLICATION_ID, Integer.parseInt(applicationId));
        utilityBean.getMetadata().put(Constants.USER_ID_KEY, userId);

        log.info("[serverValidation] Server validation successful for accountId: {} and applicationId: {}", accountId, applicationId);
        return utilityBean;
    }

    /**
     * Processes the request to update application percentiles.
     *
     * @param utilityBean UtilityBean containing validated percentilePojo, accountId, and applicationId.
     * @throws DataProcessingException if data update fails.
     */
    @LogExecutionAnnotation
    @Override
    public Void process(UtilityBean<List<ApplicationPercentilePojo>> utilityBean) throws DataProcessingException {
        log.debug("[process] Start - utilityBean: {}", utilityBean);
        List<ApplicationPercentilePojo> percentilePojos = utilityBean.getPojoObject();
        int accountId = (int) utilityBean.getMetadata().get(Constants.ACCOUNT_ID);
        int applicationId = (int) utilityBean.getMetadata().get(Constants.APPLICATION_ID);
        String userId = String.valueOf(utilityBean.getMetadata().get(Constants.USER_ID_KEY));
        String updatedTime = Objects.requireNonNull(DateTimeUtil.getCurrentTimestampInGMT()).toString();
        Map<Integer, ApplicationPercentilesBean> existingPercentilesMap = (Map<Integer, ApplicationPercentilesBean>) utilityBean.getMetadata().get("existingPercentiles");

        try {
            Map<Integer, Integer> percentileMapping =
                    parsePercentileMapping(ConfProperties.getString(Constants.APPLICATION_PERCENTILES_MAPPING));
            Map<Integer, Integer> reversePercentileMapping =
                    percentileMapping.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey));

            List<Integer> incomingIds = percentilePojos.stream()
                    .map(ApplicationPercentilePojo::getId)
                    .filter(id -> id != 0)
                    .toList();

            for (Integer existingId : existingPercentilesMap.keySet()) {
                if (!incomingIds.contains(existingId)) {
                    applicationNotifAndPercentileDao.deleteApplicationPercentileById(existingId);
                    log.info("[process] Deleted percentile with id [{}] for application [{}]", existingId, applicationId);
                }
            }

            for (ApplicationPercentilePojo percentilePojo : percentilePojos) {
                String combinedDisplayName = percentilePojo.getDisplayName();
                int mstKpiDetailsId = reversePercentileMapping.getOrDefault(percentilePojo.getPercentileValue(), 0);

                if (percentilePojo.getId() == 0) { // Create new
                    ApplicationPercentilesBean beanToCreate = ApplicationPercentilesBean.builder()
                            .applicationId(applicationId)
                            .accountId(accountId)
                            .displayName(combinedDisplayName)
                            .percentileValue(percentilePojo.getPercentileValue())
                            .mstKpiDetailsId(mstKpiDetailsId)
                            .createdTime(updatedTime)
                            .updatedTime(updatedTime)
                            .userDetailsId(userId)
                            .build();
                    applicationNotifAndPercentileDao.insertApplicationPercentile(beanToCreate);
                    log.info("[process] Inserted new percentile for application [{}]", applicationId);
                } else { // Update existing
                    ApplicationPercentilesBean existingPercentile = existingPercentilesMap.get(percentilePojo.getId());
                    ApplicationPercentilesBean beanToUpdate = ApplicationPercentilesBean.builder()
                            .id(percentilePojo.getId())
                            .applicationId(applicationId)
                            .accountId(accountId)
                            .displayName(combinedDisplayName)
                            .percentileValue(percentilePojo.getPercentileValue())
                            .mstKpiDetailsId(mstKpiDetailsId)
                            .createdTime(existingPercentile.getCreatedTime())
                            .updatedTime(updatedTime)
                            .userDetailsId(userId)
                            .build();
                    applicationNotifAndPercentileDao.updateApplicationPercentiles(beanToUpdate);
                    log.info("[process] Updated percentile with id [{}] for application [{}]", percentilePojo.getId(), applicationId);
                }
            }
        } catch (HealControlCenterException e) {
            log.error("[process] Error updating application percentiles for applicationId [{}] and accountId [{}]: {}", applicationId, accountId, e.getMessage(), e);
            throw new DataProcessingException(e.getMessage());
        }
        return null;
    }

    /**
     * Parses the percentile mapping string from conf.properties into a Map.
     * Expected format: "[mstKpiId-percentileValue],[mstKpiId-percentileValue],..."
     *
     * @param mappingString The string to parse.
     * @return A map where key is mstKpiDetailsId and value is percentileValue.
     */
    private Map<Integer, Integer> parsePercentileMapping(String mappingString) {
        Map<Integer, Integer> mapping = new HashMap<>();
        if (mappingString == null || mappingString.trim().isEmpty()) {
            log.warn("[parsePercentileMapping] Percentile mapping string is empty or null.");
            return mapping;
        }

        String[] entries = mappingString.split("],\\[");
        for (String entry : entries) {
            entry = entry.replace("[", "").replace("]", "").trim();
            String[] parts = entry.split("-");
            if (parts.length == 2) {
                try {
                    int mstKpiId = Integer.parseInt(parts[0].trim());
                    int percentileValue = Integer.parseInt(parts[1].trim());
                    mapping.put(mstKpiId, percentileValue);
                } catch (NumberFormatException e) {
                    log.error("[parsePercentileMapping] Invalid number format in mapping entry: {}. Skipping.", entry, e);
                }
            } else {
                log.warn("[parsePercentileMapping] Malformed mapping entry: {}. Skipping.", entry);
            }
        }
        return mapping;
    }

}
