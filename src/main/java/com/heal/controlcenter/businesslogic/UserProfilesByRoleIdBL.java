package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.IdBean;
import com.heal.controlcenter.beans.UserProfileBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.ServerValidationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserProfilesByRoleIdBL implements BusinessLogic<String, Long, List<IdBean>> {

    private final UserDao userDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;

    public UserProfilesByRoleIdBL(UserDao userDao, ClientValidationUtils clientValidationUtils, ServerValidationUtils serverValidationUtils) {
        this.userDao = userDao;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
    }

    @Override
    public UtilityBean<String> clientValidation(String requestBody, String... requestParams) throws ClientException {
        String authKey = requestParams[0];
        clientValidationUtils.authKeyValidation(authKey);

        HashMap<String, String> requestParamsMap = new HashMap<>();
        requestParamsMap.put(Constants.AUTH_KEY, authKey);

        return UtilityBean.<String>builder()
                .requestParams(requestParamsMap)
                .build();
    }

    @Override
    public Long serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        serverValidationUtils.authKeyValidation(authKey);

        return null;
    }

    @Override
    public List<IdBean> process(Long roleId) throws DataProcessingException {
        try {
            List<IdBean> listOfRolesById = userDao.getRolesById(roleId);
            if (listOfRolesById.isEmpty()) {
                log.debug("Role Id provided is unavailable");
                throw new HealControlCenterException("Role Id provided is unavailable");
            }
            List<UserProfileBean> listOfUserProfiles = userDao.getUserProfiles();
            if (listOfUserProfiles.isEmpty()) {
                log.debug("No profiles are present in Appsone Schema");
                return new ArrayList<>();
            }
            String roleName = listOfRolesById.stream().map(IdBean::getName).findFirst().orElse(null);
            return listOfUserProfiles.stream().filter((profile) -> profile.getRole().equals(roleName))
                    .map(p -> IdBean.builder().id(p.getUserProfileId()).name(p.getRole()).build())
                    .collect(Collectors.toList());
        } catch (HealControlCenterException e) {
            log.error("Exception encountered while fetching user roles. Reason: ", e);
            throw new DataProcessingException(e.getMessage());
        }
    }
}
