package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.ThresholdDao;
import com.heal.controlcenter.dao.mysql.entity.InstanceKpiAttributeThresholdBean;
import com.heal.controlcenter.dao.opensearch.InstanceKpiThresholdRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.InstanceKpiThresholdDetails;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class PostInstanceLevelKpiThresholdBL implements BusinessLogic<InstanceKpiThresholdDetails, List<InstanceKpiAttributeThresholdBean>, String> {

    private final ThresholdDao thresholdDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;
    private final InstanceKpiThresholdRepo instanceKpiThresholdRepo;
    private final InstanceLevelKpiThresholdUtil instanceLevelKpiThresholdUtil;

    public PostInstanceLevelKpiThresholdBL(ThresholdDao thresholdDao, ClientValidationUtils clientValidationUtils, ServerValidationUtils serverValidationUtils, InstanceKpiThresholdRepo instanceKpiThresholdRepo, InstanceLevelKpiThresholdUtil instanceLevelKpiThresholdUtil) {
        this.thresholdDao = thresholdDao;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
        this.instanceKpiThresholdRepo = instanceKpiThresholdRepo;
        this.instanceLevelKpiThresholdUtil = instanceLevelKpiThresholdUtil;
    }

    @Override
    public UtilityBean<InstanceKpiThresholdDetails> clientValidation(InstanceKpiThresholdDetails requestBody, String... requestParams) throws ClientException {
        try {
            clientValidationUtils.nullRequestBodyCheck(requestBody, UIMessages.REQUEST_BODY_NULL);

            String authKey = requestParams[0];
            clientValidationUtils.nullOrEmptyCheck(authKey, UIMessages.AUTH_KEY_INVALID);

            String accountIdentifier = requestParams[1];
            clientValidationUtils.nullOrEmptyCheck(accountIdentifier, UIMessages.ACCOUNT_IDENTIFIER_INVALID);

            instanceLevelKpiThresholdUtil.validateInstanceKpiThresholdDetails(requestBody, true);

            HashMap<String, String> requestParamsMap = new HashMap<>();
            requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, requestParams[1]);
            requestParamsMap.put(Constants.AUTH_KEY, requestParams[0]);

            return UtilityBean.<InstanceKpiThresholdDetails>builder().requestParams(requestParamsMap).pojoObject(requestBody).build();
        } catch (HealControlCenterException e) {
            throw new ClientException(e.getMessage());
        }
    }

    @Override
    public List<InstanceKpiAttributeThresholdBean> serverValidation(UtilityBean<InstanceKpiThresholdDetails> utilityBean) throws ServerException {
        try {
            String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
            String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);

            String userId = serverValidationUtils.authKeyValidation(authKey);
            Account account = serverValidationUtils.accountValidation(accountIdentifier);

            InstanceKpiThresholdDetails details = utilityBean.getPojoObject();
            Map<Integer, String> compInstIdToIdentifierMap = instanceLevelKpiThresholdUtil.verifyInstanceIdKpiAndGroupKpi(details, account.getId());

            return instanceLevelKpiThresholdUtil.kpiAndCompInstanceValidation(details, compInstIdToIdentifierMap, userId, account.getId(), accountIdentifier, true);
        } catch (HealControlCenterException e) {
            throw new ServerException(e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String process(List<InstanceKpiAttributeThresholdBean> thresholdsToAdd) throws DataProcessingException {
        try {
            insertThresholdIntoPerconaAndOpensearch(thresholdsToAdd);
        } catch (HealControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
        instanceLevelKpiThresholdUtil.addInstanceKpiAttributeLevelThresholdsInRedis(thresholdsToAdd);
        return "Instance Level KPI Attribute thresholds added successfully";
    }

    private void insertThresholdIntoPerconaAndOpensearch(List<InstanceKpiAttributeThresholdBean> thresholdBeans) throws HealControlCenterException {
        if (thresholdBeans == null || thresholdBeans.isEmpty()) {
            log.info("No thresholds to be added");
            return;
        }

        log.info("Inserting instance level KPI attribute thresholds into Percona: total size {}", thresholdBeans.size());

        int[] ids = thresholdDao.addInstanceKpiAttributeLevelThresholds(thresholdBeans);
        if (ids == null || ids.length == 0) {
            log.error("Error while adding attribute level thresholds");
            throw new HealControlCenterException("Error while adding attribute level thresholds");
        }

        //inserting threshold detail in opensearch
        try {
            log.info("Inserting instance level KPI attribute thresholds into OpenSearch: total size {}", thresholdBeans.size());
            instanceKpiThresholdRepo.addInstanceKpiThresholdInBulk(thresholdBeans);
        } catch (HealControlCenterException e) {
            log.error("Error while adding the thresholds to OpenSearch");
            throw new HealControlCenterException("Error while adding the thresholds to OpenSearch");
        }
    }
}
