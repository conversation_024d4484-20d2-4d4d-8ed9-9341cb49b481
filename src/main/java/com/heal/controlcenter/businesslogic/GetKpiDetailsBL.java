package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.KpiDataDao;
import com.heal.controlcenter.dao.mysql.entity.*;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.*;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.PaginationUtils;
import com.heal.controlcenter.util.ServerValidationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GetKpiDetailsBL implements BusinessLogic<String, UtilityBean<Account>, Page<KpiDetailsPojo>> {

    private final KpiDataDao kpiDataDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;

    public GetKpiDetailsBL(KpiDataDao kpiDataDao, ClientValidationUtils clientValidationUtils,
                           ServerValidationUtils serverValidationUtils) {
        this.kpiDataDao = kpiDataDao;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
    }

    /**
     * Validates the client request for fetching KPI details.
     * <p>
     * This method checks the validity of the account identifier and parses all filter parameters
     * (searchTerm, kpiType, group, category, component) into a requestParamsMap. Only non-empty filters
     * are added. Throws ClientException if validation fails.
     *
     * @param requestBody   The request body (not used).
     * @param requestParams The request parameters: accountIdentifier, searchTerm, kpiType, group, category, component.
     * @return UtilityBean containing validated request parameters and metadata.
     * @throws ClientException if validation fails.
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<String> clientValidation(String requestBody, String... requestParams) throws ClientException {
        log.trace("Invoked clientValidation for KPI details");
        try {
            String accountIdentifier = requestParams[0];
            log.debug("Validating accountIdentifier: {}", accountIdentifier);
            clientValidationUtils.accountIdentifierValidation(accountIdentifier);

            String searchTerm = (requestParams.length > 1) ? requestParams[1] : null;
            String kpiType = (requestParams.length > 2) ? requestParams[2] : null;
            String group = (requestParams.length > 3) ? requestParams[3] : null;
            String category = (requestParams.length > 4) ? requestParams[4] : null;
            String component = (requestParams.length > 5) ? requestParams[5] : null;
            log.debug("Parsed filters - searchTerm: {}, kpiType: {}, group: {}, category: {}, component: {}",
                    searchTerm, kpiType, group, category, component);
            HashMap<String, String> requestParamsMap = new HashMap<>();
            requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
            if (searchTerm != null && !searchTerm.isEmpty()) requestParamsMap.put("searchTerm", searchTerm);
            if (kpiType != null && !kpiType.isEmpty()) requestParamsMap.put("kpiType", kpiType);
            if (group != null && !group.isEmpty()) requestParamsMap.put("group", group);
            if (category != null && !category.isEmpty()) requestParamsMap.put("category", category);
            if (component != null && !component.isEmpty()) requestParamsMap.put("component", component);
            log.debug("Built requestParamsMap: {}", requestParamsMap);
            return UtilityBean.<String>builder()
                    .requestParams(requestParamsMap)
                    .metadata(new HashMap<>())
                    .build();
        } catch (Exception e) {
            log.error("Client validation failed: {}", e.getMessage(), e);
            throw new ClientException("Invalid request body");
        }
    }

    /**
     * Validates the server-side requirements for fetching KPI details.
     * <p>
     * This method validates the account identifier and builds a UtilityBean containing the Account object,
     * request parameters, metadata, and pagination info. Throws ServerException if validation fails.
     *
     * @param utilityBean UtilityBean from client validation.
     * @return UtilityBean containing Account object and validated parameters.
     * @throws ServerException if validation fails.
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<Account> serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        log.trace("Invoked serverValidation for KPI details");
        try {
            String userId = (String) utilityBean.getMetadata().get(Constants.USER_ID_KEY);
            String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
            log.debug("Validating accountIdentifier: {} for userId: {}", accountIdentifier, userId);
            Account account = serverValidationUtils.accountValidation(accountIdentifier);
            log.debug("Account validation successful for accountIdentifier: {}", accountIdentifier);
            return UtilityBean.<Account>builder()
                    .pojoObject(account)
                    .requestParams(utilityBean.getRequestParams())
                    .metadata(Map.of(Constants.USER_ID_KEY, userId))
                    .pageable(utilityBean.getPageable())
                    .build();
        } catch (Exception e) {
            log.error("Server validation failed: {}", e.getMessage(), e);
            throw new ServerException("Server validation failed: " + e.getMessage());
        }
    }

    /**
     * Processes the request to fetch a paginated list of KPI details based on provided filters.
     * <p>
     * This method fetches KPI details from the DAO, applies all filters and pagination, and maps the results
     * to KpiDetailsPojo objects. Throws DataProcessingException if any error occurs during processing.
     *
     * @param utilityBean UtilityBean containing Account, filters, and pagination info.
     * @return Page of KpiDetailsPojo objects.
     * @throws DataProcessingException if data processing fails.
     */
    @Override
    @LogExecutionAnnotation
    public Page<KpiDetailsPojo> process(UtilityBean<Account> utilityBean) throws DataProcessingException {
        log.trace("Invoked process for KPI details");
        int accountId = utilityBean.getPojoObject().getId();
        Pageable pageable = utilityBean.getPageable();
        String searchTerm = utilityBean.getRequestParams().get("searchTerm");
        if (searchTerm == null) {
            searchTerm = "";
        }
        String kpiType = utilityBean.getRequestParams().get("kpiType");
        String group = utilityBean.getRequestParams().get("group");
        String category = utilityBean.getRequestParams().get("category");
        String component = utilityBean.getRequestParams().get("component");
        log.debug("Processing KPI details for accountId: {} with filters - searchTerm: {}, kpiType: {}, group: {}," +
                " category: {}, component: {}", accountId, searchTerm, kpiType, group, category, component);
        PaginationUtils.validatePagination(pageable);
        try {
            List<KpiListBean> kpiDetails = kpiDataDao.getKpiList(accountId, pageable, searchTerm, kpiType, group, category, component);
            long totalCount = kpiDataDao.getKpiListCount(accountId, searchTerm, kpiType, group, category, component);
            log.debug("Fetched {} KPI records for accountId: {}", kpiDetails.size(), accountId);
            Map<Integer, KpiCategoryMapping> kpiVsCategoryMapping = getKpiVsCategoryMapping(kpiDetails);
            Map<Integer, List<ComponentBean>> commonVersionVsComponentBeans = getCommonVersionVsComponentBeans(accountId);
            Map<Integer, List<ComputedKpiBean>> computedKpiVsSupportingKpis = getComputedKpiVsSupportingKpis(accountId);
            Map<Integer, Integer> instanceCountForComponentsMap = getInstanceCountForComponentsMap(accountId);
            Map<Integer, Integer> kpiMappedToComputedKpiCountMap = getKpiMappedToComputedKpiCountMap(accountId);
            List<KpiDetailsPojo> mappedKpis = new ArrayList<>();
            for (KpiListBean kpi : kpiDetails.stream().distinct().toList()) {
                KpiCategoryMapping kpiCategoryDetails = kpiVsCategoryMapping != null ? kpiVsCategoryMapping.get(kpi.getId()) : null;
                ComponentKpiDetail componentDetail;
                String componentName;
                componentDetail = kpiDataDao.getComponentDetails(kpi.getComponentId());
                componentName = (componentDetail == null) ? "" : componentDetail.getName();
                log.debug("Fetched component details for componentId: {} - name: {}", kpi.getComponentId(), componentName);
                ComponentListPojo componentListPojo = ComponentListPojo.builder()
                        .id(kpi.getComponentId())
                        .typeId(kpi.getComponentTypeId())
                        .name(componentName)
                        .instanceCount(instanceCountForComponentsMap.getOrDefault(kpi.getComponentId(), 0))
                        .build();
                List<ComponentBean> versions = commonVersionVsComponentBeans.getOrDefault(kpi.getCommonVersionId(), new ArrayList<>());
                CommonVersionPojo commonVersionPojo = versions.isEmpty() ? new CommonVersionPojo() :
                        CommonVersionPojo.builder()
                                .id(versions.get(0).getCommonVersionId())
                                .name(versions.get(0).getCommonVersionName())
                                .supportedVersions(versions.stream()
                                        .map(v -> SupportedVersionPojo.builder()
                                                .id(v.getComponentVersionId())
                                                .name(v.getComponentVersionName())
                                                .build())
                                        .collect(Collectors.toList()))
                                .build();
                List<ComputedKpiBean> computedList = computedKpiVsSupportingKpis.getOrDefault(kpi.getId(), new ArrayList<>());
                ComputedKpiPojo computedKpiPojo = null;
                if (!computedList.isEmpty()) {
                    computedKpiPojo = new ComputedKpiPojo();
                    computedKpiPojo.setKpisUsed(computedList.stream().map(ComputedKpiBean::getKpiDetailsId).collect(Collectors.toList()));
                    computedKpiPojo.setFormula(computedList.get(0).getFormula());
                    computedKpiPojo.setDisplayFormula(computedList.get(0).getDisplayFormula());
                }
                KpiDetailsPojo kpiDetailsPojo = KpiDetailsPojo.builder()
                        .id(kpi.getId())
                        .name(kpi.getName())
                        .identifier(kpi.getIdentifier())
                        .description(kpi.getDescription())
                        .dataType(kpi.getDataType())
                        .valueType(kpi.getValueType())
                        .clusterAggregation(kpi.getClusterAggregation())
                        .instanceAggregation(kpi.getInstanceAggregation())
                        .clusterOperation(kpi.getClusterOperation())
                        .rollupOperation(kpi.getRollupOperation())
                        .kpiUnit(kpi.getKpiUnit())
                        .kpiType(kpi.getKpiType())
                        .status(kpi.getStatus())
                        .groupKpiId(kpi.getGroupKpiId())
                        .groupKpiName(kpi.getGroupKpiName())
                        .groupKpiDescription(kpi.getGroupKpiDescription())
                        .groupKpiDiscovery(kpi.getGroupKpiDiscovery())
                        .groupKpiStandardType(kpi.getGroupKpiStandardType())
                        .collectionInterval(kpi.getCollectionInterval())
                        .standardType(kpi.getStandardType())
                        .availableForAnalytics(kpi.getAvailableForAnalytics())
                        .isMappedToComputedKPI(kpiMappedToComputedKpiCountMap.getOrDefault(kpi.getId(), 0))
                        .cronExpression(kpi.getCronExpression())
                        .deltaPerSec(kpi.getDeltaPerSec())
                        .resetDeltaValue(kpi.getResetDeltaValue())
                        .categoryName(kpiCategoryDetails != null ? kpiCategoryDetails.getCategoryName() : null)
                        .workloadCategory(kpiCategoryDetails != null ? kpiCategoryDetails.getWorkLoad() == 1 : null)
                        .categoryId(kpiCategoryDetails != null ? kpiCategoryDetails.getCategoryId() : 0)
                        .component(componentListPojo)
                        .componentCommonVersion(commonVersionPojo)
                        .computedKpiDetails(computedKpiPojo)
                        .build();
                mappedKpis.add(kpiDetailsPojo);
            }
            log.debug("Mapped {} KPI records to KpiDetailsPojo for accountId: {}", mappedKpis.size(), accountId);
            return PaginationUtils.createPage(sortKpiDetails(mappedKpis), pageable, totalCount);
        } catch (Exception e) {
            log.error("Error getting KPI details for accountId: {}. Details: {}", accountId, e.getMessage(), e);
            throw new DataProcessingException("Error getting KPIs: " + e.getMessage());
        }
    }

    /**
     * Retrieves a mapping of KPI IDs to their category details.
     * <p>
     * This method queries the DAO for category details for the provided KPI list and returns a map
     * where the key is the KPI ID and the value is the corresponding {@link KpiCategoryMapping}.
     *
     * @param kpiDetails List of KPI beans for which category details are required.
     * @return Map of KPI ID to KpiCategoryMapping.
     * @throws HealControlCenterException if the DAO call fails.
     */
    private Map<Integer, KpiCategoryMapping> getKpiVsCategoryMapping(List<KpiListBean> kpiDetails) throws HealControlCenterException {
        if (kpiDetails.isEmpty()) return null;
        List<Integer> kpiIds = kpiDetails.stream().map(KpiListBean::getId).collect(Collectors.toList());
        return kpiDataDao.getCategoryDetailsForKpis(kpiIds)
                .stream().collect(Collectors.toMap(KpiCategoryMapping::getKpiId, Function.identity()));
    }

    /**
     * Retrieves a mapping of common version IDs to their associated component beans for the given account.
     *
     * @param accountId The account ID for which to fetch component details.
     * @return Map of common version ID to list of ComponentBean.
     * @throws HealControlCenterException if the DAO call fails.
     */
    private Map<Integer, List<ComponentBean>> getCommonVersionVsComponentBeans(int accountId) throws HealControlCenterException {
        return kpiDataDao.getComponentDetailsForAccount(accountId)
                .stream().collect(Collectors.groupingBy(ComponentBean::getCommonVersionId));
    }

    /**
     * Retrieves a mapping of computed KPI IDs to their supporting KPI beans for the given account.
     *
     * @param accountId The account ID for which to fetch computed KPI details.
     * @return Map of computed KPI ID to list of ComputedKpiBean.
     * @throws HealControlCenterException if the DAO call fails.
     */
    private Map<Integer, List<ComputedKpiBean>> getComputedKpiVsSupportingKpis(int accountId) throws HealControlCenterException {
        List<ComputedKpiBean> computedKpiDetails = kpiDataDao.getComputedKpiDetails(accountId);
        return computedKpiDetails.stream()
                .collect(Collectors.groupingBy(ComputedKpiBean::getComputedKpiId));
    }

    /**
     * Retrieves a mapping of component IDs to their active instance count for the given account.
     *
     * @param accountId The account ID for which to fetch instance counts.
     * @return Map of component ID to instance count.
     * @throws HealControlCenterException if the DAO call fails.
     */
    private Map<Integer, Integer> getInstanceCountForComponentsMap(int accountId) throws HealControlCenterException {
        return kpiDataDao.getActiveInstanceCountForComponents(accountId)
                .stream().collect(Collectors.toMap(CountBean::getId, CountBean::getCount));
    }

    /**
     * Retrieves a mapping of KPI IDs to the count of computed KPIs they are mapped to for the given account.
     *
     * @param accountId The account ID for which to fetch mapping counts.
     * @return Map of KPI ID to count of computed KPIs mapped.
     * @throws HealControlCenterException if the DAO call fails.
     */
    private Map<Integer, Integer> getKpiMappedToComputedKpiCountMap(int accountId) throws HealControlCenterException {
        return kpiDataDao.getKPIsMappedToComputedKpi(accountId)
                .stream().collect(Collectors.toMap(CountBean::getId, CountBean::getCount));
    }

    /**
     * Sorts the list of KPI details by component name (descending) and then by common version name (descending).
     *
     * @param kpiDetails List of KPI details to sort.
     * @return Sorted list of KPI details.
     */
    private List<KpiDetailsPojo> sortKpiDetails(List<KpiDetailsPojo> kpiDetails) {
        return kpiDetails.stream()
                .sorted(Comparator.comparing((KpiDetailsPojo k) -> k.getComponent().getName(),
                                Comparator.nullsLast(Comparator.reverseOrder()))
                        .thenComparing(k -> k.getComponentCommonVersion().getName(),
                                Comparator.nullsLast(Comparator.reverseOrder()))).collect(Collectors.toList());
    }
}
