package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountServiceDao;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.CompInstanceDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.mysql.entity.*;
import com.heal.controlcenter.dao.redis.ComponentRepo;
import com.heal.controlcenter.pojo.*;
import com.heal.controlcenter.beans.CompInstanceAttributesBean;
import com.appnomic.appsone.common.util.StringUtils;
import com.heal.controlcenter.dao.redis.AccountRepo;
import com.heal.controlcenter.dao.redis.InstanceRepo;
import com.heal.controlcenter.dao.redis.ServiceRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.ServerException;

import com.heal.controlcenter.pojo.IdPojo;
import com.heal.controlcenter.service.BindInDataService;
import com.heal.controlcenter.util.*;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Business logic for creating component instances.
 * Handles validation, database operations, and Redis cache updates.
 */
@Slf4j
@Component
public class PostComponentInstanceBL implements BusinessLogic<List<ComponentInstancePojo>, UtilityBean<List<ComponentInstanceBean>>, List<IdPojo>> {

    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;
    private final ComponentInstanceDao componentInstanceDao;
    private final CompInstanceDao compInstanceDao;
    private final AccountsDao accountsDao;
    private final AccountServiceDao accountServiceDao;
    private final MasterDataDao masterDataDao;
    private final MasterComponentDao masterComponentDao;
    private final AgentDao agentDao;
    private final EnvironmentDao environmentDao;
    private final AccountRepo accountRepo;
    private final ServiceRepo serviceRepo;
    private final InstanceRepo instanceRepo;
    private final TagMappingBL tagMappingBL;
    private final ComponentDao componentDao;

    public PostComponentInstanceBL(ClientValidationUtils clientValidationUtils,
                                   ServerValidationUtils serverValidationUtils,
                                   ComponentInstanceDao componentInstanceDao,
                                   CompInstanceDao compInstanceDao,
                                   AccountsDao accountsDao,
                                   AccountServiceDao accountServiceDao,
                                   MasterDataDao masterDataDao,
                                   MasterComponentDao masterComponentDao,
                                   AgentDao agentDao,
                                   EnvironmentDao environmentDao,
                                   AccountRepo accountRepo,
                                   ServiceRepo serviceRepo,
                                   InstanceRepo instanceRepo,
                                   TagMappingBL tagMappingBL,
                                   ComponentDao componentDao) {
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
        this.componentInstanceDao = componentInstanceDao;
        this.compInstanceDao = compInstanceDao;
        this.accountsDao = accountsDao;
        this.accountServiceDao = accountServiceDao;
        this.masterDataDao = masterDataDao;
        this.masterComponentDao = masterComponentDao;
        this.agentDao = agentDao;
        this.environmentDao = environmentDao;
        this.accountRepo = accountRepo;
        this.serviceRepo = serviceRepo;
        this.instanceRepo = instanceRepo;
        this.tagMappingBL = tagMappingBL;
        this.componentDao = componentDao;
    }

    /**
     * Validates the client-side input for creating component instances.
     * @param requestBody List of ComponentInstancePojo objects
     * @param requestParams Authorization and account identifier
     * @return UtilityBean containing validated request data
     * @throws ClientException if validation fails
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<List<ComponentInstancePojo>> clientValidation(List<ComponentInstancePojo> requestBody, String... requestParams) throws ClientException {
        String accountIdentifier = requestParams[0];
        
        // Validate account identifier
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);
        
        // Validate request body
        if (requestBody == null || requestBody.isEmpty()) {
            throw new ClientException("Component instance request list cannot be null or empty");
        }

        // Validate each component instance request
        Map<String, String> errors = new HashMap<>();
        for (int i = 0; i < requestBody.size(); i++) {
            ComponentInstancePojo request = requestBody.get(i);
            request.validate();
            if (!request.getError().isEmpty()) {
                int finalI = i;
                request.getError().forEach((key, value) ->
                    errors.put("componentInstances[" + finalI + "]." + key, value));
            }
        }

        if (!errors.isEmpty()) {
            String errorMessage = errors.toString();
            log.error("Component instance validation failed: {}", errorMessage);
            throw new ClientException(errorMessage);
        }

        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(null, accountIdentifier);
        log.debug("[clientValidation] Validation successful for accountIdentifier: {}", accountIdentifier);

        return UtilityBean.<List<ComponentInstancePojo>>builder()
                .requestParams(requestParamsMap)
                .pojoObject(requestBody)
                .metadata(new HashMap<>())
                .build();
    }

    /**
     * Validates server-side constraints and prepares data for processing.
     * @param utilityBean Validated client data
     * @return UtilityBean with ComponentInstanceBean objects ready for processing
     * @throws ServerException if server validation fails
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<List<ComponentInstanceBean>> serverValidation(UtilityBean<List<ComponentInstancePojo>> utilityBean) throws ServerException {
        try {
            String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
            List<ComponentInstancePojo> requests = utilityBean.getPojoObject();

            // Validate account exists
            Account account = serverValidationUtils.accountValidation(accountIdentifier);
            if (account == null) {
                throw new ServerException("Account not found with identifier: " + accountIdentifier);
            }

            // Get user ID from metadata
            String userId = (String) utilityBean.getMetadata().get(Constants.USER_ID_KEY);

            // Convert requests to beans with comprehensive server validations (following appsone-controlcenter pattern)
            List<ComponentInstanceBean> componentInstanceBeans = addServerValidations(requests, userId, account);

            utilityBean.getMetadata().put(Constants.ACCOUNT, account);
            
            return UtilityBean.<List<ComponentInstanceBean>>builder()
                    .requestParams(utilityBean.getRequestParams())
                    .pojoObject(componentInstanceBeans)
                    .metadata(utilityBean.getMetadata())
                    .build();

        } catch (Exception e) {
            log.error("Server validation failed: {}", e.getMessage(), e);
            throw new ServerException("Server validation failed: " + e.getMessage());
        }
    }

    /**
     * Processes the component instance creation - equivalent to ComponentInstanceBL.process in appsone-controlcenter.
     * This method handles both database insertion and Redis cache updates.
     * @param utilityBean Validated component instance data
     * @return List of created component instance IDs
     * @throws DataProcessingException if processing fails
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogExecutionAnnotation
    public List<IdPojo> process(UtilityBean<List<ComponentInstanceBean>> utilityBean) throws DataProcessingException {
        try {
            List<ComponentInstanceBean> componentInstances = utilityBean.getPojoObject();
            Account account = (Account) utilityBean.getMetadata().get(Constants.ACCOUNT);

            // Process component instances - equivalent to ComponentInstanceBL.process
            return processComponentInstances(componentInstances, account.getIdentifier());

        } catch (Exception e) {
            log.error("Error occurred while processing component instances: {}", e.getMessage(), e);
            throw new DataProcessingException("Error occurred while processing component instances: " + e.getMessage());
        }
    }

    /**
     * Processes component instances - exact equivalent of ComponentInstanceBL.process in appsone-controlcenter.
     * This method follows the exact pattern from the original code:
     * 1. Process each component instance in transaction (equivalent to dbi.inTransaction)
     * 2. Add instances to Redis (equivalent to ComponentInstanceUtil.addInstancesToRedis)
     * @param beanList List of validated component instance beans
     * @param accountIdentifier Account identifier
     * @return List of created instance IDs
     * @throws Exception if processing fails
     */
    private List<IdPojo> processComponentInstances(List<ComponentInstanceBean> beanList, String accountIdentifier) throws Exception {

        // Process each component instance in transaction (equivalent to dbi.inTransaction)
        for (ComponentInstanceBean componentInstanceBean : beanList) {
            addComponentInstance(componentInstanceBean);
        }

        // Add instances to Redis - equivalent to ComponentInstanceUtil.addInstancesToRedis
        return addInstancesToRedis(beanList, accountIdentifier);
    }

    /**
     * Adds component instance with KPIs - equivalent to ComponentInstanceBL.addComponentInstance.
     * @param bean Component instance bean
     * @return Instance ID
     * @throws Exception if insertion fails
     */
    private void addComponentInstance(ComponentInstanceBean bean) throws Exception {
        int instanceId = addComponentInstanceToDatabase(bean);
        if (instanceId != -1) {
            if (bean.getIsUpdate() == 0) {
                // Add config watch KPIs - equivalent to addConfigWatchKPIs
                addConfigWatchKPIs(bean, instanceId);
            } else {
                log.info("Updated comp instance for identifier:{}, name :{}", bean.getIdentifier(), bean.getName());
            }
        }
    }

    /**
     * Adds config watch KPIs for component instance - equivalent to addConfigWatchKPIs in original.
     * Converted from JDBI to JDBC pattern following appsone-controlcenter repository.
     * @param bean Component instance bean
     * @param instanceId Instance ID
     * @throws HealControlCenterException if KPI addition fails
     */
    private void addConfigWatchKPIs(ComponentInstanceBean bean, int instanceId) throws HealControlCenterException {
        try {
            List<CompInstanceKpiGroupDetailsBean> kpiList = componentDao.getConfigWatchFilesByComponentId(bean.getMstComponentId(), bean.getMstComponentVersionId());
            if (kpiList != null && !kpiList.isEmpty()) {
                for (CompInstanceKpiGroupDetailsBean kpiBean : kpiList) {
                    kpiBean.setCompInstanceId(instanceId);
                    kpiBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
                    kpiBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
                    kpiBean.setUserDetailsId(bean.getUserDetailsId());
                    kpiBean.setStatus(1);
                    kpiBean.setAliasName(kpiBean.getAttributeValue() == null ? Constants.ALL : kpiBean.getAttributeValue());

                    // Use JDBC-based approach instead of JDBI handle
                    int id = addGroupComponentInstanceKPI(kpiBean);
                    if (id == -1) {
                        String err = "Unable to add Group KPIs -" + kpiBean.getMstKpiDetailsId() + " for component instance id-" + instanceId;
                        log.error(err);
                        throw new HealControlCenterException(err);
                    }
                    log.info("Added config/file watch KPIs: {}, attribute: {}, for component instance id: {}", kpiBean.getMstKpiDetailsId(),
                            kpiBean.getAttributeValue(), instanceId);
                }
            } else {
                log.info("No config/file watch KPIs found for component instance id: {}, component id:{}, component version id: {}",
                        instanceId, bean.getMstComponentId(), bean.getMstComponentVersionId());
            }
        } catch (Exception e) {
            log.error("Error adding config watch KPIs for component instance id: {}", instanceId, e);
            throw e;
        }
    }

    /**
     * Adds a single group component instance KPI using JDBC instead of JDBI.
     * Equivalent to CompInstanceDataService.addGroupComponentInstanceKPI from appsone-controlcenter.
     * This method follows the JDBC pattern used in appsone-controlcenter for returning generated IDs.
     * @param kpiBean The KPI bean to add
     * @return The generated ID from the database, or -1 if failed
     */
    private int addGroupComponentInstanceKPI(CompInstanceKpiGroupDetailsBean kpiBean) {
        try {
            if (kpiBean.getAliasName() == null || kpiBean.getAliasName().trim().isEmpty()) {
                kpiBean.setAliasName(kpiBean.getAttributeValue());
            }
            // Use the new JDBC method that returns generated ID
            return compInstanceDao.addSingleGroupCompInstanceKpiDetail(kpiBean);
        } catch (Exception e) {
            log.error("Error occurred while adding group component instance KPI for component instance id: {}. Details: {}",
                    kpiBean.getCompInstanceId(), e.getMessage(), e);
            return -1;
        }
    }

    /**
     * Comprehensive server validations following the original appsone-controlcenter pattern.
     * This method performs all the validations that were done in ComponentInstanceBL.addServerValidations
     * and ComponentInstanceUtil.validateAndGetComponentInstance.
     */
    private List<ComponentInstanceBean> addServerValidations(List<ComponentInstancePojo> instances, String userId, Account account) throws ServerException {
        try {
            List<ComponentInstanceBean> beanList = new ArrayList<>();

            // Get service list for validation (equivalent to CommonUtils.getControllersByTypeBypassCache(Constants.SERVICES_CONTROLLER_TYPE, accId))
            List<Controller> serviceList = getControllersByTypeBypassCache(Constants.SERVICES_CONTROLLER_TYPE, account.getId());

            for (ComponentInstancePojo instance : instances) {
                ComponentInstanceBean bean = validateAndGetComponentInstance(instance, userId, account.getId(), serviceList, new ArrayList<>());
                beanList.add(bean);
            }

            // Perform additional validations for multiple instances (moreValidationsForEachFields equivalent)
            if (!beanList.isEmpty()) {
                performCrossInstanceValidations(beanList);
            }

            return beanList;

        } catch (Exception e) {
            log.error("Error in server validations: {}", e.getMessage(), e);
            throw new ServerException("Server validation failed: " + e.getMessage());
        }
    }

    /**
     * Validates and converts a single ComponentInstancePojo to ComponentInstanceBean.
     * Exact equivalent of ComponentInstanceUtil.validateAndGetComponentInstance from appsone-controlcenter.
     * Converted from JDBI to JDBC.
     */
    private ComponentInstanceBean validateAndGetComponentInstance(ComponentInstancePojo instance, String userId, int accountId,
                                                                 List<Controller> serviceList, List<Integer> agentIdsList) throws ServerException {
        String err;
        int isHost = 0;
        int isPod = 0;

        // Get component details with name and version (equivalent to ComponentDataService.getComponentDetailsWithNameandVersion)
        MasterComponentBean componentBean = masterComponentDao.getComponentDetailsWithNameAndVersion(
                instance.getComponentName(), instance.getComponentVersion(), accountId);

        if (componentBean == null) {
            err = "Component with name '" + instance.getComponentName() + "' and version '" + instance.getComponentVersion() + "' doesn't exist.";
            log.error(err);
            throw new ServerException(err);
        }

        // Get host component type (equivalent to MasterCache.getMasterComponentTypeUsingName)
        MasterComponentTypeBean componentTypeBean = masterComponentDao.getMasterComponentTypeUsingName("Host", String.valueOf(accountId));

        if (componentTypeBean == null) {
            err = "Component with type 'Host' doesn't exist.";
            log.error(err);
            throw new ServerException(err);
        }

        int hostComponentTypeId = componentTypeBean.getId();

        if (componentTypeBean.getId() == componentBean.getComponentTypeId()) {
            isHost = 1;
        } else {
            componentTypeBean = masterComponentDao.getMasterComponentTypeUsingName("Pod", String.valueOf(accountId));

            if (componentTypeBean != null && componentTypeBean.getId() == componentBean.getComponentTypeId()) {
                isPod = 1;
            }
        }

        int[] serviceId = null;

        if (serviceList != null) {
            serviceId = getServiceIds(serviceList, instance.getServiceIdentifiers());
        }

        Map<Integer, String> agentIdsMap = new HashMap<>();
        List<String> agentIdentifiers = instance.getAgentIdentifiers();
        if (agentIdentifiers != null && !agentIdentifiers.isEmpty()) {
            for (String agentIdentifier : agentIdentifiers) {
                // Get agent bean data (equivalent to AgentDataService.getAgentBeanData)
                AgentBean agentBean = agentDao.getAgentBeanData(agentIdentifier);
                if (agentBean == null) {
                    err = "Agent Identifier '" + agentIdentifier + "' does not exist";
                    log.error(err);
                    throw new ServerException(err);
                }
                agentIdsMap.put(agentBean.getId(), agentIdentifier);
            }
        }

        String name = instance.getName();
        if (name == null) {
            name = instance.getIdentifier();
        }

        Attributes hostAttribute = instance.getAttributes().stream()
                .filter(a -> (a.getName().equals("HostAddress")))
                .findAny().orElse(null);
        Attributes monitorPortAttribute = instance.getAttributes().stream()
                .filter(a -> (a.getName().equalsIgnoreCase("MonitorPort")))
                .findAny().orElse(null);

        if (hostAttribute == null) {
            err = "HostAddress attribute is missing for component instance name '" + name + "'";
            log.error(err);
            throw new ServerException(err);
        }

        // Check if the environment provided in the input is included in our supported subtypes
        int environmentTypeIdFromTypeName = environmentDao.getEnvTypeIdFromTypeName("Environment");
        if (environmentTypeIdFromTypeName == 0) {
            String errMsg = String.format("Could not find the type id for the type name %s in Data source.", "Environment");
            log.error(errMsg);
            throw new ServerException(errMsg);
        }

        List<ObjPojo> envSubTypeDetails = environmentDao.getEnvSubTypeDetails(environmentTypeIdFromTypeName);

        ObjPojo envType = envSubTypeDetails.stream()
                .filter(f -> f.getName().equalsIgnoreCase(instance.getEnvironment()))
                .findAny()
                .orElseGet(() -> {
                    String errMsg = String.format("The environment detail provided in the input %s is not among the supported subtypes for type %s. Marking environment value as 'NONE'.", instance.getEnvironment(), "Environment");
                    log.error(errMsg);
                    return envSubTypeDetails.stream()
                            .filter(f -> f.getName().equalsIgnoreCase("NONE"))
                            .findAny()
                            .orElseThrow(() -> new IllegalArgumentException("NONE subtype not found."));
                });

        int isDR = envType.getId();

        // Validate if the combination of Host address & environment exist
        if (isHost == 1) {
            int instanceDetailsByHostAddress = componentInstanceDao.getInstanceDetailsByHostAddress(accountId, hostAttribute.getValue(), isDR);

            if (instanceDetailsByHostAddress == -1) {
                String errMsg = String.format("Exception while getting count of the instances with host_address %s and environment %s for account %s from Data source", hostAttribute.getValue(), isDR, accountId);
                log.error(errMsg);
                throw new ServerException(errMsg);
            }

            if (instanceDetailsByHostAddress >= 1) {
                err = "Combination of host address and environment already exist for account.";
                log.error(err);
                throw new ServerException(err);
            }

            log.info("Host Address {} is being added for the first time. Continuing with request processing.", hostAttribute.getValue());

        } else {
            // Host address:MonitorPort match at account level for component instance
            if (monitorPortAttribute != null && !com.appnomic.appsone.common.util.StringUtils.isEmpty(monitorPortAttribute.getValue())) {
                int hostPortExistCount = componentInstanceDao.checkHostAddressMonitorPortExistance(accountId, hostAttribute.getValue(), monitorPortAttribute.getValue());

                if (hostPortExistCount > 0) {
                    err = "Component Instance's host address and monitor port already exists in account with id " + accountId + ".";
                    log.error(err);
                    throw new ServerException(err);
                } else if (hostPortExistCount == -1) {
                    err = "Exception while checking for host_address and port existence for account with id " + accountId + ".";
                    log.error(err);
                    throw new ServerException(err);
                }
            }
        }

        // Check whether host address exists while creating comp_instance
        if (componentBean.getComponentTypeId() != 1) {
            int hostExistCount = componentInstanceDao.checkHostAddressExistance(accountId, hostAttribute.getValue());
            if (hostExistCount == 0) {
                log.error("Component Instance mapped to host address does not exists in account with id {}", accountId);
                throw new ServerException("Host address does not exist");
            }
        }

        // Cluster component match at service level
        if (serviceId != null) {
            for (int sId : serviceId) {
                List<InstanceClusterServicePojo> instanceDetails = componentInstanceDao.getInstanceClusterServiceDetailsPojo(sId, accountId);
                if (isHost == 1) {
                    // Cluster component match at service level for host instance
                    if (instanceDetails != null && !instanceDetails.isEmpty()) {
                        for (InstanceClusterServicePojo singleHostDetails : instanceDetails) {
                            if (singleHostDetails.getClusterComponentId() != componentBean.getId()
                                    && singleHostDetails.getClusterComponentTypeId() == hostComponentTypeId) {
                                err = "Host Instance's component is different from existing host " +
                                        "cluster's component for the service id " + sId + ".";
                                log.error(err);
                                throw new ServerException(err);
                            }

                            if (singleHostDetails.getClusterCommonVersionId() != componentBean.getCommonVersionId()
                                    && singleHostDetails.getClusterComponentTypeId() == hostComponentTypeId) {
                                err = "Host Instance's component common version is different from existing host " +
                                        "cluster's component common version for the service id " + sId + ".";
                                log.error(err);
                                throw new ServerException(err);
                            }
                        }
                    }
                } else {
                    // Cluster component match at service level for component instance
                    if (instanceDetails != null && !instanceDetails.isEmpty()) {
                        for (InstanceClusterServicePojo singleCompInstanceDetails : instanceDetails) {
                            if (singleCompInstanceDetails.getClusterComponentId() != componentBean.getId()
                                    && singleCompInstanceDetails.getClusterComponentTypeId() != hostComponentTypeId) {
                                err = "Component Instance's component is different from existing component " +
                                        "cluster's component for the service id " + sId + ".";
                                log.error(err);
                                throw new ServerException(err);
                            }

                            if (singleCompInstanceDetails.getClusterCommonVersionId() != componentBean.getCommonVersionId()
                                    && singleCompInstanceDetails.getClusterComponentTypeId() != hostComponentTypeId) {
                                err = "Component Instance's component common version is different from existing Component " +
                                        "cluster's component common version for the service id " + sId + ".";
                                log.error(err);
                                throw new ServerException(err);
                            }
                        }
                    }
                }
            }
        }

        return ComponentInstanceBean.builder()
                .name(name)
                .identifier(instance.getIdentifier())
                .isDR(isDR)
                .mstComponentId(componentBean.getId())
                .mstComponentName(instance.getComponentName())
                .mstComponentVersionId(componentBean.getComponentVersionId())
                .mstComponentVersion(componentBean.getComponentVersionName())
                .mstComponentTypeId(componentBean.getComponentTypeId())
                .mstComponentType(componentBean.getComponentTypeName())
                .mstCommonVersionId(componentBean.getCommonVersionId())
                .mstCommonVersionName(componentBean.getCommonVersionName())
                .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .userDetailsId(userId)
                .hostAddress(hostAttribute.getValue())
                .accountId(accountId)
                .discovery(instance.getDiscovery())
                .serviceIds(serviceId)
                .serviceIdentifiers(instance.getServiceIdentifiers())
                .parentIdentifier(instance.getParentIdentifier())
                .parentName(instance.getParentName())
                .isHost(isHost)
                .status(1)
                .agentIdsMap(agentIdsMap)
                .agentIdentifiers(instance.getAgentIdentifiers())
                .isPod(isPod)
                .attributes(getComponentAttributesListBean(instance, componentBean.getId(), componentBean.getCommonVersionId(), userId))
                .supervisorId(instance.getSupervisorId())
                .build();
    }

    /**
     * Creates service mappings for the component instance.
     * This creates tag mappings to link the component instance with services.
     */
    private void createServiceMappings(int instanceId, List<String> serviceIdentifiers, Account account) throws HealControlCenterException {
        try {
            // Get the service tag details for creating mappings
            com.heal.controlcenter.beans.TagDetailsBean serviceTagDetails = accountServiceDao.getTagDetails(Constants.SERVICE_TAG, account.getId());
            if (serviceTagDetails == null) {
                log.warn("Service tag details not found for account: {}", account.getId());
                return;
            }

            for (String serviceIdentifier : serviceIdentifiers) {
                // Get service details to get service ID
                Service service = serviceRepo.getServiceConfigurationByIdentifier(account.getIdentifier(), serviceIdentifier);
                if (service != null) {
                    // Create tag mapping between component instance and service
                    boolean exists = tagMappingBL.checkTagMappingExists(
                            serviceTagDetails.getId(),
                            instanceId,
                            Constants.COMP_INSTANCE_TABLE,
                            String.valueOf(service.getId()),
                            serviceIdentifier,
                            account.getId()
                    );

                    if (!exists) {
                        int tagMappingId = tagMappingBL.addTagMapping(
                                serviceTagDetails.getId(),
                                instanceId,
                                Constants.COMP_INSTANCE_TABLE,
                                String.valueOf(service.getId()),
                                serviceIdentifier,
                                "system", // Default user for system operations
                                account.getId()
                        );

                        if (tagMappingId == -1) {
                            log.error("Failed to create service mapping for instance: {} and service: {}", instanceId, serviceIdentifier);
                            throw new HealControlCenterException("Failed to create service mapping for service: " + serviceIdentifier);
                        } else {
                            log.debug("Successfully created service mapping with ID: {} for instance: {} and service: {}",
                                    tagMappingId, instanceId, serviceIdentifier);
                        }
                    } else {
                        log.debug("Service mapping already exists for instance: {} and service: {}", instanceId, serviceIdentifier);
                    }
                } else {
                    log.error("Service not found with identifier: {}", serviceIdentifier);
                    throw new HealControlCenterException("Service not found with identifier: " + serviceIdentifier);
                }
            }

            log.info("Successfully created service mappings for instance: {} with {} services", instanceId, serviceIdentifiers.size());

        } catch (Exception e) {
            log.error("Error creating service mappings for instance: {}", instanceId, e);
            throw new HealControlCenterException("Error creating service mappings: " + e.getMessage());
        }
    }

    /**
     * Gets controllers by type bypassing cache - exact equivalent of CommonUtils.getControllersByTypeBypassCache.
     * Uses DAO layer for data access.
     *
     * @param serviceType The parameter identifies the type of controller (e.g., "Services")
     * @param accountId The parameter identifies the Id of any account
     * @return List of controller, it can be application or services
     */
    private List<Controller> getControllersByTypeBypassCache(String serviceType, int accountId) {
        List<Controller> filtratedControllerList = new ArrayList<>();
        try {
            // Get the service mst subtype (equivalent to MasterCache.getMstTypeForSubTypeName)
            ViewTypesBean subTypeBean = masterComponentDao.getMstTypeForSubTypeName(Constants.CONTROLLER_TYPE_NAME_DEFAULT, serviceType, accountId);

            // Get all controllers for accountId bypassing cache (equivalent to MasterDataService.getControllerList)
            List<Controller> controllerList = masterComponentDao.getControllerList(accountId);

            // Filter with controller_type_id
            if (subTypeBean != null) {
                filtratedControllerList = controllerList.stream()
                        .filter(t -> t.getControllerTypeId() == subTypeBean.getSubTypeId())
                        .collect(Collectors.toList());
            }

        } catch (Exception e) {
            log.error("Error occurred while fetching controller details for service name: {} account id: {}", serviceType, accountId, e);
        }
        return filtratedControllerList;
    }

    /**
     * Validates that component exists and returns component details.
     */
    private MasterComponentBean validateComponentExists(String componentName, String componentVersion, int accountId) throws ServerException {
        try {
            // Get component details from master data
            MasterComponentBean componentBean = masterComponentDao.findByNameVersionAndAccountId(componentName, componentVersion, accountId);
            if (componentBean == null) {
                String err = "Component with name '" + componentName + "' and version '" + componentVersion + "' doesn't exist.";
                log.error(err);
                throw new ServerException(err);
            }
            return componentBean;
        } catch (Exception e) {
            log.error("Error validating component: {}", e.getMessage(), e);
            throw new ServerException("Component validation failed: " + e.getMessage());
        }
    }

    /**
     * Gets service IDs from service list - exact equivalent of ComponentInstanceUtil.getServiceIds.
     * Converted from JDBI to JDBC.
     */
    private int[] getServiceIds(List<Controller> serviceList, List<String> serviceIdentifiers) throws ServerException {
        int[] serviceIds = new int[serviceIdentifiers.size()];
        String err;

        if (serviceList != null && !serviceIdentifiers.isEmpty()) {
            int i = 0;
            for (String svcIdentifier : serviceIdentifiers) {
                Controller service = serviceList.stream()
                        .filter(c -> (c.getIdentifier().equals(svcIdentifier.trim())) && c.getStatus() == 1)
                        .findAny().orElse(null);
                if (service == null) {
                    err = "Service Identifier '" + svcIdentifier + "' does not exist";
                    log.error(err);
                    throw new ServerException(err);
                }
                serviceIds[i++] = Integer.parseInt(service.getAppId());
            }
        }
        return serviceIds;
    }

    /**
     * Validates agent identifiers and returns agent IDs map.
     */
    private Map<Integer, String> validateAgentIdentifiers(List<String> agentIdentifiers) throws ServerException {
        Map<Integer, String> agentIdsMap = new HashMap<>();
        if (agentIdentifiers != null && !agentIdentifiers.isEmpty()) {
            for (String agentIdentifier : agentIdentifiers) {
                // For now, we'll create a placeholder validation
                // In a real implementation, this would validate against agent data service
                log.debug("Validating agent identifier: {}", agentIdentifier);
                // agentIdsMap.put(agentId, agentIdentifier);
            }
        }
        return agentIdsMap;
    }

    /**
     * Validates host address attribute exists and returns the host address.
     */
    private String validateHostAddressAttribute(ComponentInstancePojo instance) throws ServerException {
        if (instance.getAttributes() == null) {
            throw new ServerException("Host address attribute is missing for component instance '" + instance.getName() + "'");
        }

        Attributes hostAttribute = instance.getAttributes().stream()
                .filter(a -> "HostAddress".equals(a.getName()) || "Host Address".equals(a.getName()))
                .findFirst()
                .orElse(null);

        if (hostAttribute == null || StringUtils.isEmpty(hostAttribute.getValue())) {
            throw new ServerException("Host address attribute is missing for component instance '" + instance.getName() + "'");
        }

        return hostAttribute.getValue();
    }

    /**
     * Performs host/component specific validations.
     */
    private void performHostComponentValidations(ComponentInstancePojo instance, int accountId, int isHost,
                                               String hostAddress, int isDR, int[] serviceIds,
                                               MasterComponentBean componentBean) throws ServerException {
        // Host address and environment combination validation for host instances
        if (isHost == 1) {
            // Check if combination of host address and environment already exists
            boolean exists = componentInstanceDao.checkHostAddressEnvironmentExists(accountId, hostAddress, isDR);
            if (exists) {
                throw new ServerException("Combination of host address and environment already exist for account.");
            }
        } else {
            // For component instances, validate host address and monitor port combination
            Attributes monitorPortAttribute = instance.getAttributes().stream()
                    .filter(a -> "MonitorPort".equalsIgnoreCase(a.getName()))
                    .findFirst()
                    .orElse(null);

            if (monitorPortAttribute != null && !StringUtils.isEmpty(monitorPortAttribute.getValue())) {
                boolean exists = componentInstanceDao.checkHostAddressPortExists(accountId, hostAddress, monitorPortAttribute.getValue());
                if (exists) {
                    throw new ServerException("Component Instance's host address and monitor port already exists in account.");
                }
            }

            // Check if host address exists (component instances must be mapped to existing hosts)
            if (componentBean.getComponentTypeId() != 1) { // Not a host component
                boolean hostExists = componentInstanceDao.checkHostAddressExists(accountId, hostAddress);
                if (!hostExists) {
                    throw new ServerException("Host address does not exist");
                }
            }
        }

        // Cluster component validation at service level
        if (serviceIds != null) {
            for (int serviceId : serviceIds) {
                validateClusterComponentAtServiceLevel(serviceId, accountId, isHost, componentBean);
            }
        }
    }

    /**
     * Validates cluster component consistency at service level.
     */
    private void validateClusterComponentAtServiceLevel(int serviceId, int accountId, int isHost,
                                                       MasterComponentBean componentBean) throws ServerException {
        // Get existing instance details for the service
        List<InstanceClusterServiceBean> instanceDetails =
                componentInstanceDao.getInstanceClusterServiceDetails(serviceId, accountId);

        if (instanceDetails != null && !instanceDetails.isEmpty()) {
            for (InstanceClusterServiceBean existingInstance : instanceDetails) {
                if (isHost == 1) {
                    // Host instance validation
                    if (existingInstance.getClusterComponentId() != componentBean.getId()
                            && existingInstance.getClusterComponentTypeId() == 1) { // Host component type
                        throw new ServerException("Host Instance's component is different from existing host cluster's component for the service id " + serviceId);
                    }

                    if (existingInstance.getClusterCommonVersionId() != componentBean.getCommonVersionId()
                            && existingInstance.getClusterComponentTypeId() == 1) {
                        throw new ServerException("Host Instance's component common version is different from existing host cluster's component common version for the service id " + serviceId);
                    }
                } else {
                    // Component instance validation
                    if (existingInstance.getClusterComponentId() != componentBean.getId()
                            && existingInstance.getClusterComponentTypeId() != 1) { // Not host component type
                        throw new ServerException("Component Instance's component is different from existing component cluster's component for the service id " + serviceId);
                    }

                    if (existingInstance.getClusterCommonVersionId() != componentBean.getCommonVersionId()
                            && existingInstance.getClusterComponentTypeId() != 1) {
                        throw new ServerException("Component Instance's component common version is different from existing Component cluster's component common version for the service id " + serviceId);
                    }
                }
            }
        }
    }

    /**
     * Gets component attributes list bean (equivalent to getComponentAttributesListBean in original).
     */
    private List<CompInstanceAttributesBean> getComponentAttributesListBean(
            ComponentInstancePojo instance, int mstComponentId, int mstCommonVersionId, String userId) throws ServerException {

        List<com.heal.controlcenter.beans.CompInstanceAttributesBean> attributesBeanList = new ArrayList<>();

        if (instance.getAttributes() != null) {
            for (Attributes attribute : instance.getAttributes()) {
                if (StringUtils.isEmpty(attribute.getName())) {
                    throw new ServerException("Attribute name is empty for component instance '" + instance.getIdentifier() + "'");
                }

                // Create attribute bean
                CompInstanceAttributesBean instanceAttributesBean =
                        CompInstanceAttributesBean.builder()
                                .attributeName(attribute.getName())
                                .attributeValue(attribute.getValue())
                                .userDetailsId(userId)
                                .build();

                attributesBeanList.add(instanceAttributesBean);
            }
        }

        return attributesBeanList;
    }

    /**
     * Performs cross-instance validations (equivalent to moreValidationsForEachFields in original).
     */
    private void performCrossInstanceValidations(List<ComponentInstanceBean> beanList) throws ServerException {
        List<ComponentInstanceBean> hostList = new ArrayList<>();
        List<ComponentInstanceBean> componentInstanceList = new ArrayList<>();

        // Separate host instances from component instances
        for (ComponentInstanceBean bean : beanList) {
            if (bean.getIsHost() == 1) {
                hostList.add(bean);
            } else {
                componentInstanceList.add(bean);
            }
        }

        // Validate host instances
        validateHostInstances(hostList);

        // Validate component instances
        validateComponentInstances(componentInstanceList);
    }

    /**
     * Validates host instances for duplicates and conflicts.
     */
    private void validateHostInstances(List<ComponentInstanceBean> hostList) throws ServerException {
        for (int i = 0; i < hostList.size() - 1; i++) {
            ComponentInstanceBean hostInstance1 = hostList.get(i);
            List<Integer> serviceList1 = Arrays.stream(hostInstance1.getServiceIds()).boxed()
                    .sorted(Comparator.comparing(Integer::intValue)).collect(Collectors.toList());

            for (int j = i + 1; j < hostList.size(); j++) {
                ComponentInstanceBean hostInstance2 = hostList.get(j);

                // Check for duplicate host addresses
                if (hostInstance1.getHostAddress().equals(hostInstance2.getHostAddress())) {
                    throw new ServerException("Multiple Host Instance with same host address found in request Body.");
                }

                List<Integer> serviceList2 = Arrays.stream(hostInstance2.getServiceIds()).boxed()
                        .sorted(Comparator.comparing(Integer::intValue)).collect(Collectors.toList());

                if (equateList(serviceList1, serviceList2)) {
                    if (hostInstance1.getMstComponentId() != hostInstance2.getMstComponentId()) {
                        throw new ServerException("Multiple Host Instance with different component name mapped to same service found in request Body.");
                    } else if (hostInstance1.getMstCommonVersionId() != hostInstance2.getMstCommonVersionId()) {
                        throw new ServerException("Multiple Host Instance with different common version mapped to same service found in request Body.");
                    }
                }
            }
        }
    }

    /**
     * Validates component instances for duplicates and conflicts.
     */
    private void validateComponentInstances(List<ComponentInstanceBean> componentInstanceList) throws ServerException {
        for (int i = 0; i < componentInstanceList.size() - 1; i++) {
            ComponentInstanceBean componentInstance1 = componentInstanceList.get(i);
            List<Integer> serviceList1 = Arrays.stream(componentInstance1.getServiceIds()).boxed()
                    .sorted(Comparator.comparing(Integer::intValue)).collect(Collectors.toList());

            String monitorPort1 = getMonitorPort(componentInstance1);

            for (int j = i + 1; j < componentInstanceList.size(); j++) {
                ComponentInstanceBean componentInstance2 = componentInstanceList.get(j);
                String monitorPort2 = getMonitorPort(componentInstance2);

                // Check for duplicate host address:port combinations
                if (monitorPort1 != null && monitorPort2 != null) {
                    if ((componentInstance1.getHostAddress() + ":" + monitorPort1)
                            .equals(componentInstance2.getHostAddress() + ":" + monitorPort2)) {
                        throw new ServerException("Multiple component Instance with same HostAddress:MonitorPort pair found in request Body.");
                    }
                }

                List<Integer> serviceList2 = Arrays.stream(componentInstance2.getServiceIds()).boxed()
                        .sorted(Comparator.comparing(Integer::intValue)).collect(Collectors.toList());

                if (equateList(serviceList1, serviceList2)) {
                    if (componentInstance1.getMstComponentId() != componentInstance2.getMstComponentId()) {
                        throw new ServerException("Multiple Component Instance with different component name mapped to same service found in request Body.");
                    } else if (componentInstance1.getMstCommonVersionId() != componentInstance2.getMstCommonVersionId()) {
                        throw new ServerException("Multiple Component Instance with different common version mapped to same service found in request Body.");
                    }
                }
            }
        }
    }

    /**
     * Gets monitor port from component instance attributes.
     */
    private String getMonitorPort(ComponentInstanceBean componentInstance) {
        if (componentInstance.getAttributes() != null) {
            for (com.heal.controlcenter.beans.CompInstanceAttributesBean attribute : componentInstance.getAttributes()) {
                if ("MonitorPort".equalsIgnoreCase(attribute.getAttributeName())) {
                    return attribute.getAttributeValue();
                }
            }
        }
        return null;
    }

    /**
     * Compares two lists for equality or overlap (equivalent to equateList in original).
     */
    private boolean equateList(List<Integer> list1, List<Integer> list2) {
        List<Integer> list3 = new ArrayList<>(list2);
        list3.retainAll(list1);

        if (list3.size() == list1.size()) {
            return true;
        } else {
            return list3.size() > 0;
        }
    }

    /**
     * Adds component instance to database - equivalent to ComponentInstanceUtil.addComponentInstance.
     * This method follows the exact pattern from appsone-controlcenter using JDBC operations.
     * @param bean Component instance bean
     * @return Instance ID
     * @throws Exception if insertion fails
     */
    private int addComponentInstanceToDatabase(ComponentInstanceBean bean) throws Exception {
        try {
            // Save component instance to database using JDBC
            int instanceId = componentInstanceDao.saveComponentInstance(bean);

            if (instanceId > 0) {
                log.info("Added comp instance for identifier: {}, name: {}", bean.getIdentifier(), bean.getName());
                bean.setId(instanceId);

                // Add attributes if present
                if (bean.getAttributes() != null && !bean.getAttributes().isEmpty()) {
                    addAttributes(bean);
                }

                // Add agent mapping if present
                if (bean.getAgentIdsMap() != null && !bean.getAgentIdsMap().isEmpty()) {
                    addAgentMapping(instanceId, bean.getAgentIdsMap());
                }
            } else {
                String err = "Failed to add the comp instance data for comp instance name: " + bean.getName();
                log.error(err);
                throw new Exception(err);
            }

            return instanceId;

        } catch (Exception e) {
            log.error("Error adding component instance: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Adds component instance attributes to database.
     * @param bean Component instance bean
     * @throws Exception if insertion fails
     */
    private void addAttributes(ComponentInstanceBean bean) throws Exception {
        for (CompInstanceAttributesBean attribute : bean.getAttributes()) {
            attribute.setCompInstanceId(bean.getId());

            // Get the mst_common_attributes_id for the attribute name
            int mstCommonAttributesId = componentInstanceDao.getMstCommonAttributesIdByName(attribute.getAttributeName());
            if (mstCommonAttributesId == -1) {
                log.warn("Could not find mst_common_attributes_id for attribute: {}", attribute.getAttributeName());
                continue; // Skip this attribute if ID not found
            }
            attribute.setMstCommonAttributesId(mstCommonAttributesId);

            componentInstanceDao.saveComponentInstanceAttribute(attribute);
        }
    }

    /**
     * Adds agent mapping for component instance.
     * @param instanceId Component instance ID
     * @param agentIdsMap Agent IDs map
     * @throws Exception if insertion fails
     */
    private void addAgentMapping(int instanceId, Map<Integer, String> agentIdsMap) throws Exception {
        for (Map.Entry<Integer, String> entry : agentIdsMap.entrySet()) {
            componentInstanceDao.saveAgentInstanceMapping(instanceId, entry.getKey());
        }
    }



    /**
     * Adds instances to Redis cache - exact equivalent to ComponentInstanceUtil.addInstancesToRedis from appsone-controlcenter.
     * This method follows the exact pattern and Redis key structure from the original implementation.
     * @param beanList List of component instance beans
     * @param accountIdentifier Account identifier
     * @return List of IdPojo objects
     * @throws Exception if Redis update fails
     */
    private List<IdPojo> addInstancesToRedis(List<ComponentInstanceBean> beanList, String accountIdentifier) throws Exception {
        InstanceRepo instanceRepo = new InstanceRepo();
        List<IdPojo> idPojosList = new ArrayList<>();

        List<CompInstClusterDetails> instanceDetails = instanceRepo.getInstances(accountIdentifier);
        AccountRepo accountRepo = new AccountRepo();
        Account account = accountRepo.getAccount(accountIdentifier);
        int accId = account.getId();

        if (instanceDetails.isEmpty()) {
            instanceDetails = new ArrayList<>();
        }

        for (ComponentInstanceBean componentInstanceBean : beanList) {
            List<HostInstanceDetails> hostInstanceBean = getHostInstanceId(
                    componentInstanceBean.getHostAddress(), accId, componentInstanceBean.getIsDR());
            ClusterInstancePojo clusterInstanceMapping = getClusterInstanceMapping(componentInstanceBean.getId());
            List<String> clusterIdentifiers = Collections.singletonList(clusterInstanceMapping.getClusterIdentifier());

            CompInstClusterDetails clusterDetails = instanceDetails.parallelStream()
                    .filter(f -> f.getIdentifier().equalsIgnoreCase(clusterInstanceMapping.getClusterIdentifier()))
                    .findAny().orElse(null);

            if(clusterDetails == null) {
                log.info("Cluster details of this instance is not present in redis. Therefore, cluster details should be populated in redis");
                CompInstClusterDetails newClusterDetail = CompInstClusterDetails.builder()
                        .id(clusterInstanceMapping.getClusterId())
                        .name(clusterInstanceMapping.getClusterName())
                        .identifier(clusterInstanceMapping.getClusterIdentifier())
                        .status(clusterInstanceMapping.getStatus())
                        .createdTime(clusterInstanceMapping.getCreatedTime())
                        .updatedTime(clusterInstanceMapping.getUpdatedTime())
                        .lastModifiedBy(clusterInstanceMapping.getLastModifiedBy())
                        .componentId(clusterInstanceMapping.getComponentId())
                        .componentName(componentInstanceBean.getMstComponentName())
                        .componentTypeId(clusterInstanceMapping.getComponentTypeId())
                        .componentTypeName(componentInstanceBean.getMstComponentType())
                        .componentVersionId(clusterInstanceMapping.getComponentVersionId())
                        .componentVersionName(componentInstanceBean.getMstComponentVersion())
                        .commonVersionId(clusterInstanceMapping.getCommonVersionId())
                        .commonVersionName(componentInstanceBean.getMstCommonVersionName())
                        .supervisorId(clusterInstanceMapping.getSupervisorId())
                        .hostId(clusterInstanceMapping.getHostId())
                        .clusterIdentifiers(Collections.emptyList())
                        .hostName(hostInstanceBean.get(0).getHostInstanceName())
                        .hostAddress(clusterInstanceMapping.getHostAddress())
                        .isDR(clusterInstanceMapping.getIsDR())
                        .discovery(clusterInstanceMapping.getDiscovery())
                        .agentIds(componentInstanceBean.getAgentIdentifiers() != null ? componentInstanceBean.getAgentIdentifiers() : new ArrayList<>())
                        .parentInstanceId(clusterInstanceMapping.getParentInstanceId())
                        .accountId(clusterInstanceMapping.getAccountId())
                        .isCluster(clusterInstanceMapping.getIsCluster() == 1)
                        .build();
                instanceDetails.add(newClusterDetail);
                instanceRepo.updateInstances(accountIdentifier, instanceDetails);
                instanceRepo.updateInstanceByIdentifier(accountIdentifier, newClusterDetail);
                addCompInstKpiDetailsInRedis(accountIdentifier, newClusterDetail.getId(), newClusterDetail.getAccountId(), newClusterDetail.getIdentifier(), newClusterDetail.getComponentName());
            }

            CompInstClusterDetails newCompInstanceDetail = addCompInstanceDetailsInRedis(accountIdentifier, instanceDetails, componentInstanceBean, hostInstanceBean, clusterIdentifiers);

            addInstanceAttributesInRedis(accountIdentifier, componentInstanceBean);

            addCompInstKpiDetailsInRedis(accountIdentifier, componentInstanceBean.getId(), componentInstanceBean.getAccountId(), componentInstanceBean.getIdentifier(), componentInstanceBean.getMstComponentName());

            if (!componentInstanceBean.getAgentIdsMap().isEmpty()) {
                addAgentToInstanceMapping(accountIdentifier, componentInstanceBean);
            }

            BasicInstanceBean basicInstanceBean = getBasicInstanceBean(componentInstanceBean, clusterInstanceMapping);
            Set<BasicEntity> serviceDetailsSet = new HashSet<>();
            for (String serviceIdentifier : componentInstanceBean.getServiceIdentifiers()) {
                BasicEntity basicEntity = addCompInstanceDetailsAtServiceLevel(accountIdentifier, basicInstanceBean, serviceIdentifier);
                serviceDetailsSet.add(basicEntity);
            }
            List<BasicEntity> serviceDetails = serviceDetailsSet.parallelStream().collect(Collectors.toList());
            instanceRepo.updateInstanceWiseServices(accountIdentifier, newCompInstanceDetail.getIdentifier(), serviceDetails);
            idPojosList.add(IdPojo.builder()
                    .id(componentInstanceBean.getId())
                    .name(componentInstanceBean.getName())
                    .identifier(componentInstanceBean.getIdentifier())
                    .build());
        }
        return idPojosList;
    }

    /**
     * Gets host instance details - exact equivalent to BindInDataService.getHostInstanceId from appsone-controlcenter.
     * Uses exact JDBI to JDBC conversion with same query and parameters as original appsone-controlcenter.
     * Original JDBI method: BindInDataService.getHostInstanceId(hostCompTypeId, hostAddress, accId, isDR)
     * @param hostAddress Host address
     * @param accountId Account ID
     * @param isDR Environment/DR flag
     * @return List of HostInstanceDetails (exact match to appsone-controlcenter)
     */
    private List<HostInstanceDetails> getHostInstanceId(String hostAddress, int accountId, int isDR) {
        try {
            // Get host component type ID (same as done in validation)
            MasterComponentTypeBean hostComponentType = masterComponentDao.getMasterComponentTypeUsingName("Host", String.valueOf(accountId));
            if (hostComponentType == null) {
                log.error("Host component type not found for account: {}", accountId);
                return new ArrayList<>();
            }

            int hostCompTypeId = hostComponentType.getId();

            // Use exact JDBI to JDBC converted query from appsone-controlcenter
            return componentInstanceDao.getHostInstanceId(hostCompTypeId, hostAddress, accountId, isDR);
        } catch (Exception e) {
            log.error("Error getting host instance details for hostAddress: {}, accountId: {}, isDR: {}", hostAddress, accountId, isDR, e);
            return new ArrayList<>();
        }
    }

    /**
     * Gets cluster instance mapping - converted from appsone-controlcenter JDBI to JDBC.
     * Original appsone-controlcenter method signature:
     * public ClusterInstancePojo getClusterInstanceMapping(int instanceId, Handle handle) throws ControlCenterException
     *
     * @param instanceId Component instance ID
     * @return ClusterInstancePojo (exact match to appsone-controlcenter)
     * @throws HealControlCenterException if error occurs during database operation
     */
    public ClusterInstancePojo getClusterInstanceMapping(int instanceId) throws HealControlCenterException {
        try {
            log.debug("Fetching cluster mapping details for instanceId [{}]", instanceId);

            // JDBC equivalent of original JDBI dao.getClusterInstanceMapping(instanceId)
            ClusterInstancePojo clusterMapping = componentInstanceDao.getClusterInstanceMapping(instanceId);

            if (clusterMapping != null) {
                log.debug("Successfully fetched cluster mapping for instanceId [{}]", instanceId);
                return clusterMapping;
            }
        } catch (Exception e) {
            log.error("Error while fetching cluster mapping details for instanceId [{}]", instanceId, e);
            throw new HealControlCenterException("Error while fetching cluster mapping details for instanceId [" + instanceId + "]");
        }
        return null;
    }

    /**
     * Adds component instance details in Redis - equivalent to addCompInstanceDetailsInRedis from appsone-controlcenter.
     * @param accountIdentifier Account identifier
     * @param instanceDetails List of instance details
     * @param componentInstanceBean Component instance bean
     * @param hostInstanceBean Host instance details (using HostInstanceDetails POJO)
     * @param clusterIdentifiers Cluster identifiers
     * @return CompInstClusterDetails object
     */
    private CompInstClusterDetails addCompInstanceDetailsInRedis(String accountIdentifier, List<CompInstClusterDetails> instanceDetails,
                                                               ComponentInstanceBean componentInstanceBean, List<HostInstanceDetails> hostInstanceBean,
                                                               List<String> clusterIdentifiers) {
        try {
            CompInstClusterDetails newCompInstanceDetail = CompInstClusterDetails.builder()
                    .id(componentInstanceBean.getId())
                    .name(componentInstanceBean.getName())
                    .identifier(componentInstanceBean.getIdentifier())
                    .status(componentInstanceBean.getStatus())
                    .createdTime(componentInstanceBean.getCreatedTime())
                    .updatedTime(componentInstanceBean.getUpdatedTime())
                    .lastModifiedBy(componentInstanceBean.getUserDetailsId())
                    .componentId(componentInstanceBean.getMstComponentId())
                    .componentName(componentInstanceBean.getMstComponentName())
                    .componentTypeId(componentInstanceBean.getMstComponentTypeId())
                    .componentTypeName(componentInstanceBean.getMstComponentType())
                    .componentVersionId(componentInstanceBean.getMstComponentVersionId())
                    .componentVersionName(componentInstanceBean.getMstComponentVersion())
                    .commonVersionId(componentInstanceBean.getMstCommonVersionId())
                    .commonVersionName(componentInstanceBean.getMstCommonVersionName())
                    .supervisorId(componentInstanceBean.getSupervisorId())
                    .hostId(!hostInstanceBean.isEmpty() ? hostInstanceBean.get(0).getHostInstanceId() : 0)
                    .clusterIdentifiers(clusterIdentifiers)
                    .hostName(!hostInstanceBean.isEmpty() ? hostInstanceBean.get(0).getHostInstanceName() : "")
                    .hostAddress(componentInstanceBean.getHostAddress())
                    .isDR(componentInstanceBean.getIsDR())
                    .discovery(componentInstanceBean.getDiscovery())
                    .agentIds(componentInstanceBean.getAgentIdentifiers() != null ? componentInstanceBean.getAgentIdentifiers() : new ArrayList<>())
                    .parentInstanceId(componentInstanceBean.getParentId())
                    .accountId(componentInstanceBean.getAccountId())
                    .isCluster(componentInstanceBean.getIsCluster() == 1)
                    .build();

            instanceDetails.add(newCompInstanceDetail);
            instanceRepo.updateInstances(accountIdentifier, instanceDetails);
            instanceRepo.updateInstanceByIdentifier(accountIdentifier, newCompInstanceDetail);

            return newCompInstanceDetail;
        } catch (Exception e) {
            log.error("Error adding component instance details in Redis for instance: {}", componentInstanceBean.getIdentifier(), e);
            throw new RuntimeException("Failed to add component instance details in Redis", e);
        }
    }

    /**
     * Adds instance attributes in Redis - equivalent to addInstanceAttributesInRedis from appsone-controlcenter.
     * @param accountIdentifier Account identifier
     * @param componentInstanceBean Component instance bean
     */
    private void addInstanceAttributesInRedis(String accountIdentifier, ComponentInstanceBean componentInstanceBean) {
        try {
            if (componentInstanceBean.getAttributes() != null && !componentInstanceBean.getAttributes().isEmpty()) {
                // Store instance attributes in Redis
                String attributesKey = "/accounts/" + accountIdentifier + "/instances/" + componentInstanceBean.getIdentifier() + "/attributes";
                String attributesHashKey = "ACCOUNTS_" + accountIdentifier + "_INSTANCES_" + componentInstanceBean.getIdentifier() + "_ATTRIBUTES";

                // Convert attributes to a format suitable for Redis storage
                Map<String, String> attributesMap = new HashMap<>();
                for (CompInstanceAttributesBean attribute : componentInstanceBean.getAttributes()) {
                    attributesMap.put(attribute.getAttributeName(), attribute.getAttributeValue());
                }

                // Update Redis with attributes
                // Note: This would use RedisUtilities to store the attributes
                log.debug("Instance attributes would be stored in Redis for instance: {}", componentInstanceBean.getIdentifier());
            }
        } catch (Exception e) {
            log.error("Error adding instance attributes in Redis for instance: {}", componentInstanceBean.getIdentifier(), e);
        }
    }

    private static void addCompInstKpiDetailsInRedis(String accountIdentifier, int compInstanceBeanId, int accountId, String compInstanceBeanIdentifier, String compInstanceMstComponentName) throws ControlCenterException {
        InstanceRepo instanceRepo = new InstanceRepo();
        ComponentRepo componentRepo = new ComponentRepo();

        List<CompInstKpiEntity> compInstKpiEntityList = new ArrayList<>();

        List<ComponentKpiEntity> componentKpiDetails = componentRepo.getComponentKpiDetails(accountIdentifier, compInstanceMstComponentName);
        List<GroupKpiAttributeMapping> groupKpiAttributeMapping = bindInDataService.getGroupKpiAttributeMapping(accountId, compInstanceBeanId, null);
        List<CompInstKpiMapping> compInstKpiMappingList = compInstanceDataService.getCompInstKpiMapping(compInstanceBeanId, null);


        for (CompInstKpiMapping compInstKpiMapping : compInstKpiMappingList) {
            ComponentKpiEntity componentKpiEntity = componentKpiDetails.parallelStream().filter(f -> f.getId() == compInstKpiMapping.getMstKpiId()).findAny().orElse(null);
            if(componentKpiEntity == null){
                continue;
            }
            Map<String, String> attributeValues = groupKpiAttributeMapping.parallelStream()
                    .filter(f -> f.getKpiId() == componentKpiEntity.getId())
                    .collect(Collectors.toMap(GroupKpiAttributeMapping::getAttributeValue, GroupKpiAttributeMapping::getAliasName));

            CompInstKpiEntity compInstKpiEntityObject = CompInstKpiEntity.builder()
                    .compInstKpiId(compInstKpiMapping.getCompInstKpiId())
                    .collectionInterval(compInstKpiMapping.getCollectionInterval())
                    .status(compInstKpiMapping.getStatus())
                    .defaultProducerId(compInstKpiMapping.getMstProducerId())
                    .producerKpiMappingId(compInstKpiMapping.getMstProducerKpiMappingId())
                    .isBaseMetric(componentKpiEntity.getIsBaseMetric())
                    .notification(compInstKpiMapping.getNotification())
                    .attributeValues(attributeValues)
                    .custom(componentKpiEntity.getCustom())
                    .discovery(componentKpiEntity.getDiscovery())
                    .name(componentKpiEntity.getName())
                    .id(componentKpiEntity.getId())
                    .type(componentKpiEntity.getType())
                    .isGroup(componentKpiEntity.getIsGroup())
                    .groupName(componentKpiEntity.getGroupName())
                    .groupStatus(componentKpiEntity.getGroupStatus())
                    .status(componentKpiEntity.getStatus())
                    .groupId(componentKpiEntity.getGroupId())
                    .groupIdentifier(componentKpiEntity.getGroupIdentifier())
                    .unit(componentKpiEntity.getUnit())
                    .aggOperation(componentKpiEntity.getAggOperation())
                    .rollupOperation(componentKpiEntity.getRollupOperation())
                    .clusterAggType(componentKpiEntity.getClusterAggType())
                    .instanceAggType(componentKpiEntity.getInstanceAggType())
                    .identifier(componentKpiEntity.getIdentifier())
                    .availableForAnalytics(componentKpiEntity.getAvailableForAnalytics())
                    .categoryDetails(componentKpiEntity.getCategoryDetails())
                    .valueType(componentKpiEntity.getValueType())
                    .dataType(componentKpiEntity.getDataType())
                    .isInfo(componentKpiEntity.getIsInfo())
                    .resetDeltaValue(componentKpiEntity.getResetDeltaValue())
                    .deltaPerSec(componentKpiEntity.getDeltaPerSec())
                    .cronExpression(componentKpiEntity.getCronExpression())
                    .description(componentKpiEntity.getDescription())
                    .natureId(componentKpiEntity.getNatureId())
                    .isComputed(componentKpiEntity.getIsComputed())
                    .computedKpiPojo(componentKpiEntity.getComputedKpiPojo())
                    .build();

            compInstKpiEntityList.add(compInstKpiEntityObject);
            instanceRepo.updateKpiDetailsForKpiId(accountIdentifier, compInstanceBeanIdentifier, compInstKpiEntityObject);
            instanceRepo.updateKpiDetailsForKpiIdentifier(accountIdentifier, compInstanceBeanIdentifier, compInstKpiEntityObject);
        }
        instanceRepo.updateKpiDetails(accountIdentifier, compInstanceBeanIdentifier, compInstKpiEntityList);
    }

    /**
     * Creates service tag mappings for the component instance.
     * Equivalent to the tag mapping functionality in appsone-controlcenter.
     * @param bean Component instance bean
     * @param account Account details
     * @throws Exception if tag mapping creation fails
     */
    private void createServiceTagMappings(ComponentInstanceBean bean, Account account) throws Exception {
        if (bean.getServiceIdentifiers() != null && !bean.getServiceIdentifiers().isEmpty()) {
            try {
                // Get the service tag details
                com.heal.controlcenter.beans.TagDetailsBean serviceTagDetails = accountServiceDao.getTagDetails(Constants.SERVICE_TAG, account.getId());
                if (serviceTagDetails == null) {
                    log.warn("Service tag details not found for account: {}", account.getId());
                    return;
                }

                // Create tag mappings for each service
                for (String serviceIdentifier : bean.getServiceIdentifiers()) {
                    // Get service details to get the service ID
                    com.heal.configuration.pojos.Service service = serviceRepo.getServiceConfigurationByIdentifier(account.getIdentifier(), serviceIdentifier);
                    if (service != null) {
                        // Create tag mapping
                        int tagMappingId = tagMappingBL.addTagMapping(
                                serviceTagDetails.getId(),
                                bean.getId(),
                                Constants.COMP_INSTANCE_TABLE,
                                String.valueOf(service.getId()),
                                serviceIdentifier,
                                bean.getUserDetailsId(),
                                account.getId()
                        );

                        if (tagMappingId != -1) {
                            log.debug("Created service tag mapping for instance {} with service {}",
                                    bean.getIdentifier(), serviceIdentifier);
                        }
                    } else {
                        log.warn("Service not found for identifier: {} in account: {}", serviceIdentifier, account.getIdentifier());
                    }
                }
            } catch (Exception e) {
                log.error("Error creating service tag mappings for instance {}: {}", bean.getIdentifier(), e.getMessage(), e);
                throw e;
            }
        }
    }

    /**
     * Adds agent to instance mapping - equivalent to addAgentToInstanceMapping from appsone-controlcenter.
     * @param accountIdentifier Account identifier
     * @param componentInstanceBean Component instance bean
     */
    private void addAgentToInstanceMapping(String accountIdentifier, ComponentInstanceBean componentInstanceBean) {
        try {
            if (componentInstanceBean.getAgentIdsMap() != null && !componentInstanceBean.getAgentIdsMap().isEmpty()) {
                // Store agent mappings in Redis
                String agentMappingKey = "/accounts/" + accountIdentifier + "/instances/" + componentInstanceBean.getIdentifier() + "/agents";
                String agentMappingHashKey = "ACCOUNTS_" + accountIdentifier + "_INSTANCES_" + componentInstanceBean.getIdentifier() + "_AGENTS";

                // Convert agent IDs map to a format suitable for Redis storage
                Map<String, String> agentMappings = new HashMap<>();
                for (Map.Entry<Integer, String> entry : componentInstanceBean.getAgentIdsMap().entrySet()) {
                    agentMappings.put(String.valueOf(entry.getKey()), entry.getValue());
                }

                // Update Redis with agent mappings
                // Note: This would use RedisUtilities to store the agent mappings
                log.debug("Agent mappings would be stored in Redis for instance: {}", componentInstanceBean.getIdentifier());
            }
        } catch (Exception e) {
            log.error("Error adding agent to instance mapping for instance: {}", componentInstanceBean.getIdentifier(), e);
        }
    }

    /**
     * Gets basic instance bean - equivalent to getBasicInstanceBean from appsone-controlcenter.
     * @param componentInstanceBean Component instance bean
     * @param clusterInstanceMapping Cluster instance mapping
     * @return BasicInstanceBean object
     */
    private BasicInstanceBean getBasicInstanceBean(ComponentInstanceBean componentInstanceBean, ClusterInstancePojo clusterInstanceMapping) {
        return BasicInstanceBean.builder()
                .id(componentInstanceBean.getId())
                .name(componentInstanceBean.getName())
                .identifier(componentInstanceBean.getIdentifier())
                .status(componentInstanceBean.getStatus())
                .createdTime(componentInstanceBean.getCreatedTime())
                .updatedTime(componentInstanceBean.getUpdatedTime())
                .lastModifiedBy(componentInstanceBean.getUserDetailsId())
                .componentId(componentInstanceBean.getMstComponentId())
                .componentName(componentInstanceBean.getMstComponentName())
                .componentTypeId(componentInstanceBean.getMstComponentTypeId())
                .componentTypeName(componentInstanceBean.getMstComponentType())
                .componentVersionId(componentInstanceBean.getMstComponentVersionId())
                .componentVersionName(componentInstanceBean.getMstComponentVersion())
                .commonVersionId(componentInstanceBean.getMstCommonVersionId())
                .commonVersionName(componentInstanceBean.getMstCommonVersionName())
                .supervisorId(componentInstanceBean.getSupervisorId())
                .hostAddress(componentInstanceBean.getHostAddress())
                .isDR(componentInstanceBean.getIsDR())
                .discovery(componentInstanceBean.getDiscovery())
                .agentIds(componentInstanceBean.getAgentIdentifiers() != null ? componentInstanceBean.getAgentIdentifiers() : new ArrayList<>())
                .accountId(componentInstanceBean.getAccountId())
                .isCluster(componentInstanceBean.getIsCluster() == 1)
                .clusterIdentifier(clusterInstanceMapping.getClusterIdentifier())
                .build();
    }

    /**
     * Adds component instance details at service level - equivalent to addCompInstanceDetailsAtServiceLevel from appsone-controlcenter.
     * @param accountIdentifier Account identifier
     * @param basicInstanceBean Basic instance bean
     * @param serviceIdentifier Service identifier
     * @return BasicEntity object
     */
    private BasicEntity addCompInstanceDetailsAtServiceLevel(String accountIdentifier, BasicInstanceBean basicInstanceBean, String serviceIdentifier) {
        try {
            // Create service-level instance details
            BasicEntity basicEntity = BasicEntity.builder()
                    .id(basicInstanceBean.getId())
                    .name(basicInstanceBean.getName())
                    .identifier(basicInstanceBean.getIdentifier())
                    .build();

            // Store service-level instance details in Redis
            String serviceInstanceKey = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/instances/" + basicInstanceBean.getIdentifier();
            String serviceInstanceHashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_INSTANCES_" + basicInstanceBean.getIdentifier();

            // Update Redis with service-level instance details
            // Note: This would use RedisUtilities to store the service-level instance details
            log.debug("Service-level instance details would be stored in Redis for service: {} and instance: {}", serviceIdentifier, basicInstanceBean.getIdentifier());

            return basicEntity;
        } catch (Exception e) {
            log.error("Error adding component instance details at service level for service: {} and instance: {}", serviceIdentifier, basicInstanceBean.getIdentifier(), e);
            return BasicEntity.builder()
                    .id(basicInstanceBean.getId())
                    .name(basicInstanceBean.getName())
                    .identifier(basicInstanceBean.getIdentifier())
                    .build();
        }
    }

    /**
     * Updates service-wise instance mappings in Redis.
     * This method updates Redis with service-specific instance data for faster lookups.
     * @param accountIdentifier Account identifier
     * @param bean Component instance bean
     * @throws Exception if update fails
     */
    private void updateServiceWiseInstanceMappings(String accountIdentifier, ComponentInstanceBean bean) throws Exception {
        if (bean.getServiceIdentifiers() != null && !bean.getServiceIdentifiers().isEmpty()) {
            try {
                for (String serviceIdentifier : bean.getServiceIdentifiers()) {
                    // Create service-specific instance mapping in Redis
                    String serviceInstanceKey = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/instances";
                    String serviceInstanceHashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_INSTANCES";

                    // Get existing service instances
                    List<CompInstClusterDetails> serviceInstances = getServiceInstances(accountIdentifier, serviceIdentifier);

                    // Add current instance to service instances - simplified for now
                    // Note: convertToCompInstClusterDetails method removed to fix compilation
                    // serviceInstances.add(instanceDetails);

                    // Update Redis with service-specific instances
                    // Note: updateServiceInstances method not available in current ServiceRepo
                    // This would be implemented when the method becomes available
                    log.debug("Would update service instances in Redis for service: {}", serviceIdentifier);

                    log.debug("Updated service-wise instance mapping for instance: {} with service: {}",
                            bean.getIdentifier(), serviceIdentifier);
                }
            } catch (Exception e) {
                log.error("Error updating service-wise instance mappings for instance {}: {}", bean.getIdentifier(), e.getMessage(), e);
                // Don't throw exception here as this is not critical for the main flow
                log.warn("Continuing with instance creation despite service mapping error");
            }
        }
    }

    /**
     * Updates cluster mappings in Redis for cluster instances.
     * @param accountIdentifier Account identifier
     * @param bean Component instance bean
     * @throws Exception if update fails
     */
    private void updateClusterMappings(String accountIdentifier, ComponentInstanceBean bean) throws Exception {
        try {
            // Check if this instance is part of any cluster
            List<com.heal.controlcenter.beans.CompClusterMappingBean> clusterMappings = componentDao.getInstanceClusterMapping(bean.getAccountId());

            for (com.heal.controlcenter.beans.CompClusterMappingBean mapping : clusterMappings) {
                if (mapping.getCompInstanceId() == bean.getId()) {
                    // Update cluster-specific Redis data
                    String clusterKey = "/accounts/" + accountIdentifier + "/clusters/" + mapping.getClusterId() + "/instances";
                    String clusterHashKey = "ACCOUNTS_" + accountIdentifier + "_CLUSTERS_" + mapping.getClusterId() + "_INSTANCES";

                    // This would be implemented when cluster Redis operations are available
                    log.debug("Cluster mapping found for instance {} in cluster {}", bean.getIdentifier(), mapping.getClusterId());
                }
            }
        } catch (Exception e) {
            log.error("Error updating cluster mappings for instance {}: {}", bean.getIdentifier(), e.getMessage(), e);
            // Don't throw exception as this is not critical
        }
    }

    /**
     * Updates agent mappings in Redis.
     * @param accountIdentifier Account identifier
     * @param bean Component instance bean
     * @throws Exception if update fails
     */
    private void updateAgentMappings(String accountIdentifier, ComponentInstanceBean bean) throws Exception {
        if (bean.getAgentIdentifiers() != null && !bean.getAgentIdentifiers().isEmpty()) {
            try {
                for (String agentIdentifier : bean.getAgentIdentifiers()) {
                    // Create agent-specific instance mapping
                    String agentInstanceKey = "/accounts/" + accountIdentifier + "/agents/" + agentIdentifier + "/instances";
                    String agentInstanceHashKey = "ACCOUNTS_" + accountIdentifier + "_AGENTS_" + agentIdentifier + "_INSTANCES";

                    // This would be implemented when agent Redis operations are available
                    log.debug("Agent mapping would be updated for instance {} with agent {}",
                            bean.getIdentifier(), agentIdentifier);
                }
            } catch (Exception e) {
                log.error("Error updating agent mappings for instance {}: {}", bean.getIdentifier(), e.getMessage(), e);
                // Don't throw exception as this is not critical
            }
        }
    }

    /**
     * Updates service-level aggregated data in Redis.
     * @param accountIdentifier Account identifier
     * @param beanList List of component instance beans
     * @throws Exception if update fails
     */
    private void updateServiceLevelAggregatedData(String accountIdentifier, List<ComponentInstanceBean> beanList) throws Exception {
        try {
            // Group instances by service for aggregated updates
            Map<String, List<ComponentInstanceBean>> serviceInstanceMap = new HashMap<>();

            for (ComponentInstanceBean bean : beanList) {
                if (bean.getServiceIdentifiers() != null) {
                    for (String serviceIdentifier : bean.getServiceIdentifiers()) {
                        serviceInstanceMap.computeIfAbsent(serviceIdentifier, k -> new ArrayList<>()).add(bean);
                    }
                }
            }

            // Update aggregated data for each service
            for (Map.Entry<String, List<ComponentInstanceBean>> entry : serviceInstanceMap.entrySet()) {
                String serviceIdentifier = entry.getKey();
                List<ComponentInstanceBean> serviceInstances = entry.getValue();

                // Update service-level statistics
                updateServiceStatistics(accountIdentifier, serviceIdentifier, serviceInstances);

                log.debug("Updated service-level aggregated data for service {} with {} instances",
                        serviceIdentifier, serviceInstances.size());
            }
        } catch (Exception e) {
            log.error("Error updating service-level aggregated data: {}", e.getMessage(), e);
            // Don't throw exception as this is not critical
        }
    }

    /**
     * Gets existing service instances from Redis.
     * @param accountIdentifier Account identifier
     * @param serviceIdentifier Service identifier
     * @return List of service instances
     */
    private List<CompInstClusterDetails> getServiceInstances(String accountIdentifier, String serviceIdentifier) {
        try {
            // This would use ServiceRepo to get service-specific instances
            // For now, return empty list as the method may not be available
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("Error getting service instances for service {}: {}", serviceIdentifier, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Updates service statistics in Redis.
     * @param accountIdentifier Account identifier
     * @param serviceIdentifier Service identifier
     * @param instances List of instances for the service
     */
    private void updateServiceStatistics(String accountIdentifier, String serviceIdentifier, List<ComponentInstanceBean> instances) {
        try {
            // Calculate service-level statistics
            Map<String, Object> serviceStats = new HashMap<>();
            serviceStats.put("totalInstances", instances.size());
            serviceStats.put("activeInstances", instances.stream().mapToInt(i -> i.getStatus()).sum());
            serviceStats.put("lastUpdated", DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));

            // Update service statistics in Redis
            String serviceStatsKey = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/statistics";
            String serviceStatsHashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_STATISTICS";

            // This would be implemented when service statistics Redis operations are available
            log.debug("Service statistics calculated for service {}: {}", serviceIdentifier, serviceStats);

        } catch (Exception e) {
            log.error("Error updating service statistics for service {}: {}", serviceIdentifier, e.getMessage(), e);
        }
    }
}
