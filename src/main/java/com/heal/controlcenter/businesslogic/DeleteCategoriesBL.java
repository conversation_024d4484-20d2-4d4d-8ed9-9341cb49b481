package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.Category;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.CategoryDao;
import com.heal.controlcenter.dao.redis.CategoryRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.DeleteCategoriesPojo;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.ServerValidationUtils;
import com.heal.controlcenter.util.UserValidationUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class DeleteCategoriesBL implements BusinessLogic<DeleteCategoriesPojo, UtilityBean<List<Integer>>, Object> {

    private final CategoryDao categoryDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;
    private final CategoryRepo categoryRepo;

    public DeleteCategoriesBL(CategoryDao categoryDao, UserValidationUtil userValidationUtil,
                              ClientValidationUtils clientValidationUtils, ServerValidationUtils serverValidationUtils,
                              CategoryRepo categoryRepo) {
        this.categoryDao = categoryDao;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
        this.categoryRepo = categoryRepo;
    }

    /**
     * Validates the client request for deleting categories. Checks account identifier and prepares request params and metadata.
     *
     * @param requestBody   The request body containing category identifiers and delete type.
     * @param requestParams The request parameters, where the first is expected to be the account identifier.
     * @return UtilityBean containing the validated request and metadata.
     * @throws ClientException if the account identifier is invalid or request is malformed.
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<DeleteCategoriesPojo> clientValidation(DeleteCategoriesPojo requestBody, String... requestParams) throws ClientException {
        log.debug("[clientValidation] Start - requestBody: {}, requestParams: {}", requestBody, requestParams);
        if (requestBody == null || requestBody.getCategoryIdentifiers().isEmpty()) {
            log.error("[clientValidation] Request body is null or categoryIdentifiers is empty.");
            throw new ClientException("Request body cannot be null.");
        }
        String accountIdentifier = requestParams[0];
        log.debug("[clientValidation] Start - accountIdentifier: {}, requestBody: {}", accountIdentifier, requestBody);
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);

        HashMap<String, String> requestParamsMap = new HashMap<>();
        requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        log.debug("[clientValidation] Request params map created: {}", requestParamsMap);

        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.HARD_DELETE, requestBody.isHardDelete());
        log.debug("[clientValidation] Metadata set: {}", metadata);

        UtilityBean<DeleteCategoriesPojo> result = UtilityBean.<DeleteCategoriesPojo>builder()
                .requestParams(requestParamsMap)
                .pojoObject(requestBody)
                .metadata(metadata)
                .build();
        log.debug("[clientValidation] Returning UtilityBean: {}", result);
        return result;
    }

    /**
     * Performs server-side validation for deleting categories. Checks account, category existence, and KPI mappings.
     *
     * @param utilityBean UtilityBean containing the request and metadata.
     * @return UtilityBean with validated category IDs for deletion.
     * @throws ServerException if validation fails (e.g., category not found, still mapped to KPIs).
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<List<Integer>> serverValidation(UtilityBean<DeleteCategoriesPojo> utilityBean) throws ServerException {
        log.debug("[serverValidation] Start - utilityBean: {}", utilityBean);
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        Account account = serverValidationUtils.accountValidation(accountIdentifier);
        int accountId = account.getId();

        List<String> categoryIdentifiers = utilityBean.getPojoObject().getCategoryIdentifiers();
        boolean hardDelete = (boolean) utilityBean.getMetadata().get(Constants.HARD_DELETE);

        log.debug("[serverValidation] hardDelete: {}", hardDelete);
        log.debug("[serverValidation] Category identifiers: {}", categoryIdentifiers);

        if (categoryIdentifiers == null || categoryIdentifiers.isEmpty()) {
            log.warn("[serverValidation] No category identifiers provided for deletion.");
            throw new ServerException("No category identifiers provided for deletion.");
        }

        List<Integer> categoryIdList;
        try {
            categoryIdList = categoryDao.getCategoryIdsByIdentifiers(categoryIdentifiers, accountId);
        } catch (HealControlCenterException e) {
            throw new ServerException("Server validation failed: " + e.getMessage());
        }

        log.debug("[serverValidation] Fetched categoryIdList: {}", categoryIdList);

        // Check KPI mapping for each categoryId
        for (Integer categoryId : categoryIdList) {
            try {
                int kpiCount = categoryDao.getKpiCountForCategory(categoryId);
                if (kpiCount != 0) {
                    log.error("Category cannot be deleted because it is mapped with some other KPI. CategoryId: {}", categoryId);
                    throw new ServerException("Category can't be deleted because it's mapped with KPI.");
                }
            } catch (HealControlCenterException e) {
                throw new ServerException(e.getMessage());
            }
        }

        Map<String, Object> metadata = utilityBean.getMetadata();
        metadata.put(Constants.HARD_DELETE, hardDelete);

        UtilityBean<List<Integer>> result = UtilityBean.<List<Integer>>builder()
                .requestParams(utilityBean.getRequestParams())
                .pojoObject(categoryIdList)
                .metadata(metadata)
                .build();
        log.debug("[serverValidation] Returning UtilityBean: {}", result);
        return result;
    }

    /**
     * Processes the deletion of categories (hard or soft delete) and updates Redis cache.
     *
     * @param utilityBean UtilityBean containing category IDs to delete and metadata.
     * @return Success message if deletion is successful.
     * @throws DataProcessingException if any error occurs during deletion or Redis update.
     */
    @Override
    @LogExecutionAnnotation
    @Transactional(rollbackFor = {Exception.class})
    public String process(UtilityBean<List<Integer>> utilityBean) throws DataProcessingException {
        log.debug("[process] Start - utilityBean: {}", utilityBean);
        List<Integer> categoryIdList = utilityBean.getPojoObject();
        boolean hardDelete = (boolean) utilityBean.getMetadata().get(Constants.HARD_DELETE);
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);

        log.info("[process] isHardDelete before processing: {}", hardDelete);
        log.debug("[process] Category IDs to delete: {}", categoryIdList);

        try {
            for (Integer categoryId : categoryIdList) {
                log.debug("[process] Deleting categoryId: {} (hardDelete: {})", categoryId, hardDelete);
                if (hardDelete) {
                    log.info("Performing hard delete for categoryId [{}]", categoryId);
                    hardDeleteCategory(categoryId);
                } else {
                    log.info("Performing soft delete for categoryId [{}]", categoryId);
                    softDeleteCategory(categoryId);
                }
            }
            // Redis cleanup
            deleteCategoriesInRedis(categoryIdList, accountIdentifier);
            log.debug("[process] Completed DB and Redis deletion for category IDs: {}", categoryIdList);
            return "Category(ies) deleted successfully.";
        } catch (HealControlCenterException e) {
            log.error("Error during category deletion process. Details: ", e);
            throw new DataProcessingException("Failed to delete category: " + e.getMessage());
        }
    }

    /**
     * Removes deleted categories from Redis and updates the Redis cache for the account.
     *
     * @param categoryIdList    List of category IDs to delete
     * @param accountIdentifier The account identifier
     * @throws HealControlCenterException if Redis update fails
     */
    private void deleteCategoriesInRedis(List<Integer> categoryIdList, String accountIdentifier) throws HealControlCenterException {
        log.debug("[deleteCategoriesInRedis] Start for accountIdentifier: {} and categoryIdList: {}", accountIdentifier, categoryIdList);
        List<Category> categoryDetails = categoryRepo.getCategoryDetails(accountIdentifier);
        for (Integer categoryId : categoryIdList) {
            Category category = categoryDetails.parallelStream().filter(f -> f.getId() == categoryId).findAny().orElse(null);
            if (category != null) {
                String categoryIdentifier = category.getIdentifier();
                categoryRepo.deleteCategoryInRedis(accountIdentifier, categoryIdentifier);
            } else {
                log.warn("Could not find category object detail in Redis for deletion, categoryId: {}", categoryId);
            }
        }
        categoryRepo.updateCategoryDetails(accountIdentifier, categoryDetails);
        log.debug("[deleteCategoriesInRedis] Updated Redis category list for accountIdentifier: {}", accountIdentifier);
    }

    /**
     * Performs hard delete of a category in the database.
     *
     * @param categoryId Category ID to delete.
     * @throws HealControlCenterException if the delete operation fails.
     */
    private void hardDeleteCategory(Integer categoryId) throws HealControlCenterException {
        log.debug("[hardDeleteCategory] Deleting categoryId: {}", categoryId);
        checkExecutionStatus(categoryDao.deleteCategoryById(categoryId), categoryId, "mst_category_details");
        log.debug("[hardDeleteCategory] Completed hard delete for categoryId: {}", categoryId);
    }

    /**
     * Performs soft delete (status update) of a category in the database.
     *
     * @param categoryId Category ID to soft delete.
     * @throws HealControlCenterException if the update fails.
     */
    private void softDeleteCategory(Integer categoryId) throws HealControlCenterException {
        log.debug("[softDeleteCategory] Soft deleting categoryId: {}", categoryId);
        checkExecutionStatus(categoryDao.softDeleteCategoryById(categoryId), categoryId, "mst_category_details");
        log.info("Soft deleted categoryId [{}]", categoryId);
        log.debug("[softDeleteCategory] Completed soft delete for categoryId: {}", categoryId);
    }

    /**
     * Checks the result of a delete/update operation and throws exception if it failed.
     *
     * @param res       Result of the operation (number of rows affected).
     * @param catId     Category ID.
     * @param tableName Table name where the operation was performed.
     * @throws HealControlCenterException if the operation failed.
     */
    private void checkExecutionStatus(int res, Integer catId, String tableName) throws HealControlCenterException {
        log.debug("[checkExecutionStatus] Table: {}, CatId: {}, Result: {}", tableName, catId, res);
        if (res == -1) {
            log.error("Error occurred while deleting from '[{}]' table for categoryId '[{}]'.", tableName, catId);
            throw new HealControlCenterException(String.format("Error occurred while deleting from '[%s]' table for categoryId '[%s]'.", tableName, catId));
        } else if (res == 0) {
            log.warn("No rows affected in [{}] for categoryId [{}]", tableName, catId);
        } else {
            log.debug("[checkExecutionStatus] Successful operation in [{}] for categoryId [{}]", tableName, catId);
        }
    }
}
