package com.heal.controlcenter.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties("attributeValue")
public class KpiViolationConfig {
    private String attributeValue;
    private String profileName;
    private String profileId;
    private String operation;
    private Double minThreshold;
    private Double maxThreshold;
    private String startTime;
    private String endTime;
    private int status;
    private int severity;
    private int persistence;
    private int suppression;
    private String thresholdType;
    private String thresholdLevel;
    private int thresholdSeverityId;
}
