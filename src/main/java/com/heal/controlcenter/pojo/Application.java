package com.heal.controlcenter.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.heal.controlcenter.util.Constants.ALLOWED_IDENTIFIER_CHARACTERS;
import static com.heal.controlcenter.util.Constants.ALLOWED_NAME_CHARACTERS;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class Application {
    private String name;
    private String identifier;
    private String environment;
    private String timezoneId;
    private List<SeverityLevel> severity;
    private List<ApplicationTags> tags;
    @JsonProperty("linkedEnvironment")
    private LinkedEnvironment linkedEnvironment;

    public Map<String, String> validate() {
        final int ACCOUNT_NAME_MIN_LENGTH = 2;
        final int ACCOUNT_NAME_MAX_LENGTH = 32;
        final int IDENTIFIER_MIN_LENGTH = 2;
        final int IDENTIFIER_MAX_LENGTH = 64;

        final String EMPTY_ACCOUNT_NAME = "Account name cannot be empty or null.";
        final String INVALID_ACCOUNT_NAME_LENGTH = "Account name must be between 2 and 32 characters.";

        final String INVALID_IDENTIFIER_LENGTH = "Identifier must be between 2 and 64 characters.";

        final String EMPTY_TAG_TYPE = "Tag type must not be empty.";
        final String EMPTY_TAG_KEY = "Tag key must not be empty.";
        final String EMPTY_TAG_VALUE = "Tag value must not be empty.";
        final String EMPTY_TAG_ACTION = "Tag action must not be blank if provided.";

        Map<String, String> errors = new HashMap<>();

        // Account Name Validation
        if (name == null || name.trim().isEmpty()) {
            log.error(EMPTY_ACCOUNT_NAME);
            errors.put("accountName", EMPTY_ACCOUNT_NAME);
        } else {
            String Appname = name.trim();
            if (Appname.length() < ACCOUNT_NAME_MIN_LENGTH || Appname.length() > ACCOUNT_NAME_MAX_LENGTH) {
                log.error(INVALID_ACCOUNT_NAME_LENGTH);
                errors.put("accountName", INVALID_ACCOUNT_NAME_LENGTH);
            }
            if (!Appname.matches(ALLOWED_NAME_CHARACTERS)) {
                log.error(ALLOWED_NAME_CHARACTERS);
                errors.put("accountName", ALLOWED_NAME_CHARACTERS);
            }
        }

        //Account Identifier validation
        if (identifier == null || identifier.trim().isEmpty()) {
            this.identifier = UUID.randomUUID().toString();
        } else {
            String id = identifier.trim();
            if (id.length() < IDENTIFIER_MIN_LENGTH || id.length() > IDENTIFIER_MAX_LENGTH) {
                log.error(INVALID_IDENTIFIER_LENGTH);
                errors.put("identifier", INVALID_IDENTIFIER_LENGTH);
            }
            if (!id.matches(ALLOWED_IDENTIFIER_CHARACTERS)) {
                log.error(ALLOWED_IDENTIFIER_CHARACTERS);
                errors.put("identifier", ALLOWED_IDENTIFIER_CHARACTERS);
            }
        }

       /* // ApplicationTags validation
        if (tags != null && !tags.isEmpty()) {
            int index = 0;
            for (ApplicationTags tag : tags) {
                if (tag.getType() == null || tag.getType().trim().isEmpty()) {
                    String message = "Tag 'type' is empty at index " + index;
                    log.error(message);
                    errors.put("tags[" + index + "].type", EMPTY_TAG_TYPE);
                }
                if (tag.getKey() == null || tag.getKey().trim().isEmpty()) {
                    String message = "Tag 'key' is empty at index " + index;
                    log.error(message);
                    errors.put("tags[" + index + "].key", EMPTY_TAG_KEY);
                }
                if (tag.getValue() == null || tag.getValue().trim().isEmpty()) {
                    String message = "Tag 'value' is empty at index " + index;
                    log.error(message);
                    errors.put("tags[" + index + "].value", EMPTY_TAG_VALUE);
                }
                // Optional: Validate action if required
                if (tag.getAction() != null && tag.getAction().trim().isEmpty()) {
                    String message = "Tag 'action' is blank at index " + index;
                    log.warn(message);
                    errors.put("tags[" + index + "].action", EMPTY_TAG_ACTION);
                }
                index++;
            }
        }*/
        return errors;
    }
}
