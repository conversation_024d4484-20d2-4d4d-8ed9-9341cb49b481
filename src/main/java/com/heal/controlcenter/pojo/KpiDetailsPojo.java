package com.heal.controlcenter.pojo;

import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class KpiDetailsPojo {
    private int id;
    private String name;
    private String identifier;
    private String description;
    private String dataType;
    private String valueType;
    private String clusterOperation;
    private String rollupOperation;
    private String clusterAggregation;
    private String instanceAggregation;
    private String kpiUnit;
    private String kpiType;
    private int status;
    private int groupKpiId;
    private String groupKpiName;
    private String groupKpiDescription;
    private int groupKpiDiscovery;
    private int groupKpiStandardType;
    private String categoryName;
    private int categoryId;
    private Boolean workloadCategory;
    private ComponentListPojo component;
    private CommonVersionPojo componentCommonVersion;
    private ComputedKpiPojo computedKpiDetails;
    private int collectionInterval;
    private int standardType;
    private int availableForAnalytics;
    private int isMappedToComputedKPI;
    private String cronExpression;
    private int resetDeltaValue;
    private int deltaPerSec;
}
