package com.heal.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InstanceComponentMappingDetails {

    private int hostId;
    private String hostName;
    private String hostIdentifier;
    private int compInstanceId;
    private String compInstanceName;
    private String compInstanceIdentifier;
    private int componentId;
    private String componentName;
    private int componentVersionId;
    private String componentVersionName;
    private int commonVersionId;
    private String commonVersionName;
    private int componentTypeId;
    private String componentTypeName;
    private int commonAttributeId;
    private String attributeName;
    private String attributeValue;
}
