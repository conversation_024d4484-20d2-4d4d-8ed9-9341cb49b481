package com.heal.controlcenter.pojo;
import lombok.Data;

import java.util.Map;
import com.heal.controlcenter.beans.ServiceGroupBean;
import java.util.Set;


@Data
public class ServiceListPage {
 private String name;
 private Integer id;
 private String identifier;
 private String status;
 private String createdBy;
 private Long createdOn;
 private Long lastModifiedOn;
 private String lastModifiedBy;
 private String componentType = "NA";
 private Set<IdPojo> application;
 private Map<String, Object> inbound;
 private Map<String, Object> outbound;
 private String environment;
 private String layer;
 private String type;
 private boolean entryPointService;
 private ServiceGroupBean serviceGroup;

}
