package com.heal.controlcenter.pojo;

import com.appnomic.appsone.common.enums.DiscoveryStatus;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Builder
@EqualsAndHashCode
public class GetConnectionPojo {

    private int sourceServiceId;
    private String sourceServiceName;
    private String sourceServiceIdentifier;
    private int destinationServiceId;
    private String destinationServiceName;
    private String destinationServiceIdentifier;
    @EqualsAndHashCode.Exclude
    private String process;
    @EqualsAndHashCode.Exclude
    private DiscoveryStatus status;
    @EqualsAndHashCode.Exclude
    private long lastDiscoveryRunTime;

}
