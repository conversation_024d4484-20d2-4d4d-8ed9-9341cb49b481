package com.heal.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProducerDetailsPojo {
    private int id;
    private String name;
    private String description;
    private String producerTypeName;
    private int isCustom;
    private int status;
    private int isKpiGroup;
    private Timestamp createdTime;
    private Timestamp updatedTime;
    private String userName;
}
