package com.heal.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentInstanceMappingPojo {
    private int instanceId;
    private String instanceIdentifier;
    private int agentId;
    private String agentName;
    private String uniqueToken;
    private int physicalAgentId;
    private String physicalAgentIdentifier;
    private int agentTypeId;
    private String agentTypeName;
    private int status;
}
