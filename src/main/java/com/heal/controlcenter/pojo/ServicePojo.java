package com.heal.controlcenter.pojo;

import com.appnomic.appsone.common.util.StringUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.heal.controlcenter.util.UIMessages;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ServicePojo {

    private int isEntryPointService;
    private String name;
    private String identifier;
    private String layer;
    private String type;
    private String environment;
    private List<String> appIdentifiers;

    @JsonProperty("linkedEnvironment")
    private LinkedEnvironmentPojo linkedEnvironment;
    private ServiceGroupPojo serviceGroup;

    private Map<String, String> error = new HashMap<>();

    public Map<String, String> validate() {
        error.clear();

        boolean isServiceGroupPresent = serviceGroup != null && !StringUtils.isEmpty(serviceGroup.getIdentifier());

        if (!isServiceGroupPresent) {
            // Normal service validations
            if (StringUtils.isEmpty(name) || name.length() > 128) {
                log.error(UIMessages.INVALID_SERVICE_NAME);
                error.put("name", UIMessages.INVALID_SERVICE_NAME);
            }

            if (!StringUtils.isEmpty(identifier) && identifier.length() > 128) {
                log.error(UIMessages.INVALID_SERVICE_IDENTIFIER);
                error.put("identifier", UIMessages.INVALID_SERVICE_IDENTIFIER);
            }

            if (StringUtils.isEmpty(type)) {
                log.error(UIMessages.INVALID_SERVICE_TYPE);
                error.put("type", UIMessages.INVALID_SERVICE_TYPE);
            }

            if (StringUtils.isEmpty(layer)) {
                error.put("layer", "Service layer is missing");
            }

            if (StringUtils.isEmpty(environment)) {
                error.put("environment", "Environment is missing");
            }

            if (appIdentifiers == null || appIdentifiers.isEmpty()) {
                error.put("appIdentifiers", "No mapped applications");
            }
        } else {
            if (StringUtils.isEmpty(name)) {
                error.put("name", "Name is required when using service group");
            }

            if (StringUtils.isEmpty(identifier)) {
                error.put("identifier", "Identifier is required when using service group");
            }

            if (StringUtils.isEmpty(serviceGroup.getIdentifier())) {
                error.put("serviceGroup.identifier", "Service group identifier is required");
            }
        }

        return error;
    }

    public Map<String, String> validate(boolean isPutApi) {
        error.clear();

        boolean isServiceGroupPresent = serviceGroup != null && !StringUtils.isEmpty(serviceGroup.getIdentifier());

        if (!isServiceGroupPresent) {
            // Normal service validations
            if (StringUtils.isEmpty(name) || name.length() > 128) {
                log.error(UIMessages.INVALID_SERVICE_NAME);
                error.put("name", UIMessages.INVALID_SERVICE_NAME);
            }

            if (!StringUtils.isEmpty(identifier) && identifier.length() > 128) {
                log.error(UIMessages.INVALID_SERVICE_IDENTIFIER);
                error.put("identifier", UIMessages.INVALID_SERVICE_IDENTIFIER);
            }

            if (StringUtils.isEmpty(type)) {
                log.error(UIMessages.INVALID_SERVICE_TYPE);
                error.put("type", UIMessages.INVALID_SERVICE_TYPE);
            }

            if (StringUtils.isEmpty(layer)) {
                error.put("layer", "Service layer is missing");
            }

            if (StringUtils.isEmpty(environment)) {
                error.put("environment", "Environment is missing");
            }

            if (appIdentifiers == null || appIdentifiers.isEmpty()) {
                error.put("appIdentifiers", "No mapped applications");
            }

            // PUT-specific: identifier must be present for update
            if (isPutApi && StringUtils.isEmpty(identifier)) {
                error.put("identifier", "Identifier is required for update (PUT API)");
            }
        } else {
            if (StringUtils.isEmpty(name)) {
                error.put("name", "Name is required when using service group");
            }

            if (StringUtils.isEmpty(identifier)) {
                error.put("identifier", "Identifier is required when using service group");
            }

            if (StringUtils.isEmpty(serviceGroup.getIdentifier())) {
                error.put("serviceGroup.identifier", "Service group identifier is required");
            }
        }

        return error;
    }
}
