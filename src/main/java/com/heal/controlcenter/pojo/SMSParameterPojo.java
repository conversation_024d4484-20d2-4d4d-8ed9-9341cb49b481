package com.heal.controlcenter.pojo;

import com.heal.controlcenter.util.Constants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SMSParameterPojo {

    private int parameterId;
    private String parameterName;
    private String parameterValue;
    private String parameterType;
    private String action;
    private Boolean isPlaceholder;

    private final int PARAMETER_LENGTH = 128;
    private final String INVALID_SMS_PARAMETER_NAME = "Parameter name is either NULL, empty or its length is more than 128 characters.";
    private final String INVALID_SMS_PARAMETER_VALUE = "Parameter value is either NULL, empty or its length is more than 128 characters.";
    private final String INVALID_SMS_PARAM_PLACEHOLDER = "SMS PARAMETER placeholder is NULL or EMPTY.";
    private final String INVALID_SMS_PARAM_ACTION = "SMS Parameter is invalid. It can be ‘add’, ‘edit’ or ‘delete’.";

    public Map<String, String> validate() {
        Map<String, String> error = new HashMap<>();
        if (parameterName.isEmpty() || parameterName.length() > PARAMETER_LENGTH) {
            log.error(INVALID_SMS_PARAMETER_NAME);
            error.put("SMS parameter name", INVALID_SMS_PARAMETER_NAME);
        }

        if (parameterValue.isEmpty() || parameterValue.length() > PARAMETER_LENGTH) {
            log.error(INVALID_SMS_PARAMETER_VALUE);
            error.put("SMS parameter Value", INVALID_SMS_PARAMETER_VALUE);
        }

        if (isPlaceholder == null) {
            log.error(INVALID_SMS_PARAM_PLACEHOLDER);
            error.put("SMS parameter placeholder", INVALID_SMS_PARAM_PLACEHOLDER);
        }

        if (action.isEmpty() || !(action.equalsIgnoreCase(Constants.SMS_ACTION_ADD)
                || action.equalsIgnoreCase(Constants.SMS_ACTION_EDIT)
                || action.equalsIgnoreCase(Constants.SMS_ACTION_DELETE))) {
            log.error(INVALID_SMS_PARAM_ACTION);
            error.put("SMS parameter action", INVALID_SMS_PARAM_ACTION);
        }
        return error;
    }
}
