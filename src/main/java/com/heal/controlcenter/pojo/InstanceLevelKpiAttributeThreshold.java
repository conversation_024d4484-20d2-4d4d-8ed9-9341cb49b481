package com.heal.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class InstanceLevelKpiAttributeThreshold {
    private int kpiId;
    private int groupKpiId;
    private String attributeValue;
    ThresholdDetails lowThreshold;
    ThresholdDetails mediumThreshold;
    ThresholdDetails highThreshold;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ThresholdDetails {
        private Double minThreshold;
        private Double maxThreshold;
        private String operationType;
        private int status;
        private ActionsEnum action;
        private String coverageWindow;
    }

    public boolean validate(boolean addingThresholds) {
        boolean retVal = true;

        if (groupKpiId == 0 && !attributeValue.equals("ALL")) {
            log.error("attributeValue validation failure. Reason: It should be 'ALL' for non-group KPI.");
            retVal = false;
        }

        if (attributeValue == null || attributeValue.trim().isEmpty() || attributeValue.trim().length() > 512) {
            log.error("attributeValue validation failure. Reason: It should be a non-NULL, non-empty string with length lesser than or equal to 512 characters.");
            retVal = false;
        }

        retVal = validateThresholdValues(addingThresholds, retVal, lowThreshold);
        retVal = validateThresholdValues(addingThresholds, retVal, mediumThreshold);
        retVal = validateThresholdValues(addingThresholds, retVal, highThreshold);

        return retVal;
    }

    private boolean validateThresholdValues(boolean addingThresholds, boolean retVal, ThresholdDetails thresholdDetails) {
        if (thresholdDetails == null) {
            return retVal;
        }
        String operation = thresholdDetails.getOperationType();
        Double minThreshold = thresholdDetails.getMinThreshold();
        Double maxThreshold = thresholdDetails.getMaxThreshold();
        int status = thresholdDetails.getStatus();

        if (operation != null && (operation.trim().isEmpty()
                || (!"greater than".equalsIgnoreCase(operation)
                && !"lesser than".equalsIgnoreCase(operation)
                && !"not between".equalsIgnoreCase(operation)
                && !"in between".equalsIgnoreCase(operation)
                && !"not equals".equalsIgnoreCase(operation)))) {
            log.error("operation validation failure. Reason: It should be one of 'greater than', 'lesser than', 'not between', 'in between', 'not equals'.");
            retVal = false;
        }

        if (kpiId <= 0) {
            log.error("kpiId validation failure. Reason: It should be a positive integer.");
            retVal = false;
        }

        if (minThreshold != null && minThreshold < 0) {
            log.error("minThreshold validation failure. Reason: It should be a positive value.");
            retVal = false;
        }

        if (maxThreshold != null && maxThreshold < 0) {
            log.error("maxThreshold validation failure. Reason: It should be a positive value.");
            retVal = false;
        }

        if (minThreshold != null && maxThreshold != null && minThreshold >= maxThreshold && (!"greater than".equalsIgnoreCase(operation)
                && !"lesser than".equalsIgnoreCase(operation))) {
            log.error("minThreshold and maxThreshold validation failure. Reason: maxThreshold value should be greater than minThreshold");
            retVal = false;
        }

        if (status != 0 && status != 1) {
            log.error("status validation failure. Reason: It should be either 0 or 1.");
            retVal = false;
        }

        if (addingThresholds && status == 0) {
            log.error("status validation failure. Reason: It should be 1 when adding thresholds.");
            retVal = false;
        }

        if (!addingThresholds && thresholdDetails.action == null) {
            log.error("Update action validation failure. Reason: It should be either ADD, MODIFY or DELETE.");
            retVal = false;
        }
        return retVal;
    }
}
