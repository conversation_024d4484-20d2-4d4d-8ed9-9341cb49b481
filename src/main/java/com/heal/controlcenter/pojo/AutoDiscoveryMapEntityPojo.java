package com.heal.controlcenter.pojo;

import com.heal.controlcenter.enums.Entity;
import com.heal.controlcenter.enums.Environment;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AutoDiscoveryMapEntityPojo {
    private Entity entityType;
    private int[] serviceIdentifiers;// id from controller table
    private String[] serviceMappingIdentifiers;
    private Environment environment;// field contains value only if entityType is Host. null if entityType is CompInstance.
    private int accountId;
    private Map<String, String> errorMessages = new HashMap<>();

    public Map<String, String> validate() {
        if (entityType == null) {
            String error = "Entity type cannot be null in request.";
            log.error(error);
            errorMessages.put("entityType", error);
            return errorMessages;
        }

        if (!entityType.equals(Entity.Host) && !entityType.equals(Entity.CompInstance)) {
            String error = "Entity type incompatible.";
            log.error(error);
            errorMessages.put("entityType", error);
        }

        if (serviceMappingIdentifiers == null || serviceMappingIdentifiers.length == 0) {
            String error = "Entity/entities identifier(s) list empty in request.";
            log.error(error);
            errorMessages.put("serviceMappingIdentifiers", error);
        }

        return errorMessages;
    }
}
