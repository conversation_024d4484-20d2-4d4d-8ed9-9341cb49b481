package com.heal.controlcenter.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;

import com.heal.configuration.pojos.TenantDetails;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.heal.controlcenter.util.Constants.ALLOWED_IDENTIFIER_CHARACTERS;
import static com.heal.controlcenter.util.Constants.ALLOWED_NAME_CHARACTERS;

@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Account {

    private Integer accountId;
    private String accountName;
    private String identifier;
    private int status;
    @JsonIgnore
    private String privateKey;
    @JsonIgnore
    private String publicKey;
    private List<Tags> tags;
    private long timezoneMilli;
    private String timeZoneString;
    private Long updatedTime;
    private String lastModifiedBy;
    private String dateFormat;
    private String timeFormat;
    private ThresholdSeverity thresholdSeverity;
    private int closingWindow;
    private int maxDataBreaks;
    private TenantDetails tanantDetails;

    public Map<String, String> validate() {
        final int ACCOUNT_NAME_MIN_LENGTH = 2;
        final int ACCOUNT_NAME_MAX_LENGTH = 32;
        final int IDENTIFIER_MIN_LENGTH = 2;
        final int IDENTIFIER_MAX_LENGTH = 64;

        final String EMPTY_ACCOUNT_NAME = "Account name cannot be empty or null.";
        final String INVALID_ACCOUNT_NAME_LENGTH = "Account name must be between 2 and 32 characters.";

        final String INVALID_IDENTIFIER_LENGTH = "Identifier must be between 2 and 64 characters.";

        final String EMPTY_TAG_NAME = "Tag name cannot be empty or null.";
        final String EMPTY_TAG_IDENTIFIER = "Tag identifier cannot be empty or null.";
        Map<String, String> errors = new HashMap<>();

        // Account Name Validation
        if (accountName == null || accountName.trim().isEmpty()) {
            log.error(EMPTY_ACCOUNT_NAME);
            errors.put("accountName", EMPTY_ACCOUNT_NAME);
        } else {
            String name = accountName.trim();
            if (name.length() < ACCOUNT_NAME_MIN_LENGTH || name.length() > ACCOUNT_NAME_MAX_LENGTH) {
                log.error(INVALID_ACCOUNT_NAME_LENGTH);
                errors.put("accountName", INVALID_ACCOUNT_NAME_LENGTH);
            }
            if (!name.matches(ALLOWED_NAME_CHARACTERS)) {
                log.error(ALLOWED_NAME_CHARACTERS);
                errors.put("accountName", ALLOWED_NAME_CHARACTERS);
            }
        }

        //Account Identifier validation
        if (identifier == null || identifier.trim().isEmpty()) {
            this.identifier = UUID.randomUUID().toString();
        } else {
            String id = identifier.trim();
            if (id.length() < IDENTIFIER_MIN_LENGTH || id.length() > IDENTIFIER_MAX_LENGTH) {
                log.error(INVALID_IDENTIFIER_LENGTH);
                errors.put("identifier", INVALID_IDENTIFIER_LENGTH);
            }
            if (!id.matches(ALLOWED_IDENTIFIER_CHARACTERS)) {
                log.error(ALLOWED_IDENTIFIER_CHARACTERS);
                errors.put("identifier", ALLOWED_IDENTIFIER_CHARACTERS);
            }
        }

        //Tags Validation
        if (tags != null && !tags.isEmpty()) {
            int index = 0;
            for (Tags tag : tags) {
                if (tag.getName() == null || tag.getName().trim().isEmpty()) {
                    String message = EMPTY_TAG_NAME + " at index " + index;
                    log.error(message);
                    errors.put("tagRequests[" + index + "].name", EMPTY_TAG_NAME);
                }
                if (tag.getIdentifier() == null || tag.getIdentifier().trim().isEmpty()) {
                    String message = EMPTY_TAG_IDENTIFIER + " at index " + index;
                    log.error(message);
                    errors.put("tagRequests[" + index + "].identifier", EMPTY_TAG_IDENTIFIER);
                }
                index++;
            }
        }
        return errors;
    }
}