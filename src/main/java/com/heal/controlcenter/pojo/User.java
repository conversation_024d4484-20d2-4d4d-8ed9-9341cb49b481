package com.heal.controlcenter.pojo;

import com.heal.configuration.pojos.NotificationChoice;
import com.heal.configuration.pojos.UserTimezone;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class User implements Serializable {

    private static final long serialVersionUID = 1L;

    private int userAttributesId;
    private String userDetailsId;
    private String userName;
    private String firstName;
    private String lastName;
    private String emailId;
    private String contactNumber;
    private String toEmails;
    private String ccEmails;
    private int recipientsEnabled;
    private int status;
    private int roleId;
    private int profileId;
    private int profileChange;
    private int emailEnabled;
    private int smsEnabled;
    private int forensicEnabled;
    private int forensicSuppression;
    private int notificationPreferenceId;
    private String lastForensicNotificationSentTime;
    private boolean isTimezoneMychoice;
    private boolean isNotificationsTimezoneMychoice;
    private String lastLoginTime;
    private String createdTime;
    private String updatedTime;
    private UserTimezone timezoneDetail;
    private NotificationChoice notificationChoice;
    private int alertTypeId;
}
