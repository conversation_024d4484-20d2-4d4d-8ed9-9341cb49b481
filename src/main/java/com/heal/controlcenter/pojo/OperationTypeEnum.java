package com.heal.controlcenter.pojo;

public enum OperationTypeEnum {

    GREATER("greater than"),
    LESS_THAN("lesser than"),
    IN_BETWEEN("in between"),
    NOT_BETWEEN("not between"),
    NOT_EQUALS("not equals");

    protected String type;

    OperationTypeEnum(String type){
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public static OperationTypeEnum findByType(String abbr) {
        for (OperationTypeEnum v : values()) {
            if (v.getType().equals(abbr)) {
                return v;
            }
        }
        return null;
    }
}
