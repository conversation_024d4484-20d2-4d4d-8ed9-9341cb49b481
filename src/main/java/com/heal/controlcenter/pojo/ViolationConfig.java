package com.heal.controlcenter.pojo;

import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.util.StringUtils;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR> : 1/3/19
 */
@Data
@Builder
public class ViolationConfig {
    private int id;
    private String profileName;
    private int profileId;
    private int status;
    private String operation;
    private String minThreshold;
    private String maxThreshold;
    private String startTime;
    private String endTime;
    private String applicableTo;
    private int generateAnomaly;
    private String definedBy;
    private int severity;
    
    public void validate() throws HealControlCenterException {
        if (StringUtils.isEmpty(this.profileName)) 
            throw new HealControlCenterException("Profile name can not be null or empty");
        if (StringUtils.isEmpty(this.operation)) 
            throw new HealControlCenterException("operation can not be null or empty");
        try {
            Float.valueOf(this.minThreshold);
            Float.valueOf(this.maxThreshold);
        } catch (Exception e) {
            throw new HealControlCenterException(e.getMessage());
        }
    }
}
