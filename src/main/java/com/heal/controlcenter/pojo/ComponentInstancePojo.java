package com.heal.controlcenter.pojo;


import com.appnomic.appsone.common.util.ResourceBundleUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.StringUtils;
import com.heal.controlcenter.util.UIMessages;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Data
public class ComponentInstancePojo {
    private int id;
    private String name;
    private String identifier;
    private String componentName;
    private String componentVersion;
    private String commonVersion;
    private List<String> serviceIdentifiers;
    private String parentIdentifier;
    private String parentName;
    private List<String> agentIdentifiers;
    private int discovery;
    private List<Attributes> attributes;
    private int supervisorId;
    private String instanceName;
    private String environment;

    private Map<String, String> error = new HashMap<>();

    public void validate() {
        if (StringUtils.isEmpty(name)) {
            error.put("name", "Name is empty");
        }
        if (StringUtils.isEmpty(identifier)) {
            error.put("identifier", "Identifier is empty for " + name);
        }

        if (this.name != null && !this.name.matches(Constants.ALLOWED_NAME_CHARACTERS)) {
            error.put("name_characters", ResourceBundleUtils.getString(UIMessages.ALLOWED_NAME_CHARACTERS, "Instance name"));
        }

        if (this.identifier != null && !this.identifier.matches(Constants.ALLOWED_IDENTIFIER_CHARACTERS)) {
            error.put("identifier_characters", ResourceBundleUtils.getString(UIMessages.ALLOWED_IDENTIFIER_CHARACTERS, "Instance identifier"));
        }

        if (StringUtils.isEmpty(componentName)) {
            error.put("componentName", "Component Name is empty for " + name);
        }
        if (StringUtils.isEmpty(componentVersion)) {
            error.put("componentVersion", "Component Version is empty for " + name);
        }
        if (serviceIdentifiers == null || serviceIdentifiers.isEmpty()) {
            error.put("serviceIdentifier", "Service Identifier is empty for " + name + " ");
        }
        if (environment != null && environment.trim().length() == 0) {
            error.put("environment", "Environment name is empty for " + name);
        }
    }

    public void updateValidate() {
        if (attributes == null && serviceIdentifiers == null) {
            error.put("body", "Both attributes and serviceIdentifiers is empty. Specify either of them or both");
        }

        if (attributes != null && attributes.size() == 0) {
            error.put("attributes", "attributes is empty");
        }

        if (serviceIdentifiers != null && serviceIdentifiers.size() == 0) {
            error.put("attributes", "serviceIdentifiers is empty");
        }

        if (name != null || componentName != null || componentVersion != null || commonVersion != null
                || parentIdentifier != null || parentName != null || agentIdentifiers != null) {
            error.put("attributes", "only attributes and service identifiers can be updated");
        }
    }


    @Override
    public int hashCode() {
        return 1;
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof ComponentInstancePojo))
            return false;

        ComponentInstancePojo instance = (ComponentInstancePojo) obj;
        return ((instance.identifier != null && instance.identifier.equals(identifier)) ||
                (instance.identifier == null && (instance.name != null && instance.name.equals(name))));
    }
}
