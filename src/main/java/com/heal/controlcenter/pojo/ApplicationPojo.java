package com.heal.controlcenter.pojo;

import com.heal.configuration.pojos.ParentApplication;
import com.heal.configuration.pojos.Tags;
import com.heal.controlcenter.beans.TimezoneBean;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationPojo {
    private int id;
    private String name;
    private String identifier;
    private String environment;
    private long lastModifiedOn;
    private String lastModifiedBy;
    private List<ServiceClusterDetails> services;
    private List<Tags> tags;
    private long timezoneMilli;
    private String timeZoneString;
}
