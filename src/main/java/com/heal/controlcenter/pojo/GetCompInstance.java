package com.heal.controlcenter.pojo;

import com.appnomic.appsone.common.enums.DiscoveryStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.heal.controlcenter.beans.AgentDetails;
import com.heal.controlcenter.beans.IdNamePojo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GetCompInstance {

    private String instanceId;
    private String instanceName;
    private String instanceIdentifier;
    private String hostId;
    private String hostName;
    private String hostIdentifier;
    private List<String> hostAddress;
    private int componentId;
    private String componentName;
    private int componentVersionId;
    private String componentVersionName;
    private int commonVersionId;
    private String commonVersionName;
    private int componentTypeId;
    private String componentTypeName;
    private List<IdNamePojo> application;
    private List<IdNamePojo> service;
    private List<AgentDetails> mappedAgents;
    private List<AttributeNameValue> port;
    private DiscoveryStatus status;
    private String process;
    private String environment;
    private long lastDiscoveryRunTime;
    @JsonIgnore
    private DiscoveryStatus hostStatus;

    @Data
    @Builder
    public static class AttributeNameValue {

        String attributeName;
        String attributeValue;

    }
}

