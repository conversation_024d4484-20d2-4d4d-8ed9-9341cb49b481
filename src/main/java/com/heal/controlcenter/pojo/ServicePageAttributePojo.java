package com.heal.controlcenter.pojo;

import com.heal.controlcenter.enums.AttributeSelectionType;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ServicePageAttributePojo {

    private int attributeId;
    private Object attributeValue;
    private String attributeName;
    private String attributeKey;
    private String lookupKey;
    private String displayKey;
    private String dataKey;
    private String dataUrl;
    private AttributeProperties properties;

    public ServicePageAttributePojo(int id, Object value, String name, String key, String lookupKey, String displayKey,
                                    String dataKey, String dataUrl, AttributeProperties properties){
        this.attributeId = id;
        this.attributeValue = value;
        this.attributeName = name;
        this.attributeKey = key;
        this.properties = properties;
        this.lookupKey = lookupKey;
        this.displayKey = displayKey;
        this.dataKey = dataKey;
        this.dataUrl = dataUrl;
    }


    @Data
    @Builder
    public static class AttributeProperties {

        private int min;
        private int max;
        private Object options;
        private String pattern;
        private AttributeSelectionType type;
        private int required;
        private int disabled;
        private int multiSelect;
        private int allowCreate;
        private String errorMessage;

        public AttributeProperties(int min, int max, Object options, String pattern, AttributeSelectionType type,
                                   int required , int disabled, int multiSelect, int allowCreate, String errorMessage) {
            this.min = min;
            this.max = max;
            this.options = options;
            this.pattern = pattern;
            this.type = type;
            this.required = required;
            this.disabled = disabled;
            this.multiSelect = multiSelect;
            this.allowCreate = allowCreate;
            this.errorMessage = errorMessage;
        }
    }

}
