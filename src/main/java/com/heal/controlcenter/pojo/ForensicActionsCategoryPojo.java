package com.heal.controlcenter.pojo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ForensicActionsCategoryPojo {

    private int id;
    private String name;
    private String identifier;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private int actionId;
    private String action;
    private String custom;

}
