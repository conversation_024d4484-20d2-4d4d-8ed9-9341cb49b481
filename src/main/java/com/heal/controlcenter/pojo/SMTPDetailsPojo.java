package com.heal.controlcenter.pojo;

import com.heal.controlcenter.util.UIMessages;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SMTPDetailsPojo {

    private int id;
    private String address;
    private int port;
    private String username;
    private String password;
    private String security;
    private String fromRecipient;

    private final int FIELD_LENGTH = 256;
    private final String SMTP_INVALID_ADDRESS = "Address is either NULL, empty or its length is greater than 256 characters.";
    private final String INVALID_SMTP_SECURITY_TYPE = "SMTP security type is either NULL or empty. It should be one of TLS, SSL or NONE.";
    private final String INVALID_SMTP_FROM_RECEIPT = "SMTP from recipient is either NULL, empty or its length is more than 256 characters.";
    private final String INVALID_SMTP_USERNAME = "SMTP username length is more than 256 characters.";
    private final String INVALID_SMTP_PWD = "SMTP password length is more than 256 characters.";
    private final String INVALID_PORT = "Port is invalid. It should be an integer greater than 0.";

    public Map<String, String> validate() {
        Map<String, String> error = new HashMap<>();

        if (address.isEmpty() || address.length() > FIELD_LENGTH) {
            log.error(SMTP_INVALID_ADDRESS);
            error.put("SMTP address", SMTP_INVALID_ADDRESS);
        }

        if (port <= 0) {
            log.error(INVALID_PORT);
            error.put("SMTP port", INVALID_PORT);
        }

        if (security.isEmpty()) {
            log.error(INVALID_SMTP_SECURITY_TYPE);
            error.put("SMTP security type", INVALID_SMTP_SECURITY_TYPE);
        }

        if (fromRecipient.isEmpty() || fromRecipient.length() > FIELD_LENGTH) {
            log.error(INVALID_SMTP_FROM_RECEIPT);
            error.put("SMTP recipient", INVALID_SMTP_FROM_RECEIPT);
        }

        if (!username.isEmpty() && username.length() > FIELD_LENGTH) {
            log.error(INVALID_SMTP_USERNAME);
            error.put("SMTP username", INVALID_SMTP_USERNAME);
        }

        if (!password.isEmpty() && password.length() > FIELD_LENGTH) {
            log.error(INVALID_SMTP_PWD);
            error.put("SMTP password", INVALID_SMTP_PWD);
        }
        return error;
    }
}
