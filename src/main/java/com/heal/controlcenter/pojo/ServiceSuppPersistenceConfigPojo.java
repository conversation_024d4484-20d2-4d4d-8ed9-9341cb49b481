package com.heal.controlcenter.pojo;

import com.heal.controlcenter.beans.ServiceSuppPersistenceConfigurationBean;
import com.heal.controlcenter.exception.HealControlCenterException;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceSuppPersistenceConfigPojo {
    private int serviceConfigId;
    private int lowPersistence;
    private int lowSuppression;
    private int mediumSuppression;
    private int mediumPersistence;
    private int highPersistence;
    private int highSuppression;
    private boolean lowEnable;
    private boolean mediumEnable;
    private boolean highEnable;
    private int closingWindow;
    private int maxDataBreaks;

    public void checkIfServiceConfigExists(List<ServiceSuppPersistenceConfigurationBean> list) throws HealControlCenterException {
        if (list.stream().noneMatch(s -> s.getId() == this.getServiceConfigId())) {
            throw new HealControlCenterException("Invalid ServiceConfigId : " + this.getServiceConfigId());
        }
    }

    public void validate() throws HealControlCenterException {
        if (this.lowEnable && (this.getLowPersistence() < 1 || this.getLowPersistence() > 120)) {
            throw new HealControlCenterException("Low Persistence should be in range 1-120. Invalid value : " + this.getLowPersistence());
        }
        if (this.lowEnable && (this.getLowSuppression() < 1 || this.getLowSuppression() > 120)) {
            throw new HealControlCenterException("Low Suppression should be in range 1-120. Invalid value : " + this.getLowSuppression());
        }
        if (this.mediumEnable && (this.getMediumPersistence() < 1 || this.getMediumPersistence() > 120)) {
            throw new HealControlCenterException("Medium Persistence should be in range 1-120. Invalid value : " + this.getMediumPersistence());
        }
        if (this.mediumEnable && (this.getMediumSuppression() < 1 || this.getMediumSuppression() > 120)) {
            throw new HealControlCenterException("Medium Suppression should be in range 1-120. Invalid value : " + this.getMediumSuppression());
        }
        if (this.highEnable && (this.getHighPersistence() < 1 || this.getHighPersistence() > 120)) {
            throw new HealControlCenterException("High Persistence should be in range 1-120. Invalid value : " + this.getHighPersistence());
        }
        if (this.highEnable && (this.getHighSuppression() < 1 || this.getHighSuppression() > 120)) {
            throw new HealControlCenterException("High Suppression should be in range 1-120. Invalid value : " + this.getHighSuppression());
        }
    }
}
