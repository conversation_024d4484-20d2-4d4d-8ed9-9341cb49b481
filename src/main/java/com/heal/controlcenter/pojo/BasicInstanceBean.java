package com.heal.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Basic instance bean - equivalent to BasicInstanceBean from appsone-controlcenter.
 * Used for storing basic instance information for service-level operations.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BasicInstanceBean {
    private int id;
    private String name;
    private String identifier;
    private int status;
    private String createdTime;
    private String updatedTime;
    private String lastModifiedBy;
    private int componentId;
    private String componentName;
    private int componentTypeId;
    private String componentTypeName;
    private int componentVersionId;
    private String componentVersionName;
    private int commonVersionId;
    private String commonVersionName;
    private int supervisorId;
    private String hostAddress;
    private int isDR;
    private int discovery;
    private List<String> agentIds;
    private int accountId;
    private boolean isCluster;
    private String clusterIdentifier;
}
