package com.heal.controlcenter.pojo;

import com.heal.configuration.pojos.ViewTypes;
import com.heal.controlcenter.dao.mysql.entity.InstanceKpiAttributeThresholdBean;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class ThresholdValidationContext {
    private List<Integer> instances;
    private List<InstanceLevelKpiAttributeThreshold> thresholds;
    private int accountId;
    private String accountIdentifier;
    private String userDetails;
    private SeverityTypes severityTypes;
    private Map<String, List<ViewTypes>> allViewTypesMap;
    private Map<Integer, List<InstanceKpiAttributeThresholdBean>> groupKpiMap;
    private Map<Integer, List<InstanceKpiAttributeThresholdBean>> nonGroupKpiMap;
    private Map<Integer, List<InstanceKpiAttributeThresholdBean>> existingThresholdMap;
}
