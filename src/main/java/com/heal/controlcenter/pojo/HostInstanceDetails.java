package com.heal.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Host instance details POJO - exact match to appsone-controlcenter BindInDataService.getHostInstanceId return type.
 * Original JDBI query returns: hostInstanceId, hostInstanceName, isDR
 * Used for storing host instance information in Redis operations.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HostInstanceDetails {
    private int hostInstanceId;
    private String hostInstanceName;
    private int isDR;
}
