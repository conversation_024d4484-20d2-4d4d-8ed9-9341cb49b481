package com.heal.controlcenter.pojo;

import lombok.Data;

@Data
public class AgentCommandArgumentsPojo {

    private String autoSnapshotCollectionLevel;
    private boolean autoSnapshotForException;
    private int autoSnapshotCount;
    private int autoSnapshotDuration;
    private int autoSnapshotSilentWindow;
    private int jvmCpuUtil;
    private int jvmMemUtil;
    private int verboseSnapshotCount;
    private int verboseSnapshotDuration;
    private int verboseSnapshotSilentWindow;

}
