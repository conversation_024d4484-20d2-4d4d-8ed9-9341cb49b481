package com.heal.controlcenter.pojo;

import com.heal.controlcenter.enums.Entity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AutoDiscoveryIgnoredEntitiesPojo {

    private String name;
    private String identifier;
    private List<String> hostAddress;
    private List<String> discoveredEntities;
    private Entity entityType;
    private String lastDiscoveryRunTime;
    private String lastUpdatedTime;
    private String ignoredBy;

}
