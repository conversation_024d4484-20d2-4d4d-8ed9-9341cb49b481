package com.heal.controlcenter.pojo;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class ConnectionDetailsPojo {
    private String sourceName;
    private String sourceServiceIdentifier;
    private String destinationName;
    private String destinationServiceIdentifier;
    private int isDiscovery;

    /**
     * Validates the fields of this ConnectionDetailsPojo.
     * Checks for null/empty service identifiers, ensures source and destination are not the same,
     * and validates the isDiscovery flag. Returns a map of errors if any validation fails.
     *
     * @return Map of field names to error messages, empty if valid.
     */
    public Map<String, String> validate() {
        Map<String, String> errors = new HashMap<>();
        log.debug("[validate] Validating ConnectionDetailsPojo: sourceServiceIdentifier={}, destinationServiceIdentifier={}, isDiscovery={}",
                sourceServiceIdentifier, destinationServiceIdentifier, isDiscovery);
        if (sourceServiceIdentifier == null || sourceServiceIdentifier.trim().isEmpty()) {
            errors.put("sourceServiceIdentifier", "Source Service Identifier cannot be empty or null.");
        }
        if (destinationServiceIdentifier == null || destinationServiceIdentifier.trim().isEmpty()) {
            errors.put("destinationServiceIdentifier", "Destination Service Identifier cannot be empty or null.");
        }
        if (sourceServiceIdentifier != null && destinationServiceIdentifier != null && sourceServiceIdentifier.equals(destinationServiceIdentifier)) {
            errors.put("serviceIdentifiers", "Source and Destination Service Identifiers cannot be the same.");
        }
        if (isDiscovery != 0 && isDiscovery != 1) {
            errors.put("isDiscovery", "isDiscovery must be 0 or 1.");
        }
        if (errors.isEmpty()) {
            log.debug("[validate] ConnectionDetailsPojo is valid.");
        } else {
            log.warn("[validate] Validation errors found: {}", errors);
        }
        return errors;
    }
}
