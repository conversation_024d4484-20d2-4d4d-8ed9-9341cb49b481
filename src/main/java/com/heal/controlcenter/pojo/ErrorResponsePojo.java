package com.heal.controlcenter.pojo;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.http.HttpStatus;

import java.time.Instant;
import java.util.List;

/**
 * Error response structure for API error responses.
 * This matches the structure created by the global ExceptionHandler.
 */
@JsonPropertyOrder({"message", "data", "responseStatus"})
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ErrorResponsePojo {
    
    private String message;
    private ErrorData data;
    private HttpStatus responseStatus;
    
    @JsonPropertyOrder({"timestamp", "status", "error", "type", "path", "message"})
    @AllArgsConstructor
    @NoArgsConstructor
    @Getter
    @Setter
    public static class ErrorData {
        private Instant timestamp;
        private int status;
        private List<String> error;
        private String type;
        private String path;
        private String message;
    }
}
