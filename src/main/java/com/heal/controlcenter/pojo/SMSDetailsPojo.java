package com.heal.controlcenter.pojo;

import com.heal.controlcenter.util.UIMessages;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SMSDetailsPojo {

    private int id;
    private String address;
    private int port;
    private String countryCode;
    private String protocolName;
    private String httpMethod;
    private String httpRelativeUrl;
    private String postData;
    private int isMultiRequest = 0;
    private List<SMSParameterPojo> parameters;

    private final int ADDRESS_LENGTH = 128;
    private final int COUNTRY_CODE_LENGTH = 32;
    private final int HTTP_URL_LENGTH = 512;
    private final String HTTP = "HTTP";

    private final String INVALID_ADDRESS = "Address is either NULL, empty or its length is greater than 128 characters.";
    private final String INVALID_COUNTRY_CODE = "Country code length is greater than 32 characters.";
    private final String INVALID_SMS_PROTOCOL = "Protocol is either NULL or empty. It should be one of HTTP, HTTPS or TCP.";
    private final String INVALID_HTTP_METHOD = "HTTP method is either NULL or empty. It should be either GET or POST.";
    private final String INVALID_HTTP_URL = "HTTP URL is either NULL, empty or its length is greater than 512 characters.";
    private final String INVALID_STATUS = "Multi request is invalid. Value should be either 0 or 1.";
    private final String DUPLICATE_SMS_PARAMETER = "Duplicate SMS parameter.";
    private final String INVALID_PORT = "Port is invalid. It should be an integer greater than 0.";

    public Map<String, String> validate() {
        Map<String, String> error = new HashMap<>();
        if (address.isEmpty() || address.length() > ADDRESS_LENGTH) {
            log.error(INVALID_ADDRESS);
            error.put("SMS address", INVALID_ADDRESS);
        }

        if (port <= 0) {
            log.error(INVALID_PORT);
            error.put("SMS Port", INVALID_PORT);
        }

        if (!countryCode.isEmpty() && countryCode.length() > COUNTRY_CODE_LENGTH) {
            log.error(INVALID_COUNTRY_CODE);
            error.put("SMS country", INVALID_COUNTRY_CODE);
        }

        if (protocolName.isEmpty()) {
            log.error(INVALID_SMS_PROTOCOL);
            error.put("SMS protocol", INVALID_SMS_PROTOCOL);
        }

        if (HTTP.equalsIgnoreCase(protocolName) && httpMethod.isEmpty()) {
            log.error(INVALID_HTTP_METHOD);
            error.put("SMS http method", INVALID_HTTP_METHOD);
        }

        if (HTTP.equalsIgnoreCase(protocolName) && (!httpRelativeUrl.isEmpty() && httpRelativeUrl.length() > HTTP_URL_LENGTH)) {
            log.error(INVALID_HTTP_URL);
            error.put("SMS http URL", INVALID_HTTP_URL);
        }

        if (isMultiRequest < 0 || isMultiRequest > 1) {
            log.error(INVALID_STATUS);
            error.put("Multi request", INVALID_STATUS);
        }

        for (SMSParameterPojo smsParameter : parameters) {
            Map<String, String> smsParameterError = smsParameter.validate();
            if (!smsParameterError.isEmpty()) {
                error.putAll(smsParameterError);
            }
        }

        Set<SMSParameterPojo> parameterSet = new HashSet<>(parameters);
        if (parameterSet.size() < parameters.size()) {
            log.error(DUPLICATE_SMS_PARAMETER);
            error.put("SMS parameters", DUPLICATE_SMS_PARAMETER);
        }
        return error;
    }
}
