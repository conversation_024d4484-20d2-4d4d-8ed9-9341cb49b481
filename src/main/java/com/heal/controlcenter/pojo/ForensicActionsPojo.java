package com.heal.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ForensicActionsPojo {

    private int id;
    private String name;
    private String type;
    private String commandName;
    private String commandIdentifier;
    private String commandTimeoutInSeconds;
    private String supCtrlTimeoutInSeconds;
    private String supCtrlRetryCount;

    private List<ForensicActionsCategoryPojo>  categoryList;
    private List<ForensicActionsParametersPojo> parameters;

    private int status;
    private String lastModifiedBy;
    private String lastModifiedOn;

}
