package com.heal.controlcenter.pojo;

import com.heal.controlcenter.dao.mysql.entity.KpiCategoryDetails;
import com.heal.controlcenter.enums.ClusterOperation;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class ComponentKpiConfig {
    private String name;
    private int id;
    private String kpiType;
    private Boolean isGroup;
    private String groupName;
    private int groupId;
    private String kpiUnit;
    private ClusterOperation aggOperation;
    private ClusterOperation rollUpOperation;
    private String clusterAggType;
    private String instanceAggType;
    private String aliasName;
    private int availableForAnalytics;
    private Map<String, List<KpiViolationConfig>> kpiViolationConfigMap = new HashMap<>();
    private KpiCategoryDetails categoryDetails;
    private String valueType;
    private String dataType;
    private int isInfo;
    private int resetDeltaValue;
    private int deltaPerSec;
    private String cronExpression;
}
