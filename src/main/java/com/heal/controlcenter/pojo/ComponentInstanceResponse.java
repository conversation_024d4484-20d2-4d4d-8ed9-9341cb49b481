package com.heal.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Response POJO for component instance creation.
 * Contains the created component instance details and any related information.
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ComponentInstanceResponse {

    private int id;
    private String name;
    private String identifier;
    private String hostAddress;
    private String componentName;
    private String componentType;
    private String componentVersion;
    private String commonVersion;
    private String status;
    private String createdTime;
    private String updatedTime;
    private List<String> serviceIdentifiers;
    private List<String> agentIdentifiers;
    private List<Tags> tags;
    private String parentIdentifier;
    private String clusterName;
    private boolean isCluster;
    private boolean isDR;
    private int discovery;
    private int supervisorId;
    private boolean isKubernetes;
    private boolean isPod;
}
