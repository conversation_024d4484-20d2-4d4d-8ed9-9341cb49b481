package com.heal.controlcenter.pojo;

import com.heal.controlcenter.dao.mysql.entity.InstanceKpiAttributeThresholdBean;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class InstanceProcessingData {
    private int instanceId;
    private List<InstanceKpiAttributeThresholdBean> groupKpis;
    private List<InstanceKpiAttributeThresholdBean> nonGroupKpis;
    private List<InstanceKpiAttributeThresholdBean> existingThresholds;
    private Map<KpiIdVsAttributeSeverityId, InstanceKpiAttributeThresholdBean> existingThresholdMap;
}
