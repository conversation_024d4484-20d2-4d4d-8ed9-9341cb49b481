package com.heal.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplicationAnomalyConfiguration {
    private int applicationId;
    private int accountId;
    private String userDetailsId;
    private String createdTime;
    private String updatedTime;
    private boolean lowEnable;
    private boolean mediumEnable;
    private boolean highEnable;
    private int closingWindow;
    private int maxDataBreaks;
}