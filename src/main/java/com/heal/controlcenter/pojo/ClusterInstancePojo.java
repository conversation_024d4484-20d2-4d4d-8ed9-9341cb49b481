package com.heal.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Cluster instance POJO - equivalent to ClusterInstancePojo from appsone-controlcenter.
 * Used for storing cluster instance mapping information in Redis operations.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClusterInstancePojo {
    private int clusterId;
    private String clusterName;
    private String clusterIdentifier;
    private int status;
    private String createdTime;
    private String updatedTime;
    private String lastModifiedBy;
    private int componentId;
    private int componentTypeId;
    private int componentVersionId;
    private int commonVersionId;
    private int supervisorId;
    private int hostId;
    private String hostAddress;
    private int isDR;
    private int discovery;
    private int parentInstanceId;
    private int accountId;
    private int isCluster;
}
