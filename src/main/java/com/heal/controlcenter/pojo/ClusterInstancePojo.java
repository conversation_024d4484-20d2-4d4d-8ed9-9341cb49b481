package com.heal.controlcenter.pojo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Cluster instance POJO - equivalent to ClusterInstancePojo from appsone-controlcenter.
 * Used for storing cluster instance mapping information in Redis operations.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClusterInstancePojo {
    private int clusterId;
    private String clusterIdentifier;
    private String clusterName;
    private int status;
    private int hostId;
    private int isDR;
    private int isCluster;
    private int componentVersionId;
    private String createdTime;
    private String updatedTime;
    private String lastModifiedBy;
    private int componentId;
    private int componentTypeId;
    private int discovery;
    private String hostAddress;
    private int commonVersionId;
    private int supervisorId;
    private int parentInstanceId;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer accountId;
}
