package com.heal.controlcenter.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.heal.controlcenter.beans.CommandDetailArgumentBean;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.util.DateTimeUtil;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

@Data
public class AgentCommandsPojo {

    private String commandName;
    private String commandMode;
    private String identifier;
    private AgentCommandArgumentsPojo arguments;
    private String lastUpdatedTime;
    private String serverTime;

    @JsonIgnore
    private Map<String, String> error = new HashMap<>();

    @JsonIgnore
    @Autowired
    MasterDataDao masterDataDao;
    @JsonIgnore
    @Autowired
    DateTimeUtil dateTimeUtil;
    @JsonIgnore
    @Autowired
    CommandDetailArgumentBean commandDetailArgumentBean;

    public void validate() {
        if (commandMode == null || commandMode.isEmpty()) {
            error.put("commandMode", "Command mode is empty");
        }

        if (arguments == null) {
            error.put("arguments", "Arguments are null");
        }
    }
}
