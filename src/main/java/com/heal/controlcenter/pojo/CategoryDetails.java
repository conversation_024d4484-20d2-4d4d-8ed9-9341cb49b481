package com.heal.controlcenter.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static com.heal.controlcenter.util.Constants.ALLOWED_IDENTIFIER_CHARACTERS;
import static com.heal.controlcenter.util.Constants.ALLOWED_NAME_CHARACTERS;

/**
 * POJO representing category details for API input and validation.
 * Includes builder support and validation logic.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CategoryDetails {
    private int id;
    private String name;
    private String identifier;
    @JsonIgnore
    private String description;
    private String type;
    private String subType;
    @JsonIgnore
    private int status;
    @JsonIgnore
    private int kpiCount;

    /**
     * Validates the fields of this CategoryDetails object.
     *
     * @return Map of field names to error messages, empty if valid.
     */
    public Map<String, String> validate() {
        final int CATEGORY_NAME_MIN_LENGTH = 2;
        final int CATEGORY_NAME_MAX_LENGTH = 128;
        final int IDENTIFIER_MIN_LENGTH = 2;
        final int IDENTIFIER_MAX_LENGTH = 64;

        final String EMPTY_CATEGORY_NAME = "Category name cannot be empty or null.";
        final String INVALID_CATEGORY_NAME_LENGTH = "Category name must be between 2 and 128 characters.";
        final String INVALID_IDENTIFIER_LENGTH = "Identifier must be between 2 and 64 characters.";

        Map<String, String> errors = new HashMap<>();

        // Category Name Validation
        if (name == null || name.trim().isEmpty()) {
            errors.put("categoryName", EMPTY_CATEGORY_NAME);
        } else {
            String catName = name.trim();
            if (catName.length() < CATEGORY_NAME_MIN_LENGTH || catName.length() > CATEGORY_NAME_MAX_LENGTH) {
                errors.put("categoryName", INVALID_CATEGORY_NAME_LENGTH);
            }
            if (!catName.matches(ALLOWED_NAME_CHARACTERS)) {
                errors.put("categoryName", "Category name contains invalid characters.");
            }
        }

        // Identifier validation
        if (identifier == null || identifier.trim().isEmpty()) {
            this.identifier = UUID.randomUUID().toString();
        } else {
            String id = identifier.trim();
            if (id.length() < IDENTIFIER_MIN_LENGTH || id.length() > IDENTIFIER_MAX_LENGTH) {
                errors.put("identifier", INVALID_IDENTIFIER_LENGTH);
            }
            if (!id.matches(ALLOWED_IDENTIFIER_CHARACTERS)) {
                errors.put("identifier", "Identifier contains invalid characters.");
            }
        }
        return errors;
    }
}
