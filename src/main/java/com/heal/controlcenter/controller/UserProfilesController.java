package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.UserProfileBean;
import com.heal.controlcenter.businesslogic.UserProfilesBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.swagger.CommonApiResponses;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> Suman - 19-10-2021
 */

@Slf4j
@RestController
public class UserProfilesController {

    private final UserProfilesBL userProfilesBL;
    private final JsonFileParser headersParser;

    public UserProfilesController(UserProfilesBL userProfilesBL, JsonFileParser headersParser) {
        this.userProfilesBL = userProfilesBL;
        this.headersParser = headersParser;
    }

    @Operation(
            summary = "Retrieves all available user profiles in the system.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "User profiles fetched successfully.",
                            content = @Content(schema = @Schema(implementation = UserProfileBean.class))
                    )
            }
    )
    @CommonApiResponses
    @RequestMapping(value = "/user-profiles", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<List<UserProfileBean>>> getUserProfiles(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader(value = "Authorization") String authorization)
            throws ClientException, DataProcessingException {

        userProfilesBL.clientValidation(null, authorization);
        List<UserProfileBean> listOfUserProfiles = userProfilesBL.process("User profiles");

        ResponsePojo<List<UserProfileBean>> responsePojo = new ResponsePojo<>("User profiles fetched successfully", listOfUserProfiles, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}
