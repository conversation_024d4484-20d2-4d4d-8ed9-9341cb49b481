package com.heal.controlcenter.controller;

import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.AccountKPIKey;
import com.heal.controlcenter.beans.CategoryDetailBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.PostCategoriesBL;
import com.heal.controlcenter.businesslogic.DeleteCategoriesBL;
import com.heal.controlcenter.businesslogic.GetCategoriesBL;
import com.heal.controlcenter.businesslogic.PutCategoriesBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.*;
import com.heal.controlcenter.swagger.CommonApiResponses;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Configuration
@RestController
public class CategoryController {

    private final PostCategoriesBL postCategoriesBL;
    private final PutCategoriesBL putCategoriesBL;
    private final GetCategoriesBL getCategoriesBL;
    private final DeleteCategoriesBL deleteCategoriesBL;
    private final JsonFileParser headersParser;

    public CategoryController(PostCategoriesBL postCategoriesBL, PutCategoriesBL putCategoriesBL, GetCategoriesBL getCategoriesBL, DeleteCategoriesBL deleteCategoriesBL, JsonFileParser headersParser) {
        this.postCategoriesBL = postCategoriesBL;
        this.putCategoriesBL = putCategoriesBL;
        this.getCategoriesBL = getCategoriesBL;
        this.deleteCategoriesBL = deleteCategoriesBL;
        this.headersParser = headersParser;
    }

    /**
     * Adds new categories for the specified account.
     * <p>
     * Validates the request, performs business logic for category creation, and returns the result.
     * </p>
     *
     * @param accountIdentifier Unique identifier of the account (from path variable)
     * @param body              List of CategoryDetails objects to be added (from request body)
     * @param userDetails       Authenticated user details (injected)
     * @return ResponseEntity containing a ResponsePojo with the list of IdPojo for the processed categories.
     */
    @Operation(
            summary = "Add categories for an account",
            description = "Validates and adds one or more categories for the specified account, including database persistence and Redis updates.",
            security = @io.swagger.v3.oas.annotations.security.SecurityRequirement(name = "bearerAuth"),
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "List of category details to add or update for the account.",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = CategoryDetails.class)
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Category(ies) added/updated successfully.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponsePojo.class)))
            }
    )
    @CommonApiResponses
    @AopCustomAnnotation
    @PostMapping("/accounts/{identifier}/categories")
    public ResponseEntity<ResponsePojo<List<IdPojo>>> addCategories(
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier,
            @Parameter(
                    description = "Category details to add (list)",
                    required = true
            )
            @RequestBody List<CategoryDetails> body,
            BasicUserDetails userDetails) {
        log.debug("Received request to add categories for accountIdentifier: {} with body: {}", accountIdentifier, body);
        try {
            UtilityBean<List<CategoryDetails>> categoryBean = postCategoriesBL.clientValidation(body, accountIdentifier);
            categoryBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
            UtilityBean<List<CategoryDetailBean>> validatedBean = postCategoriesBL.serverValidation(categoryBean);
            List<IdPojo> idPojoList = postCategoriesBL.process(validatedBean);
            ResponsePojo<List<IdPojo>> responsePojo = new ResponsePojo<>("Categories added successfully", idPojoList, HttpStatus.OK);
            log.info("Categories added successfully for accountIdentifier: {}. Created IDs: {}", accountIdentifier, idPojoList);
            return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Error occurred while adding categories for accountIdentifier: {}. Details: {}", accountIdentifier, e.getMessage(), e);
            return ResponseEntity.badRequest().body(new ResponsePojo<>(e.getMessage(), null, HttpStatus.BAD_REQUEST));
        } catch (Exception e) {
            log.error("Unexpected error occurred while POST addCategories for accountIdentifier: {}. Details: {}", accountIdentifier, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(new ResponsePojo<>(e.getMessage(), null, HttpStatus.INTERNAL_SERVER_ERROR));
        }
    }

    /**
     * Updates categories for the specified account.
     * <p>
     * Validates the request, performs business logic for category update, and returns the result.
     * </p>
     *
     * @param accountIdentifier Unique identifier of the account (from path variable)
     * @param body              List of CategoryDetails objects to be updated (from request body)
     * @param userDetails       Authenticated user details (injected)
     * @return ResponseEntity containing a ResponsePojo with the list of IdPojo for the processed categories.
     */
    @Operation(
            summary = "Update categories for an account",
            description = "Validates and updates one or more categories for the specified account, including database persistence and Redis updates.",
            security = @io.swagger.v3.oas.annotations.security.SecurityRequirement(name = "bearerAuth"),
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "List of category details to update for the account.",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = CategoryDetails.class)
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Category(ies) updated successfully.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponsePojo.class)))
            }
    )
    @CommonApiResponses
    @AopCustomAnnotation
    @PutMapping("/accounts/{identifier}/categories")
    public ResponseEntity<ResponsePojo<List<IdPojo>>> updateCategories(
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier,
            @Parameter(
                    description = "Category details to update (list)",
                    required = true
            )
            @RequestBody List<CategoryDetails> body,
            BasicUserDetails userDetails) {
        log.debug("Received request to update categories for accountIdentifier: {} with body: {}", accountIdentifier, body);
        try {
            UtilityBean<List<CategoryDetails>> categoryBean = putCategoriesBL.clientValidation(body, accountIdentifier);
            categoryBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
            UtilityBean<List<CategoryDetailBean>> validatedBean = putCategoriesBL.serverValidation(categoryBean);
            List<IdPojo> idPojoList = putCategoriesBL.process(validatedBean);
            ResponsePojo<List<IdPojo>> responsePojo = new ResponsePojo<>("Categories updated successfully", idPojoList, HttpStatus.OK);
            log.info("Categories updated successfully for accountIdentifier: {}. Updated IDs: {}", accountIdentifier, idPojoList);
            return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Error occurred while updating categories for accountIdentifier: {}. Details: {}", accountIdentifier, e.getMessage(), e);
            return ResponseEntity.badRequest().body(new ResponsePojo<>(e.getMessage(), null, HttpStatus.BAD_REQUEST));
        } catch (Exception e) {
            log.error("Unexpected error occurred while PUT updateCategories for accountIdentifier: {}. Details: {}", accountIdentifier, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(new ResponsePojo<>(e.getMessage(), null, HttpStatus.INTERNAL_SERVER_ERROR));
        }
    }

    /**
     * Gets categories for the specified account.
     * <p>
     * Validates the request, fetches the categories from the database, and returns the result.
     * </p>
     *
     * @param accountIdentifier Unique identifier of the account (from path variable)
     * @param userDetails       Authenticated user details (injected)
     * @return ResponseEntity containing a ResponsePojo with the list of CategoryDetails for the requested account.
     */
    @Operation(
            summary = "Get categories for an account",
            description = "Fetches all categories for the specified account with optional search, type, subType, kpiType filters and pagination.",
            security = @io.swagger.v3.oas.annotations.security.SecurityRequirement(name = "bearerAuth"),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Fetched categories successfully.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponsePojo.class)))
            }
    )
    @CommonApiResponses
    @AopCustomAnnotation
    @GetMapping("/accounts/{identifier}/categories")
    public ResponseEntity<ResponsePojo<Page<CategoryDetails>>> getCategories(
            @Parameter(name = "identifier", description = "Unique identifier of the account", required = true, example = "account-001")
            @PathVariable("identifier") String accountIdentifier,
            @RequestParam(value = "searchTerm", required = false) String searchTerm,
            @RequestParam(value = "type", required = false) String type,
            @RequestParam(value = "subType", required = false) String subType,
            @RequestParam(value = "kpiType", required = false) String kpiType,
            @Parameter(
                    description = "Pagination and sorting information. " +
                            "Use `page` (0-indexed), `size` (items per page), and `sort` (property[,asc|desc]). " +
                            "Example: `?page=0&size=10&sort=accountName,asc`"
            )
            Pageable pageable,
            BasicUserDetails userDetails) {
        log.debug("Received request to get categories for accountIdentifier: {}, searchTerm: {}, type: {}, subType: {}, kpiType: {}", accountIdentifier, searchTerm, type, subType, kpiType);
        try {
            UtilityBean<String> validatedBean = getCategoriesBL.clientValidation(null, accountIdentifier, searchTerm, type, subType, kpiType);
            validatedBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
            validatedBean.setPageable(pageable);
            UtilityBean<AccountKPIKey> accountKPIKeyBean = getCategoriesBL.serverValidation(validatedBean);
            Page<CategoryDetails> categories = getCategoriesBL.process(accountKPIKeyBean);
            ResponsePojo<Page<CategoryDetails>> responsePojo = new ResponsePojo<>("Fetched categories successfully", categories, HttpStatus.OK);
            log.info("Fetched categories successfully for accountIdentifier: {}.", accountIdentifier);
            return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Error occurred while fetching categories for accountIdentifier: {}. Details: {}", accountIdentifier, e.getMessage(), e);
            return ResponseEntity.badRequest().body(new ResponsePojo<>(e.getMessage(), null, HttpStatus.BAD_REQUEST));
        } catch (Exception e) {
            log.error("Unexpected error occurred while GET getCategories for accountIdentifier: {}. Details: {}", accountIdentifier, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(new ResponsePojo<>(e.getMessage(), null, HttpStatus.INTERNAL_SERVER_ERROR));
        }
    }

    /**
     * Deletes categories for the specified account.
     * <p>
     * Validates the request, performs business logic for category deletion (hard/soft),
     * and updates the Redis cache. Returns a success message if deletion is successful.
     * </p>
     *
     * @param accountIdentifier Unique identifier of the account (from path variable)
     * @param body              DeleteCategoriesPojo containing identifiers and delete type (from request body)
     * @param userDetails       Authenticated user details (injected)
     * @return ResponseEntity with success or error message
     */
    @Operation(
            summary = "Deletes categories for the specified account.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Categories removed successfully.",
                            content = @Content(schema = @Schema(implementation = ResponsePojo.class)))
            }
    )
    @CommonApiResponses
    @AopCustomAnnotation
    @DeleteMapping("/accounts/{identifier}/categories")
    public ResponseEntity<ResponsePojo<String>> deleteCategories(
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier,
            @Parameter(
                    description = "Category details to delete (list)",
                    required = true
            )
            @RequestBody DeleteCategoriesPojo body,
            BasicUserDetails userDetails) {
        log.trace("[deleteCategories] Method Invoked for accountIdentifier: {} with body: {}", accountIdentifier, body);
        try {
            UtilityBean<DeleteCategoriesPojo> categoryList = deleteCategoriesBL.clientValidation(body, accountIdentifier);
            log.debug("[deleteCategories] After clientValidation: {}", categoryList);
            categoryList.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
            UtilityBean<List<Integer>> categoryIdList = deleteCategoriesBL.serverValidation(categoryList);
            log.debug("[deleteCategories] After serverValidation: {}", categoryIdList);
            String message = deleteCategoriesBL.process(categoryIdList);
            log.info("[deleteCategories] Categories removed successfully for accountIdentifier: {}", accountIdentifier);
            ResponsePojo<String> responsePojo = new ResponsePojo<>(message, null, HttpStatus.OK);
            return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("[deleteCategories] Error occurred while deleting category for accountIdentifier: {}. Details: {}", accountIdentifier, e.getMessage(), e);
            return ResponseEntity.badRequest().body(new ResponsePojo<>(e.getMessage(), null, HttpStatus.BAD_REQUEST));
        } catch (Exception e) {
            log.error("[deleteCategories] Unexpected error occurred while DELETE deleteCategories for accountIdentifier: {}. Details: {}", accountIdentifier, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(new ResponsePojo<>(e.getMessage(), null, HttpStatus.INTERNAL_SERVER_ERROR));
        }
    }
}
