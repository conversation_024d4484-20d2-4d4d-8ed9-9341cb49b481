package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.businesslogic.GetEntityCountBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.swagger.CommonApiResponses;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@Slf4j
public class EntityCountController {

    private final GetEntityCountBL getEntityCountBL;
    private final JsonFileParser headersParser;

    public EntityCountController(GetEntityCountBL getEntityCountBL, JsonFileParser headersParser) {
        this.getEntityCountBL = getEntityCountBL;
        this.headersParser = headersParser;
    }

    @Operation(
            summary = "Retrieves entity count for the specified account and type.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Entity count fetched successfully.",
                            content = @Content(schema = @Schema(implementation = Map.class))
                    )
            }
    )
    @CommonApiResponses
    @RequestMapping(value ="/accounts/{identifier}/entity-count", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<Map<String, Object>>> getEntityCount(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader("Authorization") String authorization,
            @Parameter(
                    name = "type",
                    description = "Type of entity to count (e.g., SERVICE, APPLICATION)",
                    required = true,
                    example = "SERVICE"
            )
            @RequestParam("type") String typeName,
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : getEntityCount");

        UtilityBean<InstancesBean> utilityBean = getEntityCountBL.clientValidation(null, authorization, accountIdentifier, typeName);
        InstancesBean bean = getEntityCountBL.serverValidation(utilityBean);
        Map<String, Object> data = getEntityCountBL.process(bean);

        ResponsePojo<Map<String, Object>> responsePojo = new ResponsePojo<>("Entity count fetched successfully.", data, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}
