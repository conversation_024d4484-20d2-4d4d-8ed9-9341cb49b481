package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.TimezoneBean;
import com.heal.controlcenter.businesslogic.TimeZoneBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.swagger.CommonApiResponses;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Slf4j
public class TimeZoneController {

    private final TimeZoneBL timeZoneBL;
    private final JsonFileParser headersParser;

    public TimeZoneController(TimeZoneBL timeZoneBL, JsonFileParser headersParser) {
        this.timeZoneBL = timeZoneBL;
        this.headersParser = headersParser;
    }

    @Operation(
            summary = "Retrieves all available timezones.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Timezones fetched successfully.",
                            content = @Content(schema = @Schema(implementation = TimezoneBean.class))
                    )
            }
    )
    @CommonApiResponses
    @RequestMapping(value = "/timezones", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<List<TimezoneBean>>> getAllTimezones(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader(value = "Authorization") String authorization)
            throws ClientException, DataProcessingException {
        timeZoneBL.clientValidation(null, authorization);
        List<TimezoneBean> listOfTimeZones = timeZoneBL.process("Timezones");

        ResponsePojo<List<TimezoneBean>> responsePojo = new ResponsePojo<>("Timezones fetched successfully", listOfTimeZones, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}
