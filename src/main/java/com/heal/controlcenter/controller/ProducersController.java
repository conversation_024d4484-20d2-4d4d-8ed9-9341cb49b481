package com.heal.controlcenter.controller;

import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.ProducerValidationBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetProducersBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.GetProducerPojo;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.swagger.CommonApiResponses;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.HealthMetrics;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

@Slf4j
@RestController
public class ProducersController {
    private final GetProducersBL getProducersBL;
    private final HealthMetrics healthMetrics;
    private final JsonFileParser headersParser;

    public ProducersController(GetProducersBL getProducersBL, HealthMetrics healthMetrics, JsonFileParser headersParser) {
        this.getProducersBL = getProducersBL;
        this.healthMetrics = healthMetrics;
        this.headersParser = headersParser;
    }

    /**
     * Retrieves a list of producers for a given account.
     *
     * @param accountIdentifier The identifier of the account.
     * @param pageable          Pagination and sorting information.
     * @return A ResponseEntity containing a list of producers.
     */
    @Operation(summary = "Retrieves all Producers for the authenticated user.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Successfully retrieved producers",
                            content = @Content(schema = @Schema(implementation = GetProducerPojo.class))
                    )
            }
    )
    @CommonApiResponses
    @AopCustomAnnotation
    @GetMapping("/accounts/{accountIdentifier}/producers")
    public ResponseEntity<ResponsePojo<Page<GetProducerPojo>>> getProducers(@PathVariable(value = "accountIdentifier") String accountIdentifier,
                                                                            @RequestParam(value = "name", required = false) String name,
                                                                            @RequestParam(value = "status", required = false) String status,
                                                                            @RequestParam(value = "producerType", required = false) String producerType,
                                                                            @RequestParam(value = "isCustom") int isCustom,
                                                                            Pageable pageable, BasicUserDetails userDetails) {
        log.trace("GetProducers API called with account identifier: {}", accountIdentifier);
        try {
            UtilityBean<String> clientBean = getProducersBL.clientValidation(null, accountIdentifier, name,
                    status, producerType, String.valueOf(isCustom));
            clientBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
            clientBean.setPageable(pageable);
            UtilityBean<ProducerValidationBean> serverBean = getProducersBL.serverValidation(clientBean);
            Page<GetProducerPojo> producers = getProducersBL.process(serverBean);
            ResponsePojo<Page<GetProducerPojo>> responseBean = new ResponsePojo<>("Producers fetching successful.", producers, HttpStatus.OK);
            return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error("Error occurred while getting Producers. accountIdentifier: {}. Details: {}", accountIdentifier, e.getMessage(), e);
            healthMetrics.updateHealApiServiceErrors();
            healthMetrics.addStatusCodes(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, 1);
            return ResponseEntity.badRequest().body(new ResponsePojo<>(e.getMessage(), null, HttpStatus.BAD_REQUEST));
        } catch (Exception e) {
            log.error("Unexpected error occurred while getProducers API. accountIdentifier: {}. Details: {}", accountIdentifier, e.getMessage(), e);
            healthMetrics.updateHealApiServiceErrors();
            healthMetrics.addStatusCodes(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE, 1);
            return ResponseEntity.internalServerError().body(new ResponsePojo<>(e.getMessage(), null, HttpStatus.INTERNAL_SERVER_ERROR));
        }
    }

}
