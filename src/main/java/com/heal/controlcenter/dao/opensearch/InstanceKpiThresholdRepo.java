package com.heal.controlcenter.dao.opensearch;


import com.heal.configuration.pojos.opensearch.InstanceKpiThresholds;
import com.heal.configuration.util.DateHelper;
import com.heal.controlcenter.config.OpenSearchConfig;
import com.heal.controlcenter.dao.mysql.entity.InstanceKpiAttributeThresholdBean;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.opensearch._types.SortOrder;
import org.opensearch.client.opensearch._types.query_dsl.Query;
import org.opensearch.client.opensearch.core.*;
import org.opensearch.client.opensearch.core.bulk.BulkResponseItem;
import org.opensearch.client.opensearch.core.search.Hit;
import org.springframework.stereotype.Repository;

import java.io.IOException;
import java.util.*;

@Repository
@Slf4j
public class InstanceKpiThresholdRepo {

    private final OpenSearchConfig openSearchConfig;

    public InstanceKpiThresholdRepo(OpenSearchConfig openSearchConfig) {
        this.openSearchConfig = openSearchConfig;
    }

    public void updateInstanceKpiThresholdsInBulk(String accountIdentifier, List<InstanceKpiAttributeThresholdBean> thresholdDetails) throws HealControlCenterException {
        log.info("Updating instance kpi thresholds in opensearch for {} changed thresholds", thresholdDetails.size());
        try {
            String indexName = Constants.Opensearch.INDEX_PREFIX_HEAL_INSTANCE_KPI_THRESHOLDS + "_" + accountIdentifier.toLowerCase();
            OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, Constants.Opensearch.INDEX_PREFIX_HEAL_INSTANCE_KPI_THRESHOLDS);
            if (openSearchClient == null) {
                log.error("OpenSearch client is null for accountIdentifier: {}", accountIdentifier);
                return;
            }

            List<UpdateRequest<Object, Object>> updateRequestList = new ArrayList<>();
            List<IndexRequest<InstanceKpiThresholds>> indexRequestList = new ArrayList<>();
            for (InstanceKpiAttributeThresholdBean newThreshold : thresholdDetails) {
                log.debug("Updating instance kpi details in index {}: with details {}, account {}", indexName, newThreshold, accountIdentifier);
                SearchResponse<Object> searchResponse = findLatestActiveThresholds(thresholdDetails, accountIdentifier, openSearchClient);
                if (searchResponse != null && searchResponse.hits() != null && searchResponse.hits().hits() != null) {
                    List<Hit<Object>> hits = searchResponse.hits().hits();
                    if (!hits.isEmpty()) {
                        long updateTime = DateTimeUtil.getGMTToEpochTime(newThreshold.getStartTime());
                        log.debug("OS doc with _id {} in index {} is getting updated with endTime {}", hits.get(0).id(), hits.get(0).index(), updateTime);

                        UpdateRequest<Object, Object> updateRequest = new UpdateRequest.Builder<>()
                                .index(hits.get(0).index())
                                .id(hits.get(0).id())
                                .doc(new HashMap<>() {{
                                    put("endTime", updateTime);
                                }}).build();

                        updateRequestList.add(updateRequest);
                    }

                }

                IndexRequest<InstanceKpiThresholds> indexRequest = createIndexRequestForThresholdBulkAddition(newThreshold, indexName);
                indexRequestList.add(indexRequest);
            }

            BulkRequest.Builder bulkRequestBuilder = new BulkRequest.Builder();

            // Add each update request to the bulk request
            updateRequestList.forEach(updateRequest ->
                    bulkRequestBuilder.operations(op ->
                            op.update(updateReq -> updateReq
                                    .index(updateRequest.index())
                                    .id(updateRequest.id())
                                    .document(updateRequest.doc())
                                    .docAsUpsert(false)
                            )));

            // Add each index request to the bulk request
            indexRequestList.forEach(indexRequest ->
                    bulkRequestBuilder.operations(op ->
                            op.index(idx -> idx
                                    .index(indexRequest.index())
                                    .id(indexRequest.id())
                                    .document(indexRequest.document())
                            )));
            // Build the request
            BulkRequest bulkRequest = bulkRequestBuilder.build();

            BulkResponse resp = openSearchClient.bulk(bulkRequest);
            if (resp.errors()) {
                for (BulkResponseItem item : resp.items()) {
                    if (item.error() != null) {
                        log.error("Failures during bulk indexing operation. Exception type: [{}], Reason: [{}]", item.error().type(),
                                item.error().causedBy() != null ? item.error().causedBy().reason() : item.error().reason());
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error in updating end time of instance kpi attribute. Details: ", e);
        }
    }

    public void addInstanceKpiThresholdInBulk(List<InstanceKpiAttributeThresholdBean> changedThresholds) throws HealControlCenterException {
        log.info("Adding instance kpi thresholds in opensearch for changed thresholds : details {}", changedThresholds);
        try {
            String accountIdentifier = changedThresholds.get(0).getAccountIdentifier();
            String indexName = Constants.Opensearch.INDEX_PREFIX_HEAL_INSTANCE_KPI_THRESHOLDS + "_" + accountIdentifier.toLowerCase();

            OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, Constants.Opensearch.INDEX_PREFIX_HEAL_INSTANCE_KPI_THRESHOLDS);
            if (openSearchClient == null) {
                log.error("OpenSearch client is null for accountIdentifier: {}", accountIdentifier);
                return;
            }

            List<IndexRequest<InstanceKpiThresholds>> indexRequestList = new ArrayList<>();
            for (InstanceKpiAttributeThresholdBean threshold : changedThresholds) {
                IndexRequest<InstanceKpiThresholds> indexRequest = createIndexRequestForThresholdBulkAddition(threshold, indexName);
                indexRequestList.add(indexRequest);
            }

            BulkRequest.Builder bulkRequestBuilder = new BulkRequest.Builder();

            // Add each index request to the bulk request
            indexRequestList.forEach(indexRequest ->
                    bulkRequestBuilder.operations(op ->
                            op.index(idx -> idx
                                    .index(indexRequest.index())
                                    .id(indexRequest.id())
                                    .document(indexRequest.document())
                            )));
            // Build the request
            BulkRequest bulkRequest = bulkRequestBuilder.build();

            BulkResponse resp = openSearchClient.bulk(bulkRequest);
            if (!resp.errors()) {
                log.debug("successfully bulk added instance kpi thresholds details");
            }
        } catch (Exception e) {
            log.error("Error in inserting the instance kpi thresholds details. Details: ", e);
            throw new HealControlCenterException("Error in inserting the instance kpi thresholds details");
        }
    }

    public IndexRequest<InstanceKpiThresholds> createIndexRequestForThresholdBulkAddition(InstanceKpiAttributeThresholdBean threshold, String indexName) {
        String compInstId = threshold.getCompInstanceIdentifier();
        String kpiId = String.valueOf(threshold.getKpiId());
        String kpiAttribute = threshold.getAttributeValue();

        long startTime = DateTimeUtil.getGMTToEpochTime(threshold.getStartTime());
        String date = DateHelper.getWeeksAsString(startTime, startTime).get(0);
        String exactIndexName = indexName + "_" + date;
        log.debug("Adding instance kpi threshold details in opensearch to index {}: with details {}", exactIndexName, threshold);

        String docId = compInstId + "#" + kpiId + "#" + kpiAttribute + "#" + threshold.getThresholdSeverityId() + "#" + startTime;

        Map<String, Double> thresholds = new HashMap<>();
        thresholds.put("Upper", threshold.getMaxThreshold());
        thresholds.put("Lower", threshold.getMinThreshold());

        InstanceKpiThresholds instanceKpiThreshold = InstanceKpiThresholds.builder()
                .instanceId(compInstId)
                .kpiId(Integer.parseInt(kpiId))
                .kpiAttribute(kpiAttribute)
                .startTime(startTime)
                .endTime(0)
                .serviceIdentifier("")
                .applicationId("")
                .description(threshold.getDescription())
                .operationType(threshold.getOperationName())
                .thresholdType(Constants.STATIC_THRESHOLD)
                .thresholds(thresholds)
                .timestamp(DateTimeUtil.date2GMTConversion(new Date(startTime), Constants.Opensearch.TIMESTAMP_FORMAT_INDEX_PATTERN))
                .thresholdSeverityId(threshold.getThresholdSeverityId())
                .build();

        return new IndexRequest.Builder<InstanceKpiThresholds>()
                .index(exactIndexName)
                .id(docId)
                .document(instanceKpiThreshold).build();
    }

    public void closeExistingThresholds(List<InstanceKpiAttributeThresholdBean> thresholdDetails) throws HealControlCenterException {
        try {
            String accountIdentifier = thresholdDetails.get(0).getAccountIdentifier();
            OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, Constants.Opensearch.INDEX_PREFIX_HEAL_INSTANCE_KPI_THRESHOLDS);
            if (openSearchClient == null) {
                log.error("OpenSearch client is null for accountIdentifier: {}", accountIdentifier);
                return;
            }

            List<UpdateRequest<Object, Object>> updateRequestList = new ArrayList<>();
            for (InstanceKpiAttributeThresholdBean newThreshold : thresholdDetails) {
                log.debug("Deleting instance kpi details in index {}: with details {}, account {}", Constants.Opensearch.INDEX_PREFIX_HEAL_INSTANCE_KPI_THRESHOLDS, newThreshold, accountIdentifier);
                SearchResponse<Object> searchResponse = findLatestActiveThresholds(thresholdDetails, accountIdentifier, openSearchClient);
                if (searchResponse != null && searchResponse.hits() != null && searchResponse.hits().hits() != null) {
                    List<Hit<Object>> hits = searchResponse.hits().hits();
                    if (!hits.isEmpty()) {
                        long updateTime = System.currentTimeMillis();
                        log.debug("OS doc with _id {} in index {} is getting updated with endTime {}", hits.get(0).id(), hits.get(0).index(), updateTime);

                        UpdateRequest<Object, Object> updateRequest = new UpdateRequest.Builder<>()
                                .index(hits.get(0).index())
                                .id(hits.get(0).id())
                                .doc(new HashMap<>() {{
                                    put("endTime", updateTime);
                                }}).build();

                        updateRequestList.add(updateRequest);
                    }
                }
            }

            BulkRequest.Builder bulkRequestBuilder = new BulkRequest.Builder();
            // Add each update request to the bulk request
            updateRequestList.forEach(updateRequest ->
                    bulkRequestBuilder.operations(op ->
                            op.update(updateReq -> updateReq
                                    .index(updateRequest.index())
                                    .id(updateRequest.id())
                                    .document(updateRequest.doc())
                                    .docAsUpsert(false)
                            )));
            // Build the request
            BulkRequest bulkRequest = bulkRequestBuilder.build();

            BulkResponse resp = openSearchClient.bulk(bulkRequest);
            if (resp.errors()) {
                for (BulkResponseItem item : resp.items()) {
                    if (item.error() != null) {
                        log.error("Failures during bulk indexing operation. Exception type: [{}], Reason: [{}]", item.error().type(),
                                item.error().causedBy() != null ? item.error().causedBy().reason() : item.error().reason());
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error in updating end time of instance kpi attribute. Details: ", e);
        }
    }

    private SearchResponse<Object> findLatestActiveThresholds(List<InstanceKpiAttributeThresholdBean> thresholds, String accountIdentifier, OpenSearchClient openSearchClient) throws IOException {
        String indexPattern = Constants.Opensearch.INDEX_PREFIX_HEAL_INSTANCE_KPI_THRESHOLDS + "_" + accountIdentifier.toLowerCase() + "_*";

        Query.Builder mainQueryBuilder = new Query.Builder();
        List<Query> shouldClauses = new ArrayList<>();

        // Create a "should" clause for each threshold to find
        for (InstanceKpiAttributeThresholdBean threshold : thresholds) {
            Query perThresholdQuery = Query.of(c -> c.bool(d -> d
                    .must(e -> e.matchPhrase(f -> f.field("instanceId").query(threshold.getCompInstanceIdentifier())))
                    .must(e -> e.matchPhrase(f -> f.field("kpiId").query(String.valueOf(threshold.getKpiId()))))
                    .must(e -> e.matchPhrase(f -> f.field("kpiAttribute").query(threshold.getAttributeValue())))
                    .must(e -> e.matchPhrase(f -> f.field("thresholdType").query(Constants.STATIC_THRESHOLD)))
                    .must(e -> e.matchPhrase(f -> f.field("endTime").query("0")))
            ));
            shouldClauses.add(perThresholdQuery);
        }

        // Combine all "should" clauses into a single bool query
        mainQueryBuilder.bool(b -> b.should(shouldClauses).minimumShouldMatch("1"));

        SearchRequest searchRequest = new SearchRequest.Builder()
                .index(indexPattern) // Use the wildcard pattern here
                .query(mainQueryBuilder.build())
                .size(thresholds.size()) // Fetch up to the number of items we are looking for
                .sort(c -> c.field(f -> f.field("startTime").order(SortOrder.Desc)))
                .build();

        return openSearchClient.search(searchRequest, Object.class);
    }

}
