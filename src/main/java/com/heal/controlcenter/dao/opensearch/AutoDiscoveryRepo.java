package com.heal.controlcenter.dao.opensearch;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.heal.configuration.pojos.opensearch.AutoDiscoveryData;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.Constants;
import org.opensearch.client.opensearch._types.aggregations.Aggregate;
import org.opensearch.client.opensearch._types.aggregations.Aggregation;
import org.opensearch.client.opensearch._types.aggregations.StringTermsBucket;
import org.opensearch.client.opensearch._types.query_dsl.Query;
import org.opensearch.client.opensearch.core.SearchResponse;
import org.opensearch.client.opensearch.core.search.Hit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public class AutoDiscoveryRepo {

    private static final Logger log = LoggerFactory.getLogger(AutoDiscoveryRepo.class);

    private final ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();

    public Map<String, String> getAutoDiscoveryHostErrorLogs() throws HealControlCenterException {
        String indexName = Constants.INDEX_PREFIX_HEAL_AUTO_DISCOVERY_ERRORS + "_*";
        Map<String, String> result = new HashMap<>();
        try {
            Query qb = Query.of(c -> c.bool(b -> b.must(m -> m.matchAll(t -> t))));

            Map<String, Aggregation> aggregationMap = new HashMap<>();
            aggregationMap.put("DISTINCT_VALUES", Aggregation.of(c -> c.terms(d -> d.field("hostNodeId"))));
            SearchResponse<Object> searchResponse = OpenSearchRepo.getDistinctFieldsAndCount(qb, aggregationMap, indexName, Constants.GLOBAl_ACCOUNT_IDENTIFIER, Constants.INDEX_PREFIX_HEAL_AUTO_DISCOVERY_ERRORS);
            if (searchResponse == null || searchResponse.hits() == null) {
                return Collections.emptyMap();
            }

            Gson gson = new Gson();

            List<Hit<Object>> searchHits = searchResponse.hits().hits();
            if (searchHits != null && !searchHits.isEmpty()) {
                Aggregate fieldCountsAggs = searchResponse.aggregations().get("DISTINCT_VALUES");
                List<String> listOfUniqueHostIds = new ArrayList<>();
                if (fieldCountsAggs.isSterms()) {
                    for (StringTermsBucket bucket : fieldCountsAggs.sterms().buckets().array()) {
                        listOfUniqueHostIds.add(bucket.key());
                    }
                }

                for (String hostId : listOfUniqueHostIds) {
                    Query qb1 = Query.of(c -> c.bool(b -> b.must(m -> m.matchPhrase(v -> v.field("hostNodeId").query(hostId)))));
                    SearchResponse<Object> resp = OpenSearchRepo.getLimitedSortedDocument(qb1, indexName, "time", true, 1, Constants.GLOBAl_ACCOUNT_IDENTIFIER, Constants.INDEX_PREFIX_HEAL_AUTO_DISCOVERY_ERRORS);
                    if (resp == null || resp.hits() == null) {
                        throw new HealControlCenterException("OpenSearch connection unavailable");
                    }

                    List<Hit<Object>> hits = resp.hits().hits();
                    if (hits != null) {
                        for (Hit<Object> hit : hits) {
                            AutoDiscoveryData autoDiscoveryData = objectMapper.readValue(gson.toJson(hit.source()), new TypeReference<>() {
                            });
                            String hostNodeId = autoDiscoveryData.getHostNodeId();
                            long errorCount = autoDiscoveryData.getErrorCount();
                            String errorMessage = autoDiscoveryData.getErrorMessage();
                            String stackTrace = autoDiscoveryData.getStackTrace();
                            String value = "\"Error Count\" : \"" + errorCount + "\", \n\"Error Message\" : \"" + errorMessage +
                                    "\", \n\"Stack Trace\" : \n\"" + stackTrace + "\"";
                            result.put(hostNodeId, value);
                        }
                    }
                }
            }
            return result;
        } catch (Exception e) {
            log.warn("Unable to get AutodiscoveryError logs. Details: ", e);
            throw new HealControlCenterException("Unable to get AutodiscoveryError logs.");
        }
    }

}
