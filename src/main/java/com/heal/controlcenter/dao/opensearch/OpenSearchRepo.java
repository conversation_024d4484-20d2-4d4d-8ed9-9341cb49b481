package com.heal.controlcenter.dao.opensearch;

import com.heal.controlcenter.enums.OpenSearchConnectionManager;
import com.heal.controlcenter.exception.HealControlCenterException;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.opensearch._types.SortOrder;
import org.opensearch.client.opensearch._types.aggregations.Aggregation;
import org.opensearch.client.opensearch._types.query_dsl.Query;
import org.opensearch.client.opensearch.core.SearchRequest;
import org.opensearch.client.opensearch.core.SearchResponse;

import java.io.IOException;
import java.util.Map;

@Slf4j
public class OpenSearchRepo<T> {

    public static SearchResponse<Object> getDistinctFieldsAndCount(Query qb, Map<String, Aggregation> termAggs, String indexName, String accountIdentifier, String index) throws IOException, HealControlCenterException {
        OpenSearchClient openSearchClient = OpenSearchConnectionManager.INSTANCE.getOpenSearchClient(accountIdentifier, index);
        if (openSearchClient == null) {
            throw new HealControlCenterException("OpenSearch client is null.");
        }

        SearchRequest searchRequest = new SearchRequest.Builder()
                .index(indexName)
                .query(qb)
                .aggregations(termAggs)
                .build();

        return openSearchClient.search(searchRequest, Object.class);
    }

    public static SearchResponse<Object> getLimitedSortedDocument(Query qb, String indexName, String fieldName, boolean isDesc, int limit, String accountIdentifier, String index) throws IOException, HealControlCenterException {
        OpenSearchClient openSearchClient = OpenSearchConnectionManager.INSTANCE.getOpenSearchClient(accountIdentifier, index);
        if (openSearchClient == null) {
            throw new HealControlCenterException("OpenSearch client is null.");
        }

        SearchRequest searchRequest = new SearchRequest.Builder()
                .index(indexName)
                .query(qb)
                .size(limit)
                .sort(c -> c.field(f -> f.field(fieldName).order(isDesc ? SortOrder.Desc : SortOrder.Asc)))
                .build();

        return openSearchClient.search(searchRequest, Object.class);
    }
}
