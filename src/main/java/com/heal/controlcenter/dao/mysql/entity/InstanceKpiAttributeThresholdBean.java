package com.heal.controlcenter.dao.mysql.entity;

import com.heal.controlcenter.pojo.ActionsEnum;
import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class InstanceKpiAttributeThresholdBean {

    private int compInstanceId;
    private int kpiId;
    private int kpiGroupId;
    private String attributeValue;
    private int thresholdSeverityId;
    @EqualsAndHashCode.Exclude
    private String attributeOldValue;
    @EqualsAndHashCode.Exclude
    private ActionsEnum actionForUpdate;
    @EqualsAndHashCode.Exclude
    private int accountId;
    @EqualsAndHashCode.Exclude
    private String accountIdentifier;
    @EqualsAndHashCode.Exclude
    private String compInstanceIdentifier;
    @EqualsAndHashCode.Exclude
    private int operationId;
    @EqualsAndHashCode.Exclude
    private int status;
    @EqualsAndHashCode.Exclude
    private int severity;
    @EqualsAndHashCode.Exclude
    private String createdTime;
    @EqualsAndHashCode.Exclude
    private String updatedTime;
    @EqualsAndHashCode.Exclude
    private Double minThreshold;
    @EqualsAndHashCode.Exclude
    private Double maxThreshold;
    @EqualsAndHashCode.Exclude
    private String userDetailsId;
    @EqualsAndHashCode.Exclude
    private String operationName;
    @EqualsAndHashCode.Exclude
    private String startTime;
    @EqualsAndHashCode.Exclude
    private String endTime;
    @EqualsAndHashCode.Exclude
    private String description;
    @EqualsAndHashCode.Exclude
    private Integer suppression;
    @EqualsAndHashCode.Exclude
    private Integer persistence;
    @EqualsAndHashCode.Exclude
    private int excludeMaintenance;
    @EqualsAndHashCode.Exclude
    private String coverageWindow;
    @EqualsAndHashCode.Exclude
    private int isDiscovery;

}
