package com.heal.controlcenter.dao.mysql.entity;

import lombok.Data;

@Data
public class KpiListBean {
    private int id;
    private String name;
    private String identifier;
    private String description;
    private String dataType;
    private String valueType;
    private String clusterOperation;
    private String rollupOperation;
    private String clusterAggregation;
    private String instanceAggregation;
    private String kpiUnit;
    private String kpiType;
    private int status;
    private int groupKpiId;
    private String groupKpiName;
    private String groupKpiIdentifier;
    private String groupKpiDescription;
    private String groupKpiType;
    private int groupKpiDiscovery;
    private int groupKpiStandardType;
    private int componentId;
    private String componentName;
    private int componentVersionId;
    private int commonVersionId;
    private int componentTypeId;
    private int collectionInterval;
    private int standardType;
    private int availableForAnalytics;
    private String cronExpression;
    private int resetDeltaValue;
    private int deltaPerSec;
}
