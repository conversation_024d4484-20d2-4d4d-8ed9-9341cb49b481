package com.heal.controlcenter.dao.mysql;

import com.heal.configuration.pojos.Tags;
import com.heal.controlcenter.beans.TagDetailsBean;
import com.heal.controlcenter.pojo.IdPojo;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.pojo.TagMappingDetails;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Repository
public class TagsDao {

    private final JdbcTemplate jdbcTemplate;

    public TagsDao(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public int addTagMappingDetails(TagMappingDetails bean) throws HealControlCenterException {
        String query = "insert into tag_mapping (tag_id, object_id, object_ref_table, tag_key, tag_value, created_time, updated_time, account_id, user_details_id) " +
                "values (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        try{
            log.debug("adding tag mapping details.");
            return jdbcTemplate.update(query, bean.getTagId(), bean.getObjectId(), bean.getObjectRefTable(), bean.getTagKey(), bean.getTagValue(), bean.getCreatedTime(), bean.getUpdatedTime(), bean.getAccountId(), bean.getUserDetailsId());
        } catch (Exception ex) {
            log.error("Error in adding tag mapping details", ex);
            throw new HealControlCenterException("Error in adding tag mapping details");
        }
    }

    public TagDetailsBean getTagDetailsByName(String name) throws HealControlCenterException {
        String query = "select id,name,tag_type_id tagTypeId,is_predefined isPredefined,ref_table refTable,created_time createdTime,updated_time updatedTime,account_id accountId," +
                "user_details_id lastModifiedBy,ref_where_column_name refWhereColumnName,ref_select_column_name refSelectColumnName from tag_details where name = ?";
        try{
            log.debug("getting user tag.");
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(TagDetailsBean.class), name);
        } catch (Exception ex) {
            log.error("Error in getting tag details.");
            throw new HealControlCenterException("Error in getting tag details.");
        }
    }

    public List<TagDetailsBean> getTagDetailsByAccountId(int accountId) throws HealControlCenterException {
        String query = "SELECT id, name, tag_type_id tagTypeId, is_predefined isPredefined, ref_table refTable, created_time createdTime, updated_time updatedTime, " +
                "account_id accountId, user_details_id userDetailsId, ref_where_column_name refWhereColumnName, ref_select_column_name refSelectColumnName " +
                "FROM tag_details WHERE account_id IN (1, ?)";
        try{
            log.debug("getting user tag details.");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(TagDetailsBean.class), accountId);
        } catch (Exception ex) {
            log.error("Error in getting tag details.");
            throw new HealControlCenterException("Error in getting tag details.");
        }
    }

    public List<TagMappingDetails> getTagMappingDetails(int accountId) throws HealControlCenterException {
        String query = "SELECT id, tag_id tagId, object_id objectId, object_ref_table objectRefTable, tag_key tagKey, tag_value tagValue, " +
                "created_time createdTime, updated_time updatedTime, account_id accountId, user_details_id lastModifiedBy FROM tag_mapping " +
                "WHERE object_ref_table != 'transaction' AND account_id = ?";
        try{
            log.debug("getting tag mapping details.");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(TagMappingDetails.class), accountId);
        } catch (Exception ex) {
            log.error("Error in getting tag mapping details.");
            throw new HealControlCenterException("Error in getting tag mapping details.");
        }
    }

    public List<Tags> getTagsByObjectId(int objectId, String refTable) throws HealControlCenterException {
        String query = "select td.name, mt.time_zone_id as tag_key, tag_value from tag_mapping tm, mst_timezone mt, tag_details td " +
                "where tm.object_id= ? and tm.object_ref_table=? and tag_id=td.id and mt.id = tm.tag_key";
        try {
            return jdbcTemplate.query(query, (rs, rowNum) -> Tags.builder()
                    .key(rs.getString("tag_key"))
                    .value(rs.getString("tag_value"))
                    .type(rs.getString("td.name"))
                    .build(), objectId, refTable);
        } catch (EmptyResultDataAccessException e) {
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("Exception occurred when fetching tags for accountId:{}, refTable:{} ", objectId, refTable, e);
            throw new HealControlCenterException("Error in fetching account related tags");
        }
    }

    public List<TagMappingDetails> getTagMappingDetailsForCompInstanceClusters(int accountId, int tagId, List<Integer> clusterIds) throws HealControlCenterException {
        if (clusterIds.isEmpty()) {
            return Collections.emptyList();
        }
        String placeholders = String.join(",", Collections.nCopies(clusterIds.size(), "?"));
        String query = "SELECT id, tag_id tagId, object_id objectId, object_ref_table objectRefTable, " +
                "tag_key tagKey, tag_value tagValue, created_time createdTime, updated_time updatedTime, " +
                "account_id accountId, user_details_id lastModifiedBy FROM tag_mapping " +
                "WHERE account_id = ? AND tag_id = ? AND object_id IN (" + placeholders + ")";
        try {
            Object[] params = new Object[2 + clusterIds.size()];
            params[0] = accountId;
            params[1] = tagId;
            for (int i = 0; i < clusterIds.size(); i++) {
                params[2 + i] = clusterIds.get(i);
            }
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(TagMappingDetails.class), params);
        } catch (Exception ex) {
            log.error("Error in getting tag mapping details for clusters.");
            throw new HealControlCenterException("Error in getting tag mapping details for clusters.");
        }
    }

    public List<IdPojo> getTagValue(int tagId, String objectRefTable, int accountId) throws HealControlCenterException {
        String query = "SELECT object_id id, tag_value name, tag_key identifier FROM tag_mapping " +
                "WHERE tag_id = ? AND object_ref_table = ? AND account_id = ?";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(IdPojo.class), tagId, objectRefTable, accountId);
        } catch (Exception ex) {
            log.error("Error in getting tag values for tagId [{}] and objectRefTable [{}]", tagId, objectRefTable, ex);
            throw new HealControlCenterException("Error in getting tag values.");
        }
    }

    /**
     * Gets tag value by tag name and object ID with default fallback.
     */
    public String getTagValueByKey(String tagName, int objectId, String defaultValue, String objectRefTable) {
        String query = "SELECT tm.tag_value FROM tag_mapping tm " +
                       "JOIN tag_details td ON tm.tag_id = td.id " +
                       "WHERE td.name = ? AND tm.object_id = ? AND tm.object_ref_table = ?";
        try {
            return jdbcTemplate.queryForObject(query, String.class, tagName, objectId, objectRefTable);
        } catch (Exception ex) {
            log.debug("Tag value not found for tagName [{}], objectId [{}], returning default value", tagName, objectId);
            return defaultValue;
        }
    }

    /**
     * Gets all tags for a specific object.
     */
    public List<TagMappingDetails> getTagsDetailsByObjectId(int objectId, String objectRefTable) throws HealControlCenterException {
        String query = "SELECT tm.id, tm.tag_id tagId, tm.object_id objectId, tm.object_ref_table objectRefTable, " +
                       "tm.tag_key tagKey, tm.tag_value tagValue, tm.created_time createdTime, tm.updated_time updatedTime, " +
                       "tm.account_id accountId, tm.user_details_id lastModifiedBy " +
                       "FROM tag_mapping tm " +
                       "WHERE tm.object_id = ? AND tm.object_ref_table = ?";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(TagMappingDetails.class), objectId, objectRefTable);
        } catch (Exception e) {
            log.error("Error fetching tags for objectId [{}] and objectRefTable [{}]", objectId, objectRefTable, e);
            throw new HealControlCenterException("Error fetching tags");
        }
    }

    /**
     * Gets service to component instance mapping through tag_mapping table.
     */
    public Map<Integer, List<Integer>> getServiceComponentInstanceMapping(int accountId, int controllerTagId) throws HealControlCenterException {
        String query = "SELECT CAST(tm.tag_key AS UNSIGNED) as serviceId, tm.object_id as componentInstanceId " +
                       "FROM tag_mapping tm " +
                       "WHERE tm.account_id = ? AND tm.tag_id = ? AND tm.object_ref_table = 'comp_instance'";
        try {
            List<Map<String, Object>> results = jdbcTemplate.queryForList(query, accountId, controllerTagId);
            return results.stream()
                    .collect(Collectors.groupingBy(
                        row -> {
                            Object serviceIdObj = row.get("serviceId");
                            return serviceIdObj instanceof BigInteger ? 
                                ((BigInteger) serviceIdObj).intValue() : 
                                (Integer) serviceIdObj;
                        },
                        Collectors.mapping(row -> {
                            Object componentInstanceIdObj = row.get("componentInstanceId");
                            return componentInstanceIdObj instanceof BigInteger ? 
                                ((BigInteger) componentInstanceIdObj).intValue() : 
                                (Integer) componentInstanceIdObj;
                        }, Collectors.toList())
                    ));
        } catch (Exception e) {
            log.error("Error fetching service component instance mapping for accountId [{}]", accountId, e);
            throw new HealControlCenterException("Error fetching service component instance mapping.");
        }
    }

    /**
     * Updates an existing tag mapping row by its ID.
     */
    public int updateTagMappingDetails(TagMappingDetails bean) throws HealControlCenterException {
        String query = "UPDATE tag_mapping SET tag_id = ?, object_id = ?, object_ref_table = ?, tag_key = ?, tag_value = ?, updated_time = ?, account_id = ?, user_details_id = ? WHERE id = ?";
        try {
            log.debug("Updating tag mapping details for id: {}", bean.getId());
            return jdbcTemplate.update(query, bean.getTagId(), bean.getObjectId(), bean.getObjectRefTable(), bean.getTagKey(), bean.getTagValue(), bean.getUpdatedTime(), bean.getAccountId(), bean.getUserDetailsId(), bean.getId());
        } catch (Exception ex) {
            log.error("Error in updating tag mapping details", ex);
            throw new HealControlCenterException("Error in updating tag mapping details");
        }
    }

    /**
     * Gets the timezone tag mapping for a specific object (tag_id = 2).
     * Returns null if not found.
     */
    public TagMappingDetails getTimezoneTagMappingByObjectId(int objectId, String objectRefTable) throws HealControlCenterException {
        String query = "SELECT tm.id, tm.tag_id tagId, tm.object_id objectId, tm.object_ref_table objectRefTable, " +
                "tm.tag_key tagKey, tm.tag_value tagValue, tm.created_time createdTime, tm.updated_time updatedTime, " +
                "tm.account_id accountId, tm.user_details_id lastModifiedBy " +
                "FROM tag_mapping tm " +
                "WHERE tm.object_id = ? AND tm.object_ref_table = ? AND tm.tag_id = 2";
        try {
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(TagMappingDetails.class), objectId, objectRefTable);
        } catch (EmptyResultDataAccessException e) {
            return null;
        } catch (Exception e) {
            log.error("Error fetching timezone tag mapping for objectId [{}] and objectRefTable [{}]", objectId, objectRefTable, e);
            throw new HealControlCenterException("Error fetching timezone tag mapping");
        }
    }

    /**
     * Gets tag mapping details for a specific tagId, objectId, objectRefTable, tagKey, and accountId.
     */
    public List<TagMappingDetails> getTagKeyId(int tagId, int objectId, String objectRefTable, String tagKey, int accountId) {
        String query = "SELECT tag_key tagKey FROM tag_mapping WHERE tag_id = ? AND object_id = ? AND object_ref_table = ? AND tag_key = ? AND account_id = ?";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(TagMappingDetails.class), tagId, objectId, objectRefTable, tagKey, accountId);
        } catch (Exception ex) {
            log.error("Error in getting tag key id", ex);
            return Collections.emptyList();
        }
    }

    /**
     * Gets tags for a list of object IDs and a reference table.
     */
    public Map<Integer, List<Tags>> getTagsForObjectIds(List<Integer> objectIds, String refTable) throws HealControlCenterException {
        if (objectIds == null || objectIds.isEmpty()) {
            return Collections.emptyMap();
        }
        String query = "select tm.object_id, td.name as type, tm.tag_key, tm.tag_value " +
                "from tag_mapping tm, tag_details td " +
                "where tm.object_ref_table=? and tm.tag_id=td.id and tm.object_id IN (" +
                String.join(",", Collections.nCopies(objectIds.size(), "?")) + ")";

        Object[] params = new Object[1 + objectIds.size()];
        params[0] = refTable;
        for (int i = 0; i < objectIds.size(); i++) {
            params[i + 1] = objectIds.get(i);
        }

        try {
            return jdbcTemplate.query(query, params, rs -> {
                Map<Integer, List<Tags>> results = new HashMap<>();
                while (rs.next()) {
                    int objectId = rs.getInt("object_id");
                    Tags tag = Tags.builder()
                            .key(rs.getString("tag_key"))
                            .value(rs.getString("tag_value"))
                            .type(rs.getString("type"))
                            .build();
                    results.computeIfAbsent(objectId, k -> new ArrayList<>()).add(tag);
                }
                return results;
            });
        } catch (Exception e) {
            log.error("Exception occurred when fetching tags for refTable:{} ", refTable, e);
            throw new HealControlCenterException("Error in fetching account related tags");
        }
    }
}
