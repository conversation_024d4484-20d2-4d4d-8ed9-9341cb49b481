package com.heal.controlcenter.dao.mysql;

import com.heal.controlcenter.beans.ForensicActionArgumentsBean;
import com.heal.controlcenter.beans.ForensicActionBean;
import com.heal.controlcenter.exception.HealControlCenterException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Repository
public class ActionScriptDao {

    private final JdbcTemplate jdbcTemplate;

    public ActionScriptDao(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * Fetches the total count of forensic actions for a given account.
     *
     * @param accountId The ID of the account.
     * @return The total number of forensic actions.
     * @throws HealControlCenterException if an error occurs while fetching the count.
     */
    public Integer getForensicCountForAccount(int accountId) throws HealControlCenterException {
        String query = "select count(distinct Actions.id)  from actions as Actions, mst_sub_type as MasterSubType "+
                "where MasterSubType.name = 'Forensic' and Actions.account_id in (1, " + accountId + ")";
        try {
            log.debug("getting forensic count.");
            return jdbcTemplate.queryForObject(query, Integer.class);
        } catch (Exception e) {
            log.error("Exception encountered while fetching forensic count from 'actions' table for accountId [{}]. Details: ", accountId, e);
            throw new HealControlCenterException("Error occurred while getting forensic count.");
        }
    }

    /**
     * Retrieves a paginated and filtered list of forensic actions.
     *
     * @param accountId The ID of the account.
     * @param pageable Pagination and sorting information.
     * @param searchTerm A search term to filter actions by name.
     * @param commandName A filter for the command name.
     * @param commandTimeoutInSeconds A filter for the command timeout.
     * @param categoryList A list of categories to filter by.
     * @param status A filter for the action status.
     * @param lastModifiedBy A filter for the user who last modified the action.
     * @return A list of {@link ForensicActionBean} objects.
     * @throws HealControlCenterException if an error occurs while fetching the actions.
     */
    public List<ForensicActionBean> getForensicActions(Integer accountId, Pageable pageable, String searchTerm, String commandName, String commandTimeoutInSeconds, List<String> categoryList, String status, String lastModifiedBy, Integer type) throws HealControlCenterException {
        String idOrderByClause = createOrderByClause(pageable, true);
        String dataOrderByClause = createOrderByClause(pageable, false);


        List<Object> params = new java.util.ArrayList<>();
        StringBuilder idQueryBuilder = new StringBuilder("SELECT DISTINCT Actions.id " +
                "FROM actions Actions " +
                "LEFT JOIN user_attributes UA ON Actions.user_details_id = UA.user_identifier " +
                "JOIN action_category_mapping ACM ON Actions.id = ACM.action_id " +
                "JOIN mst_category_details MCD ON ACM.category_id = MCD.id " +
                "JOIN command_details CD ON ACM.object_id = CD.id " +
                "WHERE Actions.action_type_id = 289 " +
                "AND Actions.standard_type_id = ? " +
                "AND Actions.account_id IN (1, ?) " +
                "AND Actions.name LIKE ? ");
        params.add(type);
        params.add(accountId);
        params.add("%" + searchTerm + "%");

        if (commandName != null && !commandName.isEmpty()) {
            idQueryBuilder.append("AND CD.name = ? ");
            params.add(commandName);
        }
        if (commandTimeoutInSeconds != null && !commandTimeoutInSeconds.isEmpty()) {
            idQueryBuilder.append("AND CD.timeout_in_secs = ? ");
            params.add(commandTimeoutInSeconds);
        }
        if (categoryList != null && !categoryList.isEmpty()) {
            idQueryBuilder.append("AND MCD.name IN (");
            for (int i = 0; i < categoryList.size(); i++) {
                idQueryBuilder.append("?");
                if (i < categoryList.size() - 1) {
                    idQueryBuilder.append(",");
                }
            }
            idQueryBuilder.append(") ");
            params.addAll(categoryList);
        }
        if (status != null && !status.isEmpty()) {
            idQueryBuilder.append("AND Actions.status = ? ");
            params.add(status);
        }
        if (lastModifiedBy != null && !lastModifiedBy.isEmpty()) {
            idQueryBuilder.append("AND UA.username = ? ");
            params.add(lastModifiedBy);
        }

        idQueryBuilder.append(idOrderByClause);
        idQueryBuilder.append("LIMIT ? OFFSET ?");
        params.add(pageable.getPageSize());
        params.add(pageable.getOffset());

        String idQuery = idQueryBuilder.toString();

        try {
            List<Integer> actionIds = jdbcTemplate.queryForList(idQuery, Integer.class, params.toArray());

            if (actionIds.isEmpty()) {
                return new ArrayList<>();
            }

            String idsPlaceholder = String.join(",", java.util.Collections.nCopies(actionIds.size(), "?"));

            String dataQuery = "SELECT " +
                    "    Actions.id AS actionId, Actions.name AS actionName, MasterSubType.name AS actionType, CmdDetails.name AS commandName, " +
                    "    CmdDetails.identifier AS commandIdentifier, CmdDetails.id AS commandId, CmdDetails.timeout_in_secs AS commandTimeoutInSeconds, " +
                    "    ACM.ttl_in_secs AS supCtrlTimeoutInSeconds, ACM.retries AS supCtrlRetryCount, Actions.status AS status, UA.username AS lastModifiedBy, " +
                    "    Actions.updated_time AS lastModifiedOn, MasterCategoryDetails.id AS categoryId, MasterCategoryDetails.name AS categoryName, " +
                    "    MasterCategoryDetails.identifier AS categoryIdentifier, MasterCategoryDetails.is_custom AS isCustomCategory " +
                    "FROM " +
                    "    actions Actions " +
                    "JOIN " +
                    "    mst_sub_type MasterSubType ON Actions.standard_type_id = MasterSubType.id " +
                    "JOIN " +
                    "    action_category_mapping ACM ON Actions.id = ACM.action_id " +
                    "JOIN " +
                    "    command_details CmdDetails ON ACM.object_id = CmdDetails.id " +
                    "JOIN " +
                    "    mst_category_details MasterCategoryDetails ON ACM.category_id = MasterCategoryDetails.id " +
                    "LEFT JOIN " +
                    "    user_attributes UA ON Actions.user_details_id = UA.user_identifier " +
                    "WHERE " +
                    "    Actions.id IN (" + idsPlaceholder + ") " +
                    dataOrderByClause;

            return jdbcTemplate.query(dataQuery, new BeanPropertyRowMapper<>(ForensicActionBean.class), actionIds.toArray());

        } catch (Exception e) {
            log.error("Exception encountered while fetching forensic actions for accountId [{}]. Details: ", accountId, e);
            throw new HealControlCenterException("Error occurred while getting forensic actions.");
        }
    }

    /**
     * Creates the ORDER BY clause for the SQL query based on pagination and sorting parameters.
     *
     * @param pageable Pagination and sorting information.
     * @param useColumnNames A boolean to determine whether to use table column names or bean property names.
     * @return The generated ORDER BY clause as a string.
     */
    private String createOrderByClause(Pageable pageable, boolean useColumnNames) {
        Map<String, String> columnMapping = new HashMap<>();
        if (useColumnNames) {
            columnMapping.put("name", "Actions.name");
            columnMapping.put("lastModifiedBy", "UA.username");
            columnMapping.put("lastModifiedOn", "Actions.updated_time");
        } else {
            columnMapping.put("name", "actionName");
            columnMapping.put("lastModifiedBy", "lastModifiedBy");
            columnMapping.put("lastModifiedOn", "lastModifiedOn");
        }

        Sort sort = pageable.getSort();
        if (sort.isUnsorted()) {
            return "ORDER BY Actions.id ASC ";
        }

        String orderBy = sort.stream()
                .map(order -> {
                    String property = columnMapping.get(order.getProperty());
                    if (property == null) {
                        log.warn("Invalid sort property '{}' requested. Ignoring.", order.getProperty());
                        return null;
                    }
                    return property + " " + (order.isAscending() ? "ASC" : "DESC");
                })
                .filter(s -> s != null && !s.trim().isEmpty())
                .collect(Collectors.joining(", "));

        if (orderBy.isEmpty()) {
            return "ORDER BY Actions.id ASC ";
        }

        return "ORDER BY " + orderBy + " ";
    }

    /**
     * Fetches the total count of forensic actions based on the provided filters.
     *
     * @param accountId The ID of the account.
     * @param searchTerm A search term to filter actions by name.
     * @param commandName A filter for the command name.
     * @param commandTimeoutInSeconds A filter for the command timeout.
     * @param categoryList A list of categories to filter by.
     * @param status A filter for the action status.
     * @param lastModifiedBy A filter for the user who last modified the action.
     * @return The total number of forensic actions matching the filters.
     * @throws HealControlCenterException if an error occurs while fetching the count.
     */
    public int getForensicActionsCount(Integer accountId, String searchTerm, String commandName, String commandTimeoutInSeconds, List<String> categoryList, String status, String lastModifiedBy, Integer type) throws HealControlCenterException {

        List<Object> params = new java.util.ArrayList<>();
        StringBuilder queryBuilder = new StringBuilder("SELECT COUNT(DISTINCT Actions.id) " +
                "FROM " +
                "    actions Actions " +
                "JOIN " +
                "    mst_sub_type MasterSubType ON Actions.standard_type_id = MasterSubType.id " +
                "JOIN " +
                "    action_category_mapping ACM ON Actions.id = ACM.action_id " +
                "JOIN " +
                "    command_details CmdDetails ON ACM.object_id = CmdDetails.id " +
                "JOIN " +
                "    mst_category_details MasterCategoryDetails ON ACM.category_id = MasterCategoryDetails.id " +
                "LEFT JOIN " +
                "    user_attributes UA ON Actions.user_details_id = UA.user_identifier " +
                "WHERE " +
                "    Actions.action_type_id = 289 " +
                "    AND Actions.standard_type_id = ? " +
                "    AND Actions.account_id IN (1, ?) " +
                "    AND Actions.name LIKE ? ");
        params.add(type);
        params.add(accountId);
        params.add("%" + searchTerm + "%");

        if (commandName != null && !commandName.isEmpty()) {
            queryBuilder.append("AND CmdDetails.name = ? ");
            params.add(commandName);
        }
        if (commandTimeoutInSeconds != null && !commandTimeoutInSeconds.isEmpty()) {
            queryBuilder.append("AND CmdDetails.timeout_in_secs = ? ");
            params.add(commandTimeoutInSeconds);
        }
        if (categoryList != null && !categoryList.isEmpty()) {
            queryBuilder.append("AND MasterCategoryDetails.name IN (");
            for (int i = 0; i < categoryList.size(); i++) {
                queryBuilder.append("?");
                if (i < categoryList.size() - 1) {
                    queryBuilder.append(",");
                }
            }
            queryBuilder.append(") ");
            params.addAll(categoryList);
        }
        if (status != null && !status.isEmpty()) {
            queryBuilder.append("AND Actions.status = ? ");
            params.add(status);
        }
        if (lastModifiedBy != null && !lastModifiedBy.isEmpty()) {
            queryBuilder.append("AND UA.username = ? ");
            params.add(lastModifiedBy);
        }

        String query = queryBuilder.toString();
        try {
            Integer count = jdbcTemplate.queryForObject(query, Integer.class, params.toArray());
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("Exception encountered while fetching forensic actions count for accountId [{}]. Details: ", accountId, e);
            throw new HealControlCenterException("Error occurred while getting forensic actions count.");
        }
    }

    /**
     * Retrieves all forensic action parameters from the database.
     *
     * @return A list of {@link ForensicActionArgumentsBean} objects.
     * @throws HealControlCenterException if an error occurs while fetching the parameters.
     */
    public List<ForensicActionArgumentsBean> getForensicsParameters() throws HealControlCenterException {
        String query = "SELECT distinct CA.id id, CA.argument_key, CA.argument_value value, CA.default_value defaultValue, MST.name valueType, CA.command_id commandId, " +
                "(SELECT name from mst_sub_type where id = CA.argument_type_id) type " +
                "FROM command_arguments CA, mst_sub_type MST " +
                "WHERE CA.argument_value_type_id = MST.id";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ForensicActionArgumentsBean.class));
        } catch (Exception e) {
            log.error("Exception encountered while fetching command arguments. Details: ", e);
            throw new HealControlCenterException("Error occurred while fetching command arguments.");
        }
    }
}