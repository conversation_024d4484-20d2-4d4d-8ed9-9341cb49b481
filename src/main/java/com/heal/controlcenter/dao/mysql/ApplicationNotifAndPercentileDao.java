package com.heal.controlcenter.dao.mysql;

import com.heal.controlcenter.beans.ApplicationPercentilesBean;
import com.heal.controlcenter.beans.DefaultNotificationPreferences;
import com.heal.controlcenter.exception.HealControlCenterException;
import org.springframework.dao.EmptyResultDataAccessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;

@Slf4j
@Repository
public class ApplicationNotifAndPercentileDao {

    private final JdbcTemplate jdbcTemplate;

    public ApplicationNotifAndPercentileDao(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public int[] addDefaultNotificationPreferences(List<DefaultNotificationPreferences> defaultNotificationPreferences) throws HealControlCenterException {
        String query = "INSERT INTO application_notification_mapping (application_id,notification_type_id,signal_type_id,signal_severity_id,account_id,created_time, updated_time, user_details_id) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        try {
            log.debug("adding default preferences.");
            return jdbcTemplate.batchUpdate(query, new BatchPreparedStatementSetter() {

                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setInt(1, defaultNotificationPreferences.get(i).getApplicationId());
                    ps.setInt(2, defaultNotificationPreferences.get(i).getNotificationTypeId());
                    ps.setInt(3, defaultNotificationPreferences.get(i).getSignalTypeId());
                    ps.setInt(4, defaultNotificationPreferences.get(i).getSignalSeverityId());
                    ps.setInt(5, defaultNotificationPreferences.get(i).getAccountId());
                    ps.setString(6, defaultNotificationPreferences.get(i).getCreatedTime());
                    ps.setString(7, defaultNotificationPreferences.get(i).getUpdatedTime());
                    ps.setString(8, defaultNotificationPreferences.get(i).getUserDetailsId());

                }

                public int getBatchSize() {
                    return defaultNotificationPreferences.size();
                }
            });
        } catch (Exception ex) {
            log.error("Error in adding default notification preferences. Details: ", ex);
            throw new HealControlCenterException("Error in adding default notification preferences.");
        }
    }

    public int[] addApplicationPercentiles(List<ApplicationPercentilesBean> applicationPercentilesBeans) throws HealControlCenterException {
        String query = "INSERT INTO `application_percentiles` (" +
                "application_id, display_name, created_time, updated_time, " +
                "user_details_id, account_id, mst_kpi_details_id) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?)";

        try {
            log.debug("Adding application percentiles");

            return jdbcTemplate.batchUpdate(query, new BatchPreparedStatementSetter() {

                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ApplicationPercentilesBean bean = applicationPercentilesBeans.get(i);
                    ps.setInt(1, bean.getApplicationId());
                    ps.setString(2, bean.getDisplayName());
                    ps.setString(3, bean.getCreatedTime());
                    ps.setString(4, bean.getUpdatedTime());
                    ps.setString(5, bean.getUserDetailsId());
                    ps.setInt(6, bean.getAccountId());
                    ps.setInt(7, bean.getMstKpiDetailsId());
                }

                public int getBatchSize() {
                    return applicationPercentilesBeans.size();
                }

            });
        } catch (Exception ex) {
            log.error("Error in adding application percentiles. Details: ", ex);
            throw new HealControlCenterException("Error in adding application percentiles");
        }
    }

    public List<ApplicationPercentilesBean> getApplicationPercentiles(int applicationId, int accountId) throws HealControlCenterException {
        String query = "SELECT id, application_id, display_name, mst_kpi_details_id, created_time, updated_time, user_details_id, account_id " +
                       "FROM application_percentiles WHERE application_id = ? AND account_id = ?";
        try {
            log.debug("Fetching application percentiles for applicationId: {} and accountId: {}", applicationId, accountId);
            return jdbcTemplate.query(query, (rs, rowNum) ->
                    ApplicationPercentilesBean.builder()
                            .id(rs.getInt("id"))
                            .applicationId(rs.getInt("application_id"))
                            .displayName(rs.getString("display_name"))
                            .mstKpiDetailsId(rs.getInt("mst_kpi_details_id"))
                            .createdTime(rs.getString("created_time"))
                            .updatedTime(rs.getString("updated_time"))
                            .userDetailsId(rs.getString("user_details_id"))
                            .accountId(rs.getInt("account_id"))
                            .build(), applicationId, accountId);
        } catch (EmptyResultDataAccessException e) {
            log.info("No application percentile found for applicationId [{}] and accountId [{}].", applicationId, accountId);
            return Collections.emptyList();
        } catch (Exception ex) {
            log.error("Error in fetching application percentiles. Details: ", ex);
            throw new HealControlCenterException("Error in fetching application percentiles.");
        }
    }

    /**
     * Updates an existing application percentile record in the database.
     *
     * @param applicationPercentilesBean The {@link ApplicationPercentilesBean} containing the updated percentile details.
     * @throws HealControlCenterException if an error occurs during the database update.
     */
    public void updateApplicationPercentiles(ApplicationPercentilesBean applicationPercentilesBean) throws HealControlCenterException {
        String query = "UPDATE application_percentiles SET display_name = ?, mst_kpi_details_id = ?, updated_time = ?, user_details_id = ? " +
                       "WHERE id = ? AND application_id = ? AND account_id = ?";
        try {
            log.debug("Updating application percentile for id: {}, applicationId: {}, accountId: {}",
                    applicationPercentilesBean.getId(), applicationPercentilesBean.getApplicationId(), applicationPercentilesBean.getAccountId());
            int rowsAffected = jdbcTemplate.update(query,
                    applicationPercentilesBean.getDisplayName(),
                    applicationPercentilesBean.getMstKpiDetailsId(),
                    applicationPercentilesBean.getUpdatedTime(),
                    applicationPercentilesBean.getUserDetailsId(),
                    applicationPercentilesBean.getId(),
                    applicationPercentilesBean.getApplicationId(),
                    applicationPercentilesBean.getAccountId());
            log.info("Updated application percentile for id: {}. Rows affected: {}", applicationPercentilesBean.getId(), rowsAffected);
        } catch (Exception ex) {
            log.error("Error in updating application percentiles. Details: ", ex);
            throw new HealControlCenterException("Error in updating application percentiles.");
        }
    }

    /**
     * Retrieves all application percentile records for a given application ID.
     *
     * @param applicationId The ID of the application.
     * @return A list of {@link ApplicationPercentilesBean} objects.
     * @throws HealControlCenterException if a database error occurs.
     */
    public List<ApplicationPercentilesBean> getPercentilesForApplication(int applicationId) throws HealControlCenterException {
        String query = "SELECT id, application_id, account_id, display_name, mst_kpi_details_id, created_time, updated_time, user_details_id " +
                "FROM application_percentiles WHERE application_id = ?";
        try {
            return jdbcTemplate.query(query, (rs, rowNum) ->
                    ApplicationPercentilesBean.builder()
                            .id(rs.getInt("id"))
                            .applicationId(rs.getInt("application_id"))
                            .accountId(rs.getInt("account_id"))
                            .displayName(rs.getString("display_name"))
                            .mstKpiDetailsId(rs.getInt("mst_kpi_details_id"))
                            .createdTime(rs.getString("created_time"))
                            .updatedTime(rs.getString("updated_time"))
                            .userDetailsId(rs.getString("user_details_id"))
                            .build(), applicationId);
        } catch (Exception e) {
            log.error("Error while fetching percentiles for application [{}]. Details: ", applicationId, e);
            throw new HealControlCenterException("Error while fetching percentiles for application " + applicationId);
        }
    }

    /**
     * Deletes an application percentile record by its ID.
     *
     * @param id The ID of the application percentile record.
     * @throws HealControlCenterException if a database error occurs.
     */
    public void deleteApplicationPercentileById(Integer id) throws HealControlCenterException {
        String query = "DELETE FROM application_percentiles WHERE id = ?";
        try {
            jdbcTemplate.update(query, id);
        } catch (Exception e) {
            log.error("Error while deleting percentile with id [{}]. Details: ", id, e);
            throw new HealControlCenterException("Error while deleting percentile with id " + id);
        }
    }

    /**
     * Inserts a new application percentile record into the database.
     *
     * @param beanToCreate The {@link ApplicationPercentilesBean} containing the percentile details to be inserted.
     * @throws HealControlCenterException if a database error occurs.
     */
    public void insertApplicationPercentile(ApplicationPercentilesBean beanToCreate) throws HealControlCenterException {
        String query = "INSERT INTO application_percentiles (application_id, account_id, display_name, mst_kpi_details_id, created_time, updated_time, user_details_id) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?)";
        try {
            jdbcTemplate.update(query,
                    beanToCreate.getApplicationId(),
                    beanToCreate.getAccountId(),
                    beanToCreate.getDisplayName(),
                    beanToCreate.getMstKpiDetailsId(),
                    beanToCreate.getCreatedTime(),
                    beanToCreate.getUpdatedTime(),
                    beanToCreate.getUserDetailsId());
        } catch (Exception e) {
            log.error("Error while inserting new percentile for application [{}]. Details: ", beanToCreate.getApplicationId(), e);
            throw new HealControlCenterException("Error while inserting new percentile for application " + beanToCreate.getApplicationId());
        }
    }
}