package com.heal.controlcenter.dao.mysql;

import com.heal.configuration.pojos.PairData;
import com.heal.configuration.pojos.PersistenceSuppressionConfiguration;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.pojo.RulesHelperPojo;
import com.heal.controlcenter.util.PaginationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Repository
public class AccountServiceDao {

    private final JdbcTemplate jdbcTemplate;

    public AccountServiceDao(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * Retrieves a list of view types (subtypes) for a given type ID from the view_types table.
     *
     * @param typeId the type ID for which subtypes are to be fetched
     * @return List of ViewTypesBean objects representing the subtypes
     */
    public List<ViewTypesBean> getMstSubTypeByTypeId(int typeId) {
        String query = "SELECT type AS typeName, typeid AS typeId, name AS subTypeName, subtypeid AS subTypeId " +
                "FROM view_types WHERE typeid = ?";
        try {
            log.debug("Querying for view types with typeId: {}", typeId);
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ViewTypesBean.class), typeId);
        } catch (Exception e) {
            log.error("Error fetching master type info from 'view_types' for typeId [{}].", typeId, e);
        }
        return Collections.emptyList();
    }

    /**
     * Retrieves tag details for a given tag name and account ID.
     *
     * @param name the tag name
     * @param accountId the account ID
     * @return TagDetailsBean object containing tag details, or null if not found
     */
    public TagDetailsBean getTagDetails(String name, int accountId) {
        String query = "SELECT id, name, tag_type_id AS tagTypeId, is_predefined AS isPredefined, ref_table AS refTable, " +
                "created_time AS createdTime, updated_time AS updatedTime, account_id AS accountId, user_details_id AS userDetailsId, " +
                "ref_where_column_name AS refWhereColumnName, ref_select_column_name AS refSelectColumnName " +
                "FROM tag_details WHERE name = ? AND account_id IN (1, ?)";
        try {
            log.debug("Querying tag_details for name: {} and accountId: {}", name, accountId);
            return jdbcTemplate.queryForObject(query,
                    new BeanPropertyRowMapper<>(TagDetailsBean.class),
                    name, accountId);
        } catch (Exception e) {
            log.error("Error fetching tag_details for name: {} and accountId: {}", name, accountId, e);
            return null;
        }
    }

    /**
     * Adds a new controller to the database.
     *
     * @param controllerBean the ControllerBean object containing controller details
     * @return the generated ID of the inserted controller, or -1 if insertion failed
     */
    public int addController(ControllerBean controllerBean) throws HealControlCenterException {
        String query = "INSERT INTO controller " +
                "(name, identifier, account_id, user_details_id, created_time, updated_time, controller_type_id, " +
                "monitor_enabled, status, plugin_supr_interval, plugin_whitelist_status, environment) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        KeyHolder keyHolder = new GeneratedKeyHolder();

        try {
            jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(query, Statement.RETURN_GENERATED_KEYS);
                ps.setString(1, controllerBean.getName());
                ps.setString(2, controllerBean.getIdentifier());
                ps.setInt(3, controllerBean.getAccountId());
                ps.setString(4, controllerBean.getLastModifiedBy()); // user_details_id
                ps.setString(5, controllerBean.getCreatedTime());
                ps.setString(6, controllerBean.getUpdatedTime());
                ps.setInt(7, controllerBean.getControllerTypeId());
                ps.setBoolean(8, true);
                ps.setInt(9, controllerBean.getStatus());
                ps.setInt(10, controllerBean.getPluginSuppressionInterval());
                ps.setBoolean(11, controllerBean.isPluginWhitelisted());
                ps.setString(12, controllerBean.getEnvironment());

                return ps;
            }, keyHolder);

            Number key = keyHolder.getKey();
            if (key != null) {
                return key.intValue();
            } else {
                throw new HealControlCenterException("Failed to insert controller, no generated key returned.");
            }
        } catch (Exception e) {
            log.error("Error inserting controller: {}", controllerBean.getIdentifier(), e);
            throw new HealControlCenterException("Error inserting controller: " + e.getMessage());
        }
    }

    /**
     * Retrieves the tag mapping ID for the given parameters if it exists.
     *
     * @param tagId the tag ID
     * @param objectId the object ID
     * @param objectRefTable the reference table name
     * @param tagKey the tag key
     * @param tagValue the tag value
     * @param accountId the account ID
     * @return the tag mapping ID if found, otherwise null
     */
    public Integer getTagMappingId(int tagId, int objectId, String objectRefTable,
                                   String tagKey, String tagValue, int accountId) {
        String query = "SELECT id FROM tag_mapping WHERE tag_id = ? AND object_id = ? AND object_ref_table = ? " +
                "AND tag_key = ? AND tag_value = ? AND account_id = ?";
        try {
            log.debug("Querying tag_mapping for tagId: {}, objectId: {}, objectRefTable: {}, tagKey: {}, tagValue: {}, accountId: {}",
                    tagId, objectId, objectRefTable, tagKey, tagValue, accountId);
            return jdbcTemplate.queryForObject(query, Integer.class, tagId, objectId, objectRefTable, tagKey, tagValue, accountId);
        } catch (Exception e) {
            log.warn("No tag mapping found or error occurred for tagId: {}, objectId: {}, objectRefTable: {}, tagKey: {}, tagValue: {}, accountId: {}",
                    tagId, objectId, objectRefTable, tagKey, tagValue, accountId, e);
            return null;
        }
    }

    /**
     * Adds a new tag mapping record to the database.
     *
     * @param bean the TagMappingBean containing tag mapping details
     * @return the generated ID of the inserted tag mapping, or -1 if insertion failed
     */
    public int addTagMappingDetails(TagMappingBean bean) throws HealControlCenterException {
        final String query = """
        INSERT INTO tag_mapping (
            tag_id, object_id, object_ref_table, tag_key, tag_value,
            created_time, updated_time, account_id, user_details_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    """;

        KeyHolder keyHolder = new GeneratedKeyHolder();

        try {
            int rows = jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(query, Statement.RETURN_GENERATED_KEYS);
                ps.setInt(1, bean.getTagId());
                ps.setInt(2, bean.getObjectId());
                ps.setString(3, bean.getObjectRefTable());
                ps.setString(4, bean.getTagKey());
                ps.setString(5, bean.getTagValue());
                ps.setString(6, bean.getCreatedTime());
                ps.setString(7, bean.getUpdatedTime());
                ps.setInt(8, bean.getAccountId());
                ps.setString(9, bean.getUserDetailsId());
                return ps;
            }, keyHolder);

            if (rows > 0 && keyHolder.getKey() != null) {
                int generatedId = keyHolder.getKey().intValue();
                log.debug("Inserted tag_mapping row with generated ID: {}", generatedId);
                return generatedId;
            } else {
                log.warn("Tag mapping insert did not return a generated ID. Bean: {}", bean);
                throw new HealControlCenterException("Tag mapping insert did not return a generated ID.");
            }

        } catch (Exception e) {
            log.error("Error inserting tag_mapping for objectId: {}, tagId: {}",
                    bean.getObjectId(), bean.getTagId(), e);
            throw new HealControlCenterException("Error inserting tag_mapping: " + e.getMessage());
        }
    }

    /**
     * Retrieves a list of RulesHelperPojo for the given account and service.
     *
     * @param accountId the account ID
     * @param serviceId the service ID
     * @return List of RulesHelperPojo objects for the service
     */
    public List<RulesHelperPojo> getRulesHelperPojo(int accountId, int serviceId) {
        String query = """
        SELECT
            r.id AS id,
            r.name AS name,
            r.is_enabled AS enabled,
            r.`order` AS `order`,
            r.rule_type_id AS ruleTypeId,
            r.is_default AS isDefault,
            r.max_tags AS maxTags,
            r.discovery_status AS discoveryEnabled,
            tp.id AS tcpId,
            tp.initial_pattern AS tcpInitialPattern,
            tp.last_pattern AS tcpLastPattern,
            tp.length AS tcpLength,
            hp.id AS httpId,
            hp.first_uri_segments AS httpFirstUriSegments,
            hp.last_uri_segments AS httpLastUriSegments,
            hp.complete_uri AS httpCompleteURI,
            hp.payload_type_id AS httpPayloadTypeId,
            tm.tag_key AS concernedConfigId
        FROM controller c, tag_mapping tm, rules r
        LEFT JOIN tcp_patterns tp ON tp.rule_id = r.id
        LEFT JOIN http_patterns hp ON hp.rule_id = r.id
        WHERE tm.tag_id = 1
          AND tm.object_ref_table = 'rules'
          AND r.id = tm.object_id
          AND c.id = tm.tag_key
          AND c.account_id = ?
          AND tm.tag_key = ?
        """;

        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(RulesHelperPojo.class), accountId, serviceId);
        } catch (Exception e) {
            log.error("Failed to fetch RulesHelperPojo for accountId [{}] and serviceId [{}]", accountId, serviceId, e);
            return Collections.emptyList();
        }
    }

    /**
     * Retrieves a list of PairData for the given rule and HTTP pattern.
     *
     * @param ruleId the rule ID
     * @param httpPatternId the HTTP pattern ID
     * @return List of PairData objects for the rule and HTTP pattern
     */
    public List<PairData> getDataBeans(int ruleId, int httpPatternId) {
        String query = """
        SELECT id, pair_type_id AS pairTypeId, pair_key AS pairKey, pair_value AS pairValue
        FROM http_pair_data
        WHERE rule_id = ? AND http_pattern_id = ?
    """;

        try {
            log.debug("Fetching PairData for ruleId: {}, httpPatternId: {}", ruleId, httpPatternId);
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(PairData.class), ruleId, httpPatternId);
        } catch (Exception e) {
            log.error("Error fetching PairData for ruleId: {}, httpPatternId: {}", ruleId, httpPatternId, e);
            return Collections.emptyList();
        }
    }

    /**
     * Copies anomaly suppression configuration from an application to a service.
     *
     * @param applicationId the application ID
     * @param serviceId     the service ID
     */
    public void copyAnomalySuppressionFromAppToService(int applicationId, int serviceId) throws HealControlCenterException {
        String query = """
        INSERT INTO service_anomaly_ps_configurations (
            service_id, account_id, start_collection_interval, end_collection_interval,
            low_persistence, low_suppression, medium_persistence, medium_suppression,
            high_persistence, high_suppression, created_time, updated_time, user_details_id
        )
        SELECT ?, account_id, start_collection_interval, end_collection_interval,
               low_persistence, low_suppression, medium_persistence, medium_suppression,
               high_persistence, high_suppression, created_time, updated_time, user_details_id
        FROM application_anomaly_ps_configurations
        WHERE application_id = ?
    """;

        try {
            int rowsAffected = jdbcTemplate.update(query, serviceId, applicationId);
            if (rowsAffected == 0) {
                log.warn("Copying anomaly suppression config from application [{}] to service [{}] affected 0 rows.", applicationId, serviceId);
            }
        } catch (Exception e) {
            log.error("Failed to copy anomaly suppression config from application [{}] to service [{}]", applicationId, serviceId, e);
            throw new HealControlCenterException("Failed to copy anomaly suppression config: " + e.getMessage());
        }
    }

    /**
     * Retrieves anomaly suppression configuration for a given service.
     *
     * @param serviceId the service ID
     * @return List of PersistenceSuppressionConfiguration objects for the service
     */
    public List<PersistenceSuppressionConfiguration> getAnomalySuppressionForService(int serviceId) {
        String query = """
        SELECT start_collection_interval, end_collection_interval, low_persistence, low_suppression,
               medium_persistence, medium_suppression, high_persistence, high_suppression,
               created_time, updated_time, user_details_id
        FROM service_anomaly_ps_configurations
        WHERE service_id = ?
    """;

        try {
            return jdbcTemplate.query(query, (rs, rowNum) -> {
                PersistenceSuppressionConfiguration config = new PersistenceSuppressionConfiguration();
                config.setStartCollectionInterval(rs.getInt("start_collection_interval"));
                config.setEndCollectionInterval(rs.getInt("end_collection_interval"));
                config.setLowPersistence(rs.getInt("low_persistence"));
                config.setLowSuppression(rs.getInt("low_suppression"));
                config.setMediumPersistence(rs.getInt("medium_persistence"));
                config.setMediumSuppression(rs.getInt("medium_suppression"));
                config.setHighPersistence(rs.getInt("high_persistence"));
                config.setHighSuppression(rs.getInt("high_suppression"));
                config.setCreatedTime(rs.getString("created_time"));
                config.setUpdatedTime(rs.getString("updated_time"));
                config.setUserDetailsId(rs.getString("user_details_id"));
                return config;
            }, serviceId);
        } catch (Exception e) {
            log.error("Failed to fetch anomaly suppression config for serviceId [{}]", serviceId, e);
            return Collections.emptyList();
        }
    }

    /**
     * Retrieves a linked service alias by its common name.
     *
     * @param commonName the common name of the service alias
     * @return ServiceAliases object if found, otherwise null
     */
    public ServiceAliases getLinkedServiceByCommonName(String commonName) {
        String query = """
        SELECT id, common_name AS commonName, dc_service_identifier AS dcServiceIdentifier,
               dr_service_identifier AS drServiceIdentifier, user_details_id AS userDetailsId,
               created_time AS createdTime, updated_time AS updatedTime, status
        FROM service_aliases
        WHERE common_name = ?
    """;
        try {
            return jdbcTemplate.queryForObject(query,
                    new BeanPropertyRowMapper<>(ServiceAliases.class),
                    commonName);
        } catch (EmptyResultDataAccessException e) {
            log.warn("No linked service found for commonName [{}]", commonName);
            return null;
        }
    }

    /**
     * Inserts a new linked service alias into the database.
     *
     * @param commonName the common name
     * @param dcIdentifier the DC service identifier
     * @param drIdentifier the DR service identifier
     * @param userDetailsId the user details ID
     * @param createdTime the creation time
     * @param updatedTime the update time
     * @param status the status
     * @return the number of rows affected, or -1 if insertion failed
     */
    public int insertLinkedService(String commonName, String dcIdentifier, String drIdentifier,
                                   String userDetailsId, String createdTime, String updatedTime, int status) throws HealControlCenterException {
        String query = """
            INSERT INTO service_aliases (
                common_name, dc_service_identifier, dr_service_identifier,
                user_details_id, created_time, updated_time, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        """;
        try {
            int rowsAffected = jdbcTemplate.update(query, commonName, dcIdentifier, drIdentifier,
                    userDetailsId, createdTime, updatedTime, status);
            if (rowsAffected == 0) {
                throw new HealControlCenterException("Failed to insert linked service, 0 rows affected.");
            }
            return rowsAffected;
        } catch (Exception e) {
            log.error("Failed to insert linked service entry for [{}]", commonName, e);
            throw new HealControlCenterException("Failed to insert linked service entry: " + e.getMessage());
        }
    }

    /**
     * Finds a service group by its identifier and account ID.
     *
     * @param identifier the unique identifier of the service group
     * @param accountId  the account ID
     * @return ServiceGroupBean if found, otherwise null
     */
    public ServiceGroupBean findServiceGroupByIdentifier(String identifier, int accountId) {
        String query = "SELECT id, name, identifier, status, account_id FROM service_group WHERE identifier = ? AND account_id = ?";
        try {
            // This mapper automatically handles mapping columns like 'account_id' to 'accountId'.
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(ServiceGroupBean.class), identifier, accountId);
        } catch (Exception e) {
            // This catch block handles cases where no rows are found (EmptyResultDataAccessException)
            log.warn("No service group found for identifier [{}] and accountId [{}]", identifier, accountId);
            return null;
        }
    }

    /**
     * Creates a mapping between a service and a service group.
     *
     * @param serviceId      the service ID
     * @param serviceGroupId the service group ID
     * @param userId         the user ID performing the operation
     * @return the number of rows affected, or -1 if the operation failed
     */
    public int createServiceGroupMapping(int serviceId, int serviceGroupId, String userId) throws HealControlCenterException {
        String query = """
        INSERT INTO service_group_mapping (service_id, service_group_id, created_time, updated_time, user_details_id)
        VALUES (?, ?, NOW(), NOW(), ?)
    """;
        try {
            int rowsAffected = jdbcTemplate.update(query, serviceId, serviceGroupId, userId);
            if (rowsAffected == 0) {
                throw new HealControlCenterException("Failed to create service group mapping, 0 rows affected.");
            }
            return rowsAffected;
        } catch (Exception e) {
            log.error("Failed to map service [id: {}] to service group [id: {}]", serviceId, serviceGroupId, e);
            throw new HealControlCenterException("Failed to map service to service group: " + e.getMessage());
        }
    }

    /**
     * Checks if a tag mapping exists for the given parameters.
     *
     * @param tagId          the tag ID
     * @param objectId       the object ID
     * @param objectRefTable the reference table name
     * @param tagKey         the tag key
     * @param tagValue       the tag value
     * @param accountId      the account ID
     * @return true if the tag mapping exists, false otherwise
     */
    public boolean isTagMappingPresent(int tagId, int objectId, String objectRefTable,
                                       String tagKey, String tagValue, int accountId) {
        String query = "SELECT COUNT(*) FROM tag_mapping " +
                "WHERE tag_id = ? AND object_id = ? AND object_ref_table = ? " +
                "AND tag_key = ? AND tag_value = ? AND account_id = ?";
        Integer count = jdbcTemplate.queryForObject(query, Integer.class,
                tagId, objectId, objectRefTable, tagKey, tagValue, accountId);
        return count != null && count > 0;
    }


    /**
     * Retrieves the service group details for a given service.
     *
     * @param serviceId the service ID
     * @return ServiceGroupBean if found, otherwise null
     */
    public ServiceGroupBean getServiceGroupForService(int serviceId) {
        String query = "SELECT sg.id, sg.name, sg.identifier, sg.status, sg.account_id " +
                "FROM service_group sg JOIN service_group_mapping sgm ON sg.id = sgm.service_group_id " +
                "WHERE sgm.service_id = ?";
        try {
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(ServiceGroupBean.class), serviceId);
        } catch (EmptyResultDataAccessException e) {
            log.info("No service group found for serviceId [{}]", serviceId);
            return null;
        } catch (Exception e) {
            log.error("Error fetching service group for serviceId [{}]", serviceId, e);
        }
        return null;
    }

    public ServiceAliases getLinkedServiceByIdentifier(String identifier) {
        String query = """
        SELECT id, common_name AS commonName, dc_service_identifier AS dcServiceIdentifier,
               dr_service_identifier AS drServiceIdentifier, user_details_id AS userDetailsId,
               created_time AS createdTime, updated_time AS updatedTime, status
        FROM service_aliases
        WHERE dc_service_identifier = ? OR dr_service_identifier = ?
        LIMIT 1
    """;
        try {
            return jdbcTemplate.queryForObject(query,
                    new BeanPropertyRowMapper<>(ServiceAliases.class), identifier, identifier);
        } catch (EmptyResultDataAccessException e) {
            log.warn("No linked service found for identifier [{}]", identifier);

            return null;
        }
    }


    public String getTagValueForService(int serviceId, String tagType) {
        String query = "SELECT tm.tag_value FROM tag_mapping tm " +
                       "JOIN tag_details td ON tm.tag_id = td.id " +
                       "WHERE tm.object_id = ? AND tm.object_ref_table = 'controller' AND td.name = ?";
        try {
            return jdbcTemplate.queryForObject(query, String.class, serviceId, tagType);
        } catch (EmptyResultDataAccessException e) {
            log.info("No tag value found for serviceId [{}] and tagType [{}]", serviceId, tagType);
            return null;
        } catch (Exception e) {
            log.error("Error fetching tag value for serviceId [{}] and tagType [{}]", serviceId, tagType, e);
            return null;
        }
    }

    public boolean isEntryPointService(int serviceId) {
        String query = "SELECT COUNT(*) FROM tag_mapping tm " +
                       "JOIN tag_details td ON tm.tag_id = td.id " +
                       "WHERE tm.object_id = ? AND tm.object_ref_table = 'controller' AND td.name = 'Entry-Point' AND tm.tag_value = '1'";
        try {
            Integer count = jdbcTemplate.queryForObject(query, Integer.class, serviceId);
            return count != null && count > 0;
        } catch (EmptyResultDataAccessException e) {
            log.info("Service [{}] is not an Entry-Point service.", serviceId);
            return false;
        } catch (Exception e) {
            log.error("Error checking if service [{}] is an Entry-Point service.", serviceId, e);
            return false;
        }
    }

    public List<ServiceGroupBean> getServiceGroups(int accountId, String searchTerm, Pageable pageable) throws HealControlCenterException {
        String baseQuery = "SELECT id, name, identifier, status, created_time, updated_time, user_details_id, account_id, service_group_id " +
                           "FROM service_group WHERE account_id = ?";
        List<Object> params = new ArrayList<>();
        params.add(accountId);

        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            baseQuery += " AND (LOWER(name) LIKE ? OR LOWER(identifier) LIKE ?)";
            String likeTerm = "%" + searchTerm.toLowerCase().trim() + "%";
            params.add(likeTerm);
            params.add(likeTerm);
        }

        String sql = PaginationUtils.applyPagination(baseQuery, pageable);
        params = PaginationUtils.buildPaginationParams(params, pageable);

        try {
            return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(ServiceGroupBean.class), params.toArray());
        } catch (Exception e) {
            log.error("Error fetching service groups for accountId [{}]", accountId, e);
            throw new HealControlCenterException("Error fetching service groups");
        }
    }

    public int getServiceGroupsCount(int accountId, String searchTerm) throws HealControlCenterException {
        String baseQuery = "SELECT COUNT(*) FROM service_group WHERE account_id = ?";
        List<Object> params = new ArrayList<>();
        params.add(accountId);

        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            baseQuery += " AND (LOWER(name) LIKE ? OR LOWER(identifier) LIKE ?)";
            String likeTerm = "%" + searchTerm.toLowerCase().trim() + "%";
            params.add(likeTerm);
            params.add(likeTerm);
        }

        try {
            Integer count = jdbcTemplate.queryForObject(baseQuery, Integer.class, params.toArray());
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("Error counting service groups for accountId [{}]", accountId, e);
            throw new HealControlCenterException("Error counting service groups");
        }
    }

    public ServiceGroupBean getServiceGroupById(int serviceGroupId) throws HealControlCenterException {
        String query = "SELECT id, name, identifier, status, created_time, updated_time, user_details_id, account_id, service_group_id " +
                       "FROM service_group WHERE id = ?";
        try {
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(ServiceGroupBean.class), serviceGroupId);
        } catch (EmptyResultDataAccessException e) {
            log.info("No service group found for id [{}]", serviceGroupId);
            return null;
        } catch (Exception e) {
            log.error("Error fetching service group by id [{}]", serviceGroupId, e);
            throw new HealControlCenterException("Error fetching service group by id");
        }
    }

    public List<ServiceGroupBean> getServiceGroupsByIds(List<Integer> serviceGroupIds) throws HealControlCenterException {
        if (serviceGroupIds == null || serviceGroupIds.isEmpty()) {
            return Collections.emptyList();
        }
        String query = "SELECT id, name, identifier, status, created_time, updated_time, user_details_id, account_id, service_group_id " +
                "FROM service_group WHERE id IN (" + String.join(",", Collections.nCopies(serviceGroupIds.size(), "?")) + ")";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ServiceGroupBean.class), serviceGroupIds.toArray());
        } catch (Exception e) {
            log.error("Error fetching service groups by ids", e);
            throw new HealControlCenterException("Error fetching service groups by ids");
        }
    }

    public void deleteServiceGroupMapping(int serviceId) throws HealControlCenterException {
        String query = "delete from service_group_mapping where service_id = ?";
        try {
            jdbcTemplate.update(query, serviceId);
        } catch (Exception e) {
            log.error("Error deleting service group mapping for serviceId [{}].", serviceId, e);
            throw new HealControlCenterException("Error deleting service group mapping: " + e.getMessage());
        }
    }

    public void deleteTagMapping(int tagId, int objectId, String objectRefTable, int accountId) throws HealControlCenterException {
        String query = "delete from tag_mapping where tag_id = ? and object_id = ? and object_ref_table = ? and account_id = ?";
        try {
            jdbcTemplate.update(query, tagId, objectId, objectRefTable, accountId);
        } catch (Exception e) {
            log.error("Error deleting tag mapping.", e);
            throw new HealControlCenterException("Error deleting tag mapping: " + e.getMessage());
        }
    }

    public int updateLinkedService(String commonName, String dcIdentifier, String drIdentifier, String userId, String updatedTime, int status) throws HealControlCenterException {
        String query = "update service_aliases set dc_service_identifier = ?, dr_service_identifier = ?, user_details_id = ?, updated_time = ?, status = ? where common_name = ?";
        try {
            return jdbcTemplate.update(query, dcIdentifier, drIdentifier, userId, updatedTime, status, commonName);
        } catch (Exception e) {
            log.error("Error updating linked service.", e);
            throw new HealControlCenterException("Error updating linked service: " + e.getMessage());
        }
    }

    /**
     * Updates the common name and other details of a service alias in the database.
//     * @param oldCommonName The current common name used to identify the record.
     * @param newCommonName The new common name to set.
     * @param dcIdentifier The DC service identifier.
     * @param drIdentifier The DR service identifier.
     * @param userId The user details ID.
     * @param updatedTime The update time.
     * @param status The status.
     * @return The number of rows affected.
     */
    public int updateServiceAliasCommonName(String newCommonName, String dcIdentifier, String drIdentifier, String userId, String updatedTime, int status, String serviceIdentifier) throws HealControlCenterException {
        String query = "UPDATE service_aliases SET common_name = ?, dc_service_identifier = ?, dr_service_identifier = ?, user_details_id = ?, updated_time = ?, status = ? WHERE dc_service_identifier = ? OR dr_service_identifier = ?";
        try {
            return jdbcTemplate.update(query, newCommonName, dcIdentifier, drIdentifier, userId, updatedTime, status, serviceIdentifier, serviceIdentifier);
        } catch (Exception e) {
            log.error("Error updating service alias common name.", e);
            throw new HealControlCenterException("Error updating service alias common name: " + e.getMessage());
        }
    }

    /**
     * Deletes service alias entries from the service_aliases table based on provided service identifiers.
     * This method will delete rows where either dc_service_identifier or dr_service_identifier
     * matches the given serviceIdentifier or mappedServiceIdentifier.
     *
     * @param serviceIdentifier       The identifier of the primary service.
     * @param mappedServiceIdentifier The identifier of the mapped service.
     */
    public void deleteServiceAliasesByIdentifiers(String serviceIdentifier, String mappedServiceIdentifier) throws HealControlCenterException {
        String query = "DELETE FROM service_aliases WHERE dc_service_identifier = ? OR dr_service_identifier = ? OR dc_service_identifier = ? OR dr_service_identifier = ?";
        try {
            log.info("Deleting service aliases for serviceIdentifier: {} and mappedServiceIdentifier: {}", serviceIdentifier, mappedServiceIdentifier);
            jdbcTemplate.update(query, serviceIdentifier, serviceIdentifier, mappedServiceIdentifier, mappedServiceIdentifier);
        } catch (Exception e) {
            log.error("Error deleting service aliases for identifiers [{}, {}].", serviceIdentifier, mappedServiceIdentifier, e);
            throw new HealControlCenterException("Error deleting service aliases: " + e.getMessage());
        }
    }

    /**
     * Deletes service group mapping entries from the service_group_mapping table based on service ID.
     *
     * @param serviceId The ID of the service.
     */
    public void hardDeleteServiceGroupMappingByServiceId(int serviceId) throws HealControlCenterException {
        String query = "DELETE FROM service_group_mapping WHERE service_id = ?";
        try {
            jdbcTemplate.update(query, serviceId);
        } catch (Exception e) {
            log.error("Error hard deleting service_group_mapping for serviceId [{}].", serviceId, e);
            throw new HealControlCenterException("Error hard deleting service group mapping by service id: " + e.getMessage());
        }
    }

    /**
     * Updates the status of a service alias in the database.
     *
     * @param identifier The service identifier (DC or DR) to update.
     * @param status     The new status to set (e.g., 0 for inactive).
     * @param userId     The ID of the user performing the update.
     */
    public void updateServiceAliasStatus(String identifier, int status, String userId) throws HealControlCenterException {
        String query = "UPDATE service_aliases SET status = ?, user_details_id = ?, updated_time = NOW() WHERE dc_service_identifier = ? OR dr_service_identifier = ?";
        try {
            jdbcTemplate.update(query, status, userId, identifier, identifier);
        } catch (Exception e) {
            log.error("Error updating service_aliases status for identifier [{}].", identifier, e);
            throw new HealControlCenterException("Error updating service alias status: " + e.getMessage());
        }
    }

    /**
     * Updates the status of a service group mapping in the database.
     *
     * @param serviceId The ID of the service.
     * @param status    The new status to set (e.g., 0 for inactive).
     * @param userId    The ID of the user performing the update.
     * @return The number of rows affected.
     */
    public int updateServiceGroupMappingStatus(int serviceId, int status, String userId) throws HealControlCenterException {
        String query = "UPDATE service_group_mapping SET status = ?, user_details_id = ?, updated_time = NOW() WHERE service_id = ?";
        try {
            return jdbcTemplate.update(query, status, userId, serviceId);
        } catch (Exception e) {
            log.error("Error updating service_group_mapping status for serviceId [{}].", serviceId, e);
            throw new HealControlCenterException("Error updating service group mapping status: " + e.getMessage());
        }
    }

    /**
     * Deletes tag mapping entries from the tag_mapping table based on entity ID and object reference table.
     *
     * @param entityId       The ID of the entity (e.g., service ID).
     * @param objectRefTable The reference table name (e.g., "controller").
     */
    public void deleteTagMappingByEntityId(int entityId, String objectRefTable) throws HealControlCenterException {
        String query = "DELETE FROM tag_mapping WHERE object_id = ? AND object_ref_table = ?";
        try {
            jdbcTemplate.update(query, entityId, objectRefTable);
        } catch (Exception e) {
            log.error("Error deleting tag_mapping for entityId [{}] and objectRefTable [{}].", entityId, objectRefTable, e);
            throw new HealControlCenterException("Error deleting tag mapping by entity id: " + e.getMessage());
        }
    }

    /**
     * Deletes service alias entries from the service_aliases table based on a single service identifier.
     * This method will delete rows where either dc_service_identifier or dr_service_identifier
     * matches the given identifier.
     *
     * @param identifier The service identifier (DC or DR) to delete.
     */
    public void deleteServiceAliasByIdentifier(String identifier) throws HealControlCenterException {
        String query = "DELETE FROM service_aliases WHERE dc_service_identifier = ? OR dr_service_identifier = ?";
        try {
            jdbcTemplate.update(query, identifier, identifier);
        } catch (Exception e) {
            log.error("Error deleting service_aliases for identifier [{}].", identifier, e);
            throw new HealControlCenterException("Error deleting service alias by identifier: " + e.getMessage());
        }
    }

    /**
     * Deletes anomaly suppression configurations for a given service ID.
     *
     * @param serviceId The ID of the service.
     */
    public void deleteAnomalySuppressionByServiceId(int serviceId) throws HealControlCenterException {
        String query = "DELETE FROM service_anomaly_ps_configurations WHERE service_id = ?";
        try {
            jdbcTemplate.update(query, serviceId);
        } catch (Exception e) {
            log.error("Error deleting service_anomaly_ps_configurations for serviceId [{}].", serviceId, e);
            throw new HealControlCenterException("Error deleting anomaly suppression by service id: " + e.getMessage());
        }
    }

    /**
     * Retrieves tags for a list of services.
     *
     * @param serviceIds the list of service IDs
     * @return Map of service ID to a map of tag name to tag value
     */
    public Map<Integer, Map<String, String>> getTagsForServices(List<Integer> serviceIds) {
        if (serviceIds == null || serviceIds.isEmpty()) {
            return Collections.emptyMap();
        }
        String query = "SELECT tm.object_id, td.name, tm.tag_value " +
                "FROM tag_mapping tm JOIN tag_details td ON tm.tag_id = td.id " +
                "WHERE tm.object_ref_table = 'controller' AND tm.object_id IN (" +
                String.join(",", Collections.nCopies(serviceIds.size(), "?")) + ")";

        return jdbcTemplate.query(query, ps -> {
            for (int i = 0; i < serviceIds.size(); i++) {
                ps.setInt(i + 1, serviceIds.get(i));
            }
        }, rs -> {
            Map<Integer, Map<String, String>> tagsByService = new HashMap<>();
            while (rs.next()) {
                int serviceId = rs.getInt("object_id");
                String tagName = rs.getString("name");
                String tagValue = rs.getString("tag_value");
                tagsByService.computeIfAbsent(serviceId, k -> new HashMap<>()).put(tagName, tagValue);
            }
            return tagsByService;
        });
    }

    /**
     * Retrieves the IDs of entry point services from a list of service IDs.
     *
     * @param serviceIds the list of service IDs
     * @return List of entry point service IDs
     */
    public List<Integer> getEntryPointServiceIds(List<Integer> serviceIds) {
        if (serviceIds == null || serviceIds.isEmpty()) {
            return Collections.emptyList();
        }
        String query = "SELECT tm.object_id FROM tag_mapping tm " +
                "JOIN tag_details td ON tm.tag_id = td.id " +
                "WHERE tm.object_ref_table = 'controller' AND td.name = 'Entry-Point' AND tm.tag_value = '1' " +
                "AND tm.object_id IN (" +
                String.join(",", Collections.nCopies(serviceIds.size(), "?")) + ")";

        return jdbcTemplate.query(query, ps -> {
            for (int i = 0; i < serviceIds.size(); i++) {
                ps.setInt(i + 1, serviceIds.get(i));
            }
        }, rs -> {
            List<Integer> entryPointIds = new ArrayList<>();
            while (rs.next()) {
                entryPointIds.add(rs.getInt("object_id"));
            }
            return entryPointIds;
        });
    }

    /**
     * Retrieves the service group details for a list of services.
     *
     * @param serviceIds the list of service IDs
     * @return Map of service ID to ServiceGroupBean
     */
    public Map<Integer, ServiceGroupBean> getServiceGroupsForServices(List<Integer> serviceIds) {
        if (serviceIds == null || serviceIds.isEmpty()) {
            return Collections.emptyMap();
        }
        String query = "SELECT sgm.service_id, sg.id, sg.name, sg.identifier, sg.status, sg.account_id " +
                "FROM service_group sg JOIN service_group_mapping sgm ON sg.id = sgm.service_group_id " +
                "WHERE sgm.service_id IN (" +
                String.join(",", Collections.nCopies(serviceIds.size(), "?")) + ")";

        return jdbcTemplate.query(query, ps -> {
            for (int i = 0; i < serviceIds.size(); i++) {
                ps.setInt(i + 1, serviceIds.get(i));
            }
        }, rs -> {
            Map<Integer, ServiceGroupBean> serviceGroupsByService = new HashMap<>();
            while (rs.next()) {
                int serviceId = rs.getInt("service_id");
                ServiceGroupBean sg = new ServiceGroupBean();
                sg.setId(rs.getInt("id"));
                sg.setName(rs.getString("name"));
                sg.setIdentifier(rs.getString("identifier"));
                sg.setStatus(rs.getInt("status"));
                sg.setAccountId(rs.getInt("account_id"));
                serviceGroupsByService.put(serviceId, sg);
            }
            return serviceGroupsByService;
        });
    }
}
