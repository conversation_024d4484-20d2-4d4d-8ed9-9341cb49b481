package com.heal.controlcenter.dao.mysql;

import com.heal.controlcenter.beans.CategoryDetailBean;
import com.heal.controlcenter.beans.ViewTypesBean;
import com.heal.controlcenter.dao.mysql.entity.CountBean;
import com.heal.controlcenter.exception.HealControlCenterException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
@Repository
public class CategoryDao {

    private final JdbcTemplate jdbcTemplate;

    public CategoryDao(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * Gets the count of categories for a given account.
     *
     * @param accountId the account ID
     * @return the count of categories
     * @throws HealControlCenterException if an error occurs during retrieval
     */
    public Integer getCategoryCountForAccount(Integer accountId) throws HealControlCenterException {
        String query = "select count(id) from mst_category_details where account_id in (1, " + accountId + ")";
        log.debug("Preparing to get category count for accountId: {} with query: {}", accountId, query);
        try {
            log.debug("Executing query to get category count for accountId: {}", accountId);
            Integer count = jdbcTemplate.queryForObject(query, Integer.class);
            log.debug("Category count for accountId {}: {}", accountId, count);
            return count;
        } catch (EmptyResultDataAccessException e) {
            log.info("Categories unavailable for accountId [{}]", accountId);
            return 0;
        } catch (Exception e) {
            log.error("Exception encountered while fetching category count from 'mst_category_details' table " +
                    "for accountId [{}]. Details: ", accountId, e);
            throw new HealControlCenterException("Error occurred while getting category count.");
        }
    }

    /**
     * Inserts a new category into the database.
     *
     * @param categoryBean the category details
     * @return the generated category ID
     * @throws HealControlCenterException if an error occurs during insertion
     */
    public int insertCategory(CategoryDetailBean categoryBean) throws HealControlCenterException {
        String query = "INSERT INTO mst_category_details " +
                "(name, account_id, created_time, updated_time, user_details_id, identifier, status, is_workload, is_informative, is_custom, description) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        KeyHolder keyHolder = new GeneratedKeyHolder();
        log.debug("Preparing to insert category: {} with identifier: {}", categoryBean.getName(), categoryBean.getIdentifier());
        try {
            jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(query, Statement.RETURN_GENERATED_KEYS);
                ps.setString(1, categoryBean.getName());
                ps.setInt(2, categoryBean.getAccountId());
                ps.setString(3, categoryBean.getCreatedTime());
                ps.setString(4, categoryBean.getUpdatedTime());
                ps.setString(5, categoryBean.getUserDetailsId());
                ps.setString(6, categoryBean.getIdentifier());
                ps.setInt(7, categoryBean.getStatus());
                ps.setInt(8, categoryBean.getIsWorkLoad());
                ps.setInt(9, categoryBean.getIsInformative());
                ps.setInt(10, categoryBean.getIsCustom());
                ps.setString(11, categoryBean.getDescription());
                return ps;
            }, keyHolder);
            Number generatedId = keyHolder.getKey();
            log.debug("Inserted category [{}] with generated ID: {}", categoryBean.getIdentifier(), generatedId);
            if (generatedId != null) {
                return generatedId.intValue();
            } else {
                log.error("Failed to retrieve generated category ID for category [{}]", categoryBean.getIdentifier());
                throw new HealControlCenterException("Failed to retrieve generated category ID.");
            }
        } catch (Exception e) {
            log.error("Error while inserting category [{}]. Details: {}", categoryBean.getIdentifier(), e.getMessage(), e);
            throw new HealControlCenterException("Error while inserting category into the database.");
        }
    }

    /**
     * Checks if a category with the given identifier exists for the specified account.
     *
     * @param accountId  the account ID
     * @param identifier the category identifier
     * @return true if exists, false otherwise
     */
    public boolean existsByIdentifier(Integer accountId, String identifier) throws HealControlCenterException {
        String sql = "SELECT COUNT(1) FROM mst_category_details WHERE account_id in (1, ?) and identifier = ?";
        log.debug("Checking existence of category by identifier: {} for accountId: {}", identifier, accountId);
        try {
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, accountId, identifier);
            log.debug("Category existence by identifier [{}] for accountId [{}]: {}", identifier, accountId, count);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("Error while checking existence by identifier [{}] for accountId [{}]. Details: {}", identifier, accountId, e.getMessage(), e);
            throw new HealControlCenterException("Error while checking category existence by identifier.");
        }
    }

    /**
     * Checks if a category with the given name exists for the specified account.
     *
     * @param accountId the account ID
     * @param name      the category name
     * @return true if exists, false otherwise
     */
    public boolean existsByName(Integer accountId, String name) throws HealControlCenterException {
        String sql = "SELECT COUNT(1) FROM mst_category_details WHERE account_id in (1, ?) and name = ?";
        log.debug("Checking existence of category by name: {} for accountId: {}", name, accountId);
        try {
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, accountId, name);
            log.debug("Category existence by name [{}] for accountId [{}]: {}", name, accountId, count);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("Error while checking existence by name [{}] for accountId [{}]. Details: {}", name, accountId, e.getMessage(), e);
            throw new HealControlCenterException("Error while checking category existence by name.");
        }
    }

    /**
     * Updates a category in the database using JDBC Template.
     *
     * @param categoryBean CategoryDetailBean containing updated category details
     * @return number of rows affected
     * @throws HealControlCenterException if an error occurs during update
     */
    public int updateCategory(CategoryDetailBean categoryBean) throws HealControlCenterException {
        String sql = "UPDATE mst_category_details SET name = ?, updated_time = ?, status = ?, is_workload = ?, is_informative = ?, description = ? WHERE id = ?";
        try {
            int rowsAffected = jdbcTemplate.update(sql,
                    categoryBean.getName(),
                    categoryBean.getUpdatedTime(),
                    categoryBean.getStatus(),
                    categoryBean.getIsWorkLoad(),
                    categoryBean.getIsInformative(),
                    categoryBean.getDescription(),
                    categoryBean.getId());
            log.debug("Updated category [{}] with id: {}. Rows affected: {}", categoryBean.getIdentifier(), categoryBean.getId(), rowsAffected);
            return rowsAffected;
        } catch (Exception e) {
            log.error("Error while updating category [{}]. Details: {}", categoryBean.getIdentifier(), e.getMessage(), e);
            throw new HealControlCenterException("Error while updating category in the database.");
        }
    }

    /**
     * Gets a category by accountId and identifier using Spring JDBC.
     *
     * @param accountId  the account ID
     * @param identifier the category identifier
     * @return CategoryDetailBean if found, null otherwise
     */
    public CategoryDetailBean getCategoryByAccountIdAndIdentifier(int accountId, String identifier) throws HealControlCenterException {
        String sql = "SELECT id, name, description, account_id, created_time, updated_time, user_details_id, identifier, status, is_workload, is_informative, is_custom " +
                "FROM mst_category_details WHERE account_id in (1, ?) AND identifier = ?";
        try {
            return jdbcTemplate.queryForObject(sql, new Object[]{accountId, identifier}, (rs, rowNum) -> CategoryDetailBean.builder()
                    .id(rs.getInt("id"))
                    .name(rs.getString("name"))
                    .description(rs.getString("description"))
                    .accountId(rs.getInt("account_id"))
                    .createdTime(rs.getString("created_time"))
                    .updatedTime(rs.getString("updated_time"))
                    .userDetailsId(rs.getString("user_details_id"))
                    .identifier(rs.getString("identifier"))
                    .status(rs.getInt("status"))
                    .isWorkLoad(rs.getInt("is_workload"))
                    .isInformative(rs.getInt("is_informative"))
                    .isCustom(rs.getInt("is_custom"))
                    .build());
        } catch (Exception e) {
            log.error("Error while fetching category by accountId [{}] and identifier [{}]. Details: {}", accountId, identifier, e.getMessage(), e);
            throw new HealControlCenterException("Error while fetching category by accountId and identifier.");
        }
    }

    /**
     * Retrieves all categories for a given account using JDBC Template.
     *
     * @param accountId the account ID
     * @return List of CategoryDetailBean
     */
    public List<CategoryDetailBean> getCategoriesForAccount(Integer accountId) throws HealControlCenterException {
        String sql = "SELECT id, name, description, account_id, created_time, updated_time, user_details_id, identifier, status, is_workload, is_informative, is_custom " +
                "FROM mst_category_details WHERE account_id in (1, ?)";
        try {
            return jdbcTemplate.query(sql, new Object[]{accountId}, (rs, rowNum) -> CategoryDetailBean.builder()
                    .id(rs.getInt("id"))
                    .name(rs.getString("name"))
                    .description(rs.getString("description"))
                    .accountId(rs.getInt("account_id"))
                    .createdTime(rs.getString("created_time"))
                    .updatedTime(rs.getString("updated_time"))
                    .userDetailsId(rs.getString("user_details_id"))
                    .identifier(rs.getString("identifier"))
                    .status(rs.getInt("status"))
                    .isWorkLoad(rs.getInt("is_workload"))
                    .isInformative(rs.getInt("is_informative"))
                    .isCustom(rs.getInt("is_custom"))
                    .build());
        } catch (Exception e) {
            log.error("Error while fetching categories for accountId [{}]. Details: {}", accountId, e.getMessage(), e);
            throw new HealControlCenterException("Error while fetching categories for account.");
        }
    }

    /**
     * Gets the count of KPIs mapped to a category using JDBC Template.
     *
     * @param categoryId the category ID
     * @return KPI count for the category
     * @throws HealControlCenterException if any error occurs during fetch
     */
    public int getKpiCountForCategory(Integer categoryId) throws HealControlCenterException {
        log.debug("[getKpiCountForCategory] Start for categoryId: {}", categoryId);
        String sql = "SELECT COUNT(kpi_id) FROM view_kpi_category_details WHERE category_id = ? GROUP BY category_id";
        try {
            int count = jdbcTemplate.queryForObject(sql, Integer.class, categoryId);
            log.debug("[getKpiCountForCategory] KPI count for categoryId {}: {}", categoryId, count);
            return count;
        } catch (EmptyResultDataAccessException e) {
            log.info("[getKpiCountForCategory] No KPIs mapped to categoryId: {}", categoryId);
            return 0;
        } catch (Exception e) {
            log.error("[getKpiCountForCategory] Error while fetching KPI count for category [{}]. Details: {}", categoryId, e.getMessage(), e);
            throw new HealControlCenterException("Error while fetching KPI count for category.");
        }
    }

    /**
     * Retrieves KPI IDs mapped to a category.
     *
     * @param categoryId the category ID
     * @return List of KPI IDs
     */
    public List<Integer> getKpiIdsForCategory(Integer categoryId) throws HealControlCenterException {
        String sql = "SELECT kpi_id FROM view_kpi_category_details WHERE category_id = ?";
        try {
            return jdbcTemplate.query(sql, new Object[]{categoryId}, (rs, rowNum) -> rs.getInt("kpi_id"));
        } catch (Exception e) {
            log.error("Error while fetching KPI IDs for category [{}]. Details: {}", categoryId, e.getMessage(), e);
            throw new HealControlCenterException("Error while fetching KPI IDs for category.");
        }
    }

    /**
     * Updates is_informative for mapped KPIs.
     *
     * @param kpiIds      List of KPI IDs
     * @param isInfo      New is_informative value
     * @param updatedTime Updated time string
     */
    public void updateInfoForMappedKPIs(List<Integer> kpiIds, Integer isInfo, String updatedTime) throws HealControlCenterException {
        if (kpiIds == null || kpiIds.isEmpty()) return;
        String inSql = String.join(",", Collections.nCopies(kpiIds.size(), "?"));
        String sql = "UPDATE mst_kpi_details SET is_informative = ?, updated_time = ? WHERE id IN (" + inSql + ")";
        List<Object> params = new ArrayList<>();
        params.add(isInfo);
        params.add(updatedTime);
        params.addAll(kpiIds);
        try {
            jdbcTemplate.update(sql, params.toArray());
        } catch (Exception e) {
            log.error("Error while updating is_informative for KPIs {}. Details: {}", kpiIds, e.getMessage(), e);
            throw new HealControlCenterException("Error while updating is_informative for KPIs.");
        }
    }

    /**
     * Retrieves KPI count for multiple categories using JDBC Template.
     *
     * @param categoryIds List of category IDs
     * @return List of CountBean with categoryId and count
     */
    public List<CountBean> getKpiCountForCategories(List<Integer> categoryIds) throws HealControlCenterException {
        if (categoryIds == null || categoryIds.isEmpty()) return Collections.emptyList();
        String inSql = String.join(",", Collections.nCopies(categoryIds.size(), "?"));
        String sql = "select category_id id, count(kpi_id) count from view_kpi_category_details where category_id in (" + inSql + ") group by category_id";
        try {
            return jdbcTemplate.query(sql, categoryIds.toArray(), (rs, rowNum) ->
                    new CountBean(rs.getInt("id"), rs.getInt("count"))
            );
        } catch (Exception e) {
            log.error("Error while fetching KPI count for categories {}. Details: {}", categoryIds, e.getMessage(), e);
            throw new HealControlCenterException("Error while fetching KPI count for categories.");
        }
    }

    /**
     * Fetches KPI type (subType) details by sub type name from the database for KPI type (mst_type_id = 10, typeName = 'KPI').
     *
     * @param subTypeName the sub type name (e.g., specific KPI type)
     * @return ViewTypesBean if found
     * @throws HealControlCenterException if not found or any error occurs
     */
    public ViewTypesBean getKpiTypeBySubTypeName(String subTypeName) throws HealControlCenterException {
        String typeName = "KPI";
        String sql = "SELECT id as subTypeId, name as subTypeName, mst_type_id as typeId FROM mst_sub_type " +
                "WHERE mst_type_id = 10 AND LOWER(TRIM(name)) = LOWER(TRIM(?))";
        try {
            log.debug("Executing getKpiTypeBySubTypeName for subTypeName: {} with SQL: {}", subTypeName, sql);
            ViewTypesBean bean = jdbcTemplate.queryForObject(sql, new Object[]{subTypeName}, (rs, rowNum) -> ViewTypesBean.builder()
                    .subTypeId(rs.getInt("subTypeId"))
                    .subTypeName(rs.getString("subTypeName"))
                    .typeId(rs.getInt("typeId"))
                    .typeName(typeName)
                    .build());
            log.info("Found KPI type for subTypeName [{}]: {}", subTypeName, bean);
            return bean;
        } catch (Exception e) {
            log.error("KPI type not found for sub type [{}]. Exception: {}", subTypeName, e.getMessage(), e);
            throw new HealControlCenterException("KPI type not found for sub type: " + subTypeName);
        }
    }

    /**
     * Retrieves paginated and filtered categories for a given account.
     *
     * @param accountId  the account ID
     * @param searchTerm filter by category name (optional)
     * @param type       filter by type (optional, expects "CUSTOM" or "STANDARD")
     * @param subType    filter by subType (optional, expects "WORKLOAD", "INFO", "NON_INFO")
     * @param pageable   pagination and sorting
     * @return List of CategoryDetailBean
     * @throws HealControlCenterException if an error occurs during retrieval
     */
    public List<CategoryDetailBean> getCategoriesForAccountWithFilters(Integer accountId, String searchTerm, String type, String subType, Pageable pageable) throws HealControlCenterException {
        try {
            String baseQuery = "SELECT id, name, description, account_id, created_time, updated_time, user_details_id, identifier, status, is_workload, is_informative, is_custom " +
                    "FROM mst_category_details WHERE account_id in (1, ?)";
            List<Object> params = new ArrayList<>();
            params.add(accountId);

            if (searchTerm != null && !searchTerm.trim().isEmpty()) {
                baseQuery += " AND LOWER(name) LIKE ?";
                params.add("%" + searchTerm.toLowerCase().trim() + "%");
            }
            if (type != null && !type.trim().isEmpty()) {
                if (type.equalsIgnoreCase("CUSTOM")) {
                    baseQuery += " AND is_custom = 1";
                } else if (type.equalsIgnoreCase("STANDARD")) {
                    baseQuery += " AND is_custom = 0";
                }
            }
            if (subType != null && !subType.trim().isEmpty()) {
                if (subType.equalsIgnoreCase("WORKLOAD")) {
                    baseQuery += " AND is_workload = 1";
                } else if (subType.equalsIgnoreCase("INFO")) {
                    baseQuery += " AND is_informative = 1";
                } else if (subType.equalsIgnoreCase("NON_INFO")) {
                    baseQuery += " AND is_workload = 0 AND is_informative = 0";
                }
            }

            String paginatedQuery = com.heal.controlcenter.util.PaginationUtils.applyPagination(baseQuery, pageable);
            params = com.heal.controlcenter.util.PaginationUtils.buildPaginationParams(params, pageable);

            return jdbcTemplate.query(paginatedQuery, params.toArray(), (rs, rowNum) -> CategoryDetailBean.builder()
                    .id(rs.getInt("id"))
                    .name(rs.getString("name"))
                    .description(rs.getString("description"))
                    .accountId(rs.getInt("account_id"))
                    .createdTime(rs.getString("created_time"))
                    .updatedTime(rs.getString("updated_time"))
                    .userDetailsId(rs.getString("user_details_id"))
                    .identifier(rs.getString("identifier"))
                    .status(rs.getInt("status"))
                    .isWorkLoad(rs.getInt("is_workload"))
                    .isInformative(rs.getInt("is_informative"))
                    .isCustom(rs.getInt("is_custom"))
                    .build());
        } catch (Exception e) {
            log.error("Error while fetching paginated/filtered categories for accountId [{}]. Details: {}", accountId, e.getMessage(), e);
            throw new HealControlCenterException("Error while fetching paginated/filtered categories for account.");
        }
    }

    /**
     * Counts total filtered categories for pagination.
     *
     * @param accountId  the account ID
     * @param searchTerm filter by category name (optional)
     * @param type       filter by type (optional, expects "CUSTOM" or "STANDARD")
     * @param subType    filter by subType (optional, expects "WORKLOAD", "INFO", "NON_INFO")
     * @return total count of filtered categories
     * @throws HealControlCenterException if an error occurs during count
     */
    public int countCategoriesForAccountWithFilters(Integer accountId, String searchTerm, String type, String subType) throws HealControlCenterException {
        try {
            String countQuery = "SELECT COUNT(*) FROM mst_category_details WHERE account_id in (1, ?)";
            List<Object> params = new ArrayList<>();
            params.add(accountId);

            if (searchTerm != null && !searchTerm.trim().isEmpty()) {
                countQuery += " AND LOWER(name) LIKE ?";
                params.add("%" + searchTerm.toLowerCase().trim() + "%");
            }
            if (type != null && !type.trim().isEmpty()) {
                if (type.equalsIgnoreCase("CUSTOM")) {
                    countQuery += " AND is_custom = 1";
                } else if (type.equalsIgnoreCase("STANDARD")) {
                    countQuery += " AND is_custom = 0";
                }
            }
            if (subType != null && !subType.trim().isEmpty()) {
                if (subType.equalsIgnoreCase("WORKLOAD")) {
                    countQuery += " AND is_workload = 1";
                } else if (subType.equalsIgnoreCase("INFO")) {
                    countQuery += " AND is_informative = 1";
                } else if (subType.equalsIgnoreCase("NON_INFO")) {
                    countQuery += " AND is_workload = 0 AND is_informative = 0";
                }
            }
            Integer count = jdbcTemplate.queryForObject(countQuery, Integer.class, params.toArray());
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("Error while counting paginated/filtered categories for accountId [{}]. Details: {}", accountId, e.getMessage(), e);
            throw new HealControlCenterException("Error while counting paginated/filtered categories for account.");
        }
    }

    /**
     * Hard deletes a category by its ID.
     *
     * @param categoryId the category ID
     * @return number of rows affected
     * @throws HealControlCenterException if an error occurs during deletion
     */
    public int deleteCategoryById(Integer categoryId) throws HealControlCenterException {
        log.debug("[deleteCategoryById] Start for categoryId: {}", categoryId);
        String query = "DELETE FROM mst_category_details WHERE id = ?";
        try {
            int rows = jdbcTemplate.update(query, categoryId);
            log.debug("[deleteCategoryById] Rows affected: {} for categoryId: {}", rows, categoryId);
            return rows;
        } catch (Exception e) {
            log.error("[deleteCategoryById] Error while hard deleting category with id [{}]. Details: {}", categoryId, e.getMessage(), e);
            throw new HealControlCenterException("Error while hard deleting category with id.");
        }
    }

    /**
     * Soft deletes a category by setting its status to 0.
     *
     * @param categoryId the category ID
     * @return number of rows affected
     * @throws HealControlCenterException if an error occurs during update
     */
    public int softDeleteCategoryById(Integer categoryId) throws HealControlCenterException {
        log.debug("[softDeleteCategoryById] Start for categoryId: {}", categoryId);
        String query = "UPDATE mst_category_details SET status = 0 WHERE id = ?";
        try {
            int rows = jdbcTemplate.update(query, categoryId);
            log.debug("[softDeleteCategoryById] Rows affected: {} for categoryId: {}", rows, categoryId);
            return rows;
        } catch (Exception e) {
            log.error("[softDeleteCategoryById] Error while soft deleting category with id [{}]. Details: {}", categoryId, e.getMessage(), e);
            throw new HealControlCenterException("Error while soft deleting category with id.");
        }
    }

    /**
     * Fetches a list of category IDs for the given identifiers and account ID.
     *
     * @param categoryIdentifiers List of category identifiers
     * @param accountId           Account ID
     * @return List of category IDs
     * @throws HealControlCenterException if any error occurs during fetch
     */
    public List<Integer> getCategoryIdsByIdentifiers(List<String> categoryIdentifiers, int accountId) throws HealControlCenterException {
        log.debug("[getCategoryIdsByIdentifiers] Start for accountId: {}, categoryIdentifiers: {}", accountId, categoryIdentifiers);
        if (categoryIdentifiers == null || categoryIdentifiers.isEmpty()) {
            log.warn("[getCategoryIdsByIdentifiers] categoryIdentifiers is null or empty for accountId: {}", accountId);
            return Collections.emptyList();
        }
        String inClause = String.join(",", Collections.nCopies(categoryIdentifiers.size(), "?"));
        String sql = "SELECT id FROM mst_category_details WHERE identifier IN (" + inClause + ") AND account_id IN (1, ?) AND status = 1";
        List<Object> params = new ArrayList<>(categoryIdentifiers);
        params.add(accountId);
        log.debug("[getCategoryIdsByIdentifiers] Executing SQL: {} with params: {}", sql, params);
        try {
            List<Integer> ids = jdbcTemplate.query(sql, params.toArray(), (rs, rowNum) -> rs.getInt("id"));
            log.debug("[getCategoryIdsByIdentifiers] Fetched category IDs: {} for accountId: {}", ids, accountId);
            return ids;
        } catch (Exception e) {
            log.error("[getCategoryIdsByIdentifiers] Error while fetching category IDs by identifiers [{}] for accountId [{}]. Details: {}", categoryIdentifiers, accountId, e.getMessage(), e);
            throw new HealControlCenterException("Error while fetching category IDs by identifiers.");
        }
    }
}
