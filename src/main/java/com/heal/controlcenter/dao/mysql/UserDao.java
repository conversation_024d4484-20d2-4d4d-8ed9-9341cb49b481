package com.heal.controlcenter.dao.mysql;

import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.util.DateTimeUtil;
import com.heal.controlcenter.util.HealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Repository
public class UserDao {

    private final JdbcTemplate jdbcTemplate;
    private final HealthMetrics healthMetrics;

    public UserDao(JdbcTemplate jdbcTemplate, HealthMetrics healthMetrics) {
        this.jdbcTemplate = jdbcTemplate;
        this.healthMetrics = healthMetrics;
    }

    /**
     * Updates the last login time for a user in the database.
     *
     * @param userIdentifier the unique identifier of the user
     * @param lastLoginTime  the last login time to be set (as a String)
     * @return the number of rows affected, or -1 if an error occurs
     */
    public int updateUserLoginTime(String userIdentifier, String lastLoginTime) {
        String query = "UPDATE user_attributes SET last_login_time = ? WHERE user_identifier = ?";

        try {
            return jdbcTemplate.update(query, lastLoginTime, userIdentifier);
        } catch (Exception e) {
            healthMetrics.updateHealApiServiceErrors();
            log.error("Exception occurred when updating last login time for user [{}]. Details: ", userIdentifier, e);
            return -1;
        }
    }

    public List<ViewApplicationServiceMappingBean> getUserAccessibleApplicationsServices(String userId, String accountIdentifier) throws HealControlCenterException {
        String query = "SELECT vasm.application_id applicationId, vasm.application_name applicationName, " +
                "vasm.application_identifier applicationIdentifier, vasm.service_id serviceId, " +
                "vasm.service_name serviceName, vasm.service_identifier serviceIdentifier, " +
                "vasm.account_id accountId FROM view_application_service_mapping vasm " +
                "JOIN user_attributes ua ON ua.user_identifier = ? " +
                "WHERE vasm.account_id = (SELECT id FROM account WHERE identifier = ?)";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ViewApplicationServiceMappingBean.class), userId, accountIdentifier);
        } catch (Exception ex) {
            log.error("Error in getting user accessible applications services for user [{}] and account [{}]", userId, accountIdentifier, ex);
            throw new HealControlCenterException("Error in getting user accessible applications services");
        }
    }

    public List<ViewApplicationServiceMappingBean> getApplicationsForServiceGroup(int serviceGroupId) throws HealControlCenterException {
        String query = "SELECT DISTINCT vasm.application_id AS applicationId, vasm.application_name AS applicationName, " +
                "vasm.application_identifier AS applicationIdentifier " +
                "FROM service_group_mapping sgm " +
                "JOIN view_application_service_mapping vasm ON sgm.service_id = vasm.service_id " +
                "WHERE sgm.service_group_id = ?";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ViewApplicationServiceMappingBean.class), serviceGroupId);
        } catch (Exception e) {
            log.error("Error in getting applications for service group [{}]", serviceGroupId, e);
            throw new HealControlCenterException("Error in getting applications for service group");
        }
    }

    /**
     * Retrieves a list of applications associated with a list of service groups.
     *
     * @param serviceGroupIds A list of service group IDs.
     * @return A list of {@link ViewApplicationServiceMappingBean} objects, each representing an application.
     * @throws HealControlCenterException If an error occurs while fetching the data.
     */
    public List<ViewApplicationServiceMappingBean> getApplicationsForServiceGroups(List<Integer> serviceGroupIds) throws HealControlCenterException {
        String query = "SELECT DISTINCT vasm.application_id AS applicationId, vasm.application_name AS applicationName, " +
                "vasm.application_identifier AS applicationIdentifier, sgm.service_group_id AS serviceGroupId " +
                "FROM service_group_mapping sgm " +
                "JOIN view_application_service_mapping vasm ON sgm.service_id = vasm.service_id " +
                "WHERE sgm.service_group_id IN (" + String.join(",", Collections.nCopies(serviceGroupIds.size(), "?")) + ")";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ViewApplicationServiceMappingBean.class), serviceGroupIds.toArray());
        } catch (Exception e) {
            log.error("Error in getting applications for service groups", e);
            throw new HealControlCenterException("Error in getting applications for service groups");
        }
    }

    public UserAccessBean fetchUserAccessDetailsUsingIdentifier(String userIdentifier) {
        try {
            String query = "select a.access_details, a.user_identifier from user_access_details a where user_identifier=?";
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(UserAccessBean.class),
                    userIdentifier);
        } catch (DataAccessException e) {
            log.error("Error while fetching user access information for user [{}]. Details: ", userIdentifier, e);
        }

        return null;
    }

    public boolean adEditStatusKeycloak() throws HealControlCenterException {
        String query = "SELECT count(*) from appsonekeycloak.COMPONENT_CONFIG where NAME='editMode' and VALUE='UNSYNCED'";
        try {
            log.debug("getting Ad Edit Status");
            return jdbcTemplate.queryForObject(query, Boolean.class);
        } catch (Exception ex) {
            log.error("Error in Ad Edit Status");
            throw new HealControlCenterException("Error in Ad Edit Status");
        }
    }

    public UserInfoBean getUserDetails(String userId) throws HealControlCenterException {
        String query = "SELECT u.id mysqlId, u.user_identifier id, u.username userName, u.mst_access_profile_id profileId, " +
                "u.mst_role_id roleId, u.status, a.access_details accessDetailsJSON, email_address emailId, contact_number contactNumber, " +
                "u.is_timezone_mychoice isTimezoneMychoice, u.is_notifications_timezone_mychoice isNotificationsTimezoneMychoice " +
                "FROM user_attributes u, user_access_details a " +
                "where u.user_identifier = a.user_identifier and u.user_identifier = ? ";
        try {
            log.debug("getting user details");
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(UserInfoBean.class), userId);
        } catch (Exception ex) {
            log.error("Error in getting user details for user identifier [{}]. Details: ", userId, ex);
            throw new HealControlCenterException("Error in getting user details");
        }
    }

    public List<UserDetailsBean> getUsers() throws HealControlCenterException {
        String query = "SELECT u.user_identifier id, u.username userName, u.user_details_id updatedBy, a.name userProfile, " +
                "r.name role, u.status, u.updated_time updatedOn FROM user_attributes u, mst_roles r, mst_access_profiles a " +
                "where u.mst_access_profile_id = a.id and u.mst_role_id = r.id and a.mst_role_id = r.id";
        try {
            log.debug("getting users");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(UserDetailsBean.class));
        } catch (Exception ex) {
            log.error("Error in getting users", ex);
            throw new HealControlCenterException("Error in getting users");
        }
    }

    public UserBean getUserDetailsFromUsername(String userName) throws HealControlCenterException {
        String query = "select first_name firstName, last_name lastName, email, id, username, enabled from appsonekeycloak.USER_ENTITY where username = ?";
        try {
            log.debug("getting user details from username.");
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(UserBean.class), userName);
        } catch (Exception ex) {
            log.error("Error in getting user details from username.");
            throw new HealControlCenterException("Error in getting user details from username.");
        }
    }

    public UserProfileBean getUserProfile(int profileId) throws HealControlCenterException {
        String query = "select map.id userProfileId, map.name userProfileName, mr.name role " +
                "from mst_access_profiles map, mst_roles mr where mr.id=map.mst_role_id and map.id=?";
        try {
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(UserProfileBean.class), profileId);
        } catch (Exception ex) {
            log.error("Error in getting user profiles.");
            throw new HealControlCenterException("Error in getting user profiles.");
        }
    }

    public UserAccessBean getUserAccessDetails(String userId) throws HealControlCenterException {
        String query = "select access_details accessDetailsJson, user_identifier userId from user_access_details where user_identifier= ?";
        try {
            log.debug("getting user access details");
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(UserAccessBean.class), userId);
        } catch (Exception ex) {
            log.error("Error in getting user access details");
            throw new HealControlCenterException("Error in getting user access details");
        }
    }

    public String getUsernameFromIdentifier(String userId) throws HealControlCenterException {
        String query = "select username from user_attributes where user_identifier= ?";
        try {
            return jdbcTemplate.queryForObject(query, String.class, userId);
        } catch (Exception ex) {
            log.error("Error in fetching username for userId [{}]. Details: ", userId, ex);
            throw new HealControlCenterException("Error in fetching username");
        }
    }

    public List<String> getUserIdentifiers() throws HealControlCenterException {
        try {
            String USER_IDENTIFIER_QUERY = "select ua.user_identifier from user_attributes ua,user_access_details uad " +
                    "where uad.user_identifier=ua.user_identifier";
            return jdbcTemplate.queryForList(USER_IDENTIFIER_QUERY, String.class);
        } catch (Exception e) {
            log.error("Error while getting user attribute details from DB. Details: ", e);
            throw new HealControlCenterException("Error in fetching user attribute details");
        }
    }

    public List<String> getKeycloakUserIdentifiers() throws HealControlCenterException {
        try {
            String KEYCLOAK_USER_IDENTIFIER_QUERY = "select id from appsonekeycloak.USER_ENTITY";
            return jdbcTemplate.queryForList(KEYCLOAK_USER_IDENTIFIER_QUERY, String.class);
        } catch (Exception e) {
            log.error("Error while getting keycloak user identifiers from DB. Details: ", e);
            throw new HealControlCenterException("Error in fetching keycloak user identifiers.");
        }
    }

    public List<UserBean> getUserDetailsFromKeycloak() throws HealControlCenterException {
        try {
            String USER_DETAILS_FROM_KEYCLOAK_QUERY = "select first_name firstName, last_name lastName, email, id, username, enabled " +
                    "from appsonekeycloak.USER_ENTITY";
            return jdbcTemplate.query(USER_DETAILS_FROM_KEYCLOAK_QUERY, new BeanPropertyRowMapper<>(UserBean.class));
        } catch (Exception e) {
            log.error("Error while getting user identifier. Details: ", e);
            throw new HealControlCenterException("Error while getting user identifier");
        }
    }

    public UserAttributesBean getRoleProfileInfoForUserId(String userIdentifier) throws HealControlCenterException {
        String ROLE_PROFILE_FOR_USER = "select mr.name roleName, mr.id roleId, map.id accessProfileId, map.name accessProfileName " +
                "from mst_roles mr join mst_access_profiles map join user_attributes ua on mr.id=ua.mst_role_id " +
                "and mr.id=map.mst_role_id and map.id=mst_access_profile_id where ua.user_identifier='" + userIdentifier + "'";
        try {
            return jdbcTemplate.queryForObject(ROLE_PROFILE_FOR_USER, new BeanPropertyRowMapper<>(UserAttributesBean.class));
        } catch (Exception e) {
            log.error("Exception encountered while fetching user role and profile. Details: ", e);
            throw new HealControlCenterException("Exception encountered while fetching user role and profile");
        }
    }

    public List<String> getUserAccessibleActions(int accessProfileId) throws HealControlCenterException {
        String USER_ACCESSIBLE_ACTION = "select vrd.action_identifier from mst_access_profile_mapping mapm join " +
                "view_route_details vrd on mst_big_feature_id=vrd.big_feature_id where " +
                "dashboard_name='ControlCenter' and mst_access_profile_id= ?";
        try {
            return jdbcTemplate.queryForList(USER_ACCESSIBLE_ACTION, String.class, accessProfileId);
        } catch (Exception e) {
            log.error("Exception encountered while fetching user attributes information. Details: ", e);
            throw new HealControlCenterException("Exception encountered while fetching user attributes information");
        }
    }

    public String getHealSetup() {
        String defaultSetupType = "Keycloak";
        String setup = defaultSetupType;
        try {
            String query = "SELECT value FROM a1_installation_attributes where name = 'SetupType'";
            setup = jdbcTemplate.queryForObject(query, String.class);
            if (setup == null) {
                return defaultSetupType;
            }
        } catch (Exception e) {
            log.error("Exception while getting setup type: ", e);
        }
        return setup;
    }

    public void deleteUserAttributesAndAccessDetails(String userId) throws HealControlCenterException {
        try {
            String deleteUserFromUseridQuery = "delete from  user_attributes where user_identifier = ?";
            jdbcTemplate.update(deleteUserFromUseridQuery, userId);

            String deleteUserFromAccessDetailsQuery = "delete from user_access_details where user_identifier = ?";
            jdbcTemplate.update(deleteUserFromAccessDetailsQuery, userId);
        } catch (Exception e) {
            String ERROR_DELETE_USER_ATTRIBUTES = "Error in deleting user_attributes or user_access_details";
            log.error("{}. Reason: ", ERROR_DELETE_USER_ATTRIBUTES, e);
            throw new HealControlCenterException(ERROR_DELETE_USER_ATTRIBUTES);
        }
    }

    public List<UserInfoBean> getActiveUsers() throws HealControlCenterException {
        List<UserInfoBean> activeUsersList;
        try {
            String GET_ACTIVE_USERS_QUERY = "SELECT  u.user_identifier id, u.created_time createdTime, u.last_login_time lastLoginTime," + "\n" +
                    "  u.is_timezone_mychoice isTimezoneMychoice, u.is_notifications_timezone_mychoice isNotificationsTimezoneMychoice," + "\n" +
                    "  un.FIRST_NAME firstName, un.LAST_NAME lastName, u.username userName, u.email_address emailId" + "\n" +
                    "  FROM user_attributes u, appsonekeycloak.USER_ENTITY un  " + "\n" +
                    "  where u.status = 1 and un.id= u.user_identifier and u.mst_access_profile_id != 1";
            activeUsersList = jdbcTemplate.query(GET_ACTIVE_USERS_QUERY, new BeanPropertyRowMapper<>(UserInfoBean.class));
        } catch (Exception e) {
            log.error("Error while getting active_users. Reason: ", e);
            throw new HealControlCenterException("Error while getting active_users");
        }
        return activeUsersList;
    }

    public UserInfoBean getSuperAdmin() {
        UserInfoBean superAdmin = null;
        try {
            String GET_SUPER_ADMIN_QUERY = "SELECT  u.user_identifier id, u.created_time createdTime, u.last_login_time lastLoginTime, " +
                    "u.is_timezone_mychoice isTimezoneMychoice, u.is_notifications_timezone_mychoice isNotificationsTimezoneMychoice, " +
                    "un.FIRST_NAME firstName, un.LAST_NAME lastName, u.username userName, u.email_address emailId " +
                    "FROM user_attributes u, appsonekeycloak.USER_ENTITY un " +
                    "where u.status = 1 and un.id= u.user_identifier and u.mst_access_profile_id = 1";
            superAdmin = jdbcTemplate.queryForObject(GET_SUPER_ADMIN_QUERY, new BeanPropertyRowMapper<>(UserInfoBean.class));
        } catch (Exception e) {
            String ERROR_GETTING_SUPER_ADMIN = "Error while getting super_admin";
            log.error("{}.Reason: ", ERROR_GETTING_SUPER_ADMIN, e);
        }
        return superAdmin;
    }

    public void updateUserStatusToInactive(String userIdentifier, String superUserIdentifier) {
        String UPDATE_USER_STATUS_INACTIVE = "UPDATE user_attributes SET u.status = 0, u.user_details_id = " + superUserIdentifier +
                "WHERE u.user_identifier = " + userIdentifier;
        jdbcTemplate.execute(UPDATE_USER_STATUS_INACTIVE);
    }

    public List<UserDetailsBean> getNonSuperUsers() throws HealControlCenterException {
        String query = "SELECT u.user_identifier userId, u.username userName, u.user_details_id updatedBy, a.name userProfile, " +
                "r.name role, u.status, u.updated_time updatedOn FROM user_attributes u, mst_roles r, mst_access_profiles a " +
                "where u.mst_access_profile_id = a.id and u.mst_role_id = r.id and a.mst_role_id = r.id and r.name != 'Super Admin'";
        try {
            return jdbcTemplate.query(query, (rs, rowNum) -> {
                UserDetailsBean userDetailsBean = new UserDetailsBean();
                userDetailsBean.setUserId(rs.getString("userId"));
                userDetailsBean.setUpdatedBy(rs.getString("updatedBy"));
                userDetailsBean.setUserName(rs.getString("userName"));
                userDetailsBean.setUpdatedOn(DateTimeUtil.getGMTToEpochTime(rs.getString("updatedOn")));
                userDetailsBean.setStatus(rs.getInt("status"));
                userDetailsBean.setUserProfile(rs.getString("userProfile"));
                userDetailsBean.setRole(rs.getString("role"));
                return userDetailsBean;
            });
        } catch (Exception e) {
            log.error("Exception encountered while fetching users. Reason: ", e);
            throw new HealControlCenterException("Exception encountered while fetching users");
        }
    }

    public List<IdBean> getRoles() throws HealControlCenterException {
        String query = "select id, name from mst_roles where ui_visible = 1";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(IdBean.class));
        } catch (Exception ex) {
            log.info("Error while getting user roles details from DB", ex);
            throw new HealControlCenterException("Error in fetching user roles details");
        }
    }

    public List<UserProfileBean> getUserProfiles() throws HealControlCenterException {
        String query = "select map.id userProfileId, map.name userProfileName, mr.name role from mst_access_profiles map," +
                "mst_roles mr where mr.id=map.mst_role_id";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(UserProfileBean.class));
        } catch (Exception ex) {
            log.info("Error in fetching user profile details from DB", ex);
            throw new HealControlCenterException("Error in fetching user profile details");
        }
    }

    public List<String> getAccessProfileMapping(int profileId) throws HealControlCenterException {
        String query = "select mb.name from mst_access_profile_mapping ma, mst_big_features mb " +
                "where ma.mst_big_feature_id=mb.id and mb.ui_visible=1 and ma.mst_access_profile_id=?";
        try {
            return jdbcTemplate.queryForList(query, String.class, profileId);
        } catch (Exception ex) {
            log.info("Error in fetching user profiles mapping details from DB", ex);
            throw new HealControlCenterException("Error in fetching user profiles mapping");
        }
    }

    public List<IdBean> getRolesById(Long roleId) throws HealControlCenterException {
        String query = "select id, name from mst_roles where ui_visible = 1 and id = ?";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(IdBean.class), roleId);
        } catch (Exception ex) {
            log.info("Error while getting user roles details from DB", ex);
            throw new HealControlCenterException("Error in fetching user roles details");
        }
    }

    /**
     * Retrieves a map of user identifiers to their corresponding usernames from the `user_attributes` table.
     *
     * @param userIdentifiers List of user identifiers
     * @return Map of user identifiers to usernames
     * @throws HealControlCenterException If any database error occurs during fetch
     */
    public Map<String, String> getUsernamesFromIdentifiers(List<String> userIdentifiers) throws HealControlCenterException {
        if (userIdentifiers == null || userIdentifiers.isEmpty()) {
            return Collections.emptyMap();
        }
        String query = "select user_identifier, username from user_attributes where user_identifier IN (" +
                String.join(",", Collections.nCopies(userIdentifiers.size(), "?")) + ")";
        try {
            return jdbcTemplate.query(query, ps -> {
                for (int i = 0; i < userIdentifiers.size(); i++) {
                    ps.setString(i + 1, userIdentifiers.get(i));
                }
            }, rs -> {
                Map<String, String> results = new HashMap<>();
                while (rs.next()) {
                    results.put(rs.getString("user_identifier"), rs.getString("username"));
                }
                return results;
            });
        } catch (Exception ex) {
            log.error("Error in fetching usernames for userIds. Details: ", ex);
            throw new HealControlCenterException("Error in fetching usernames");
        }
    }
}
