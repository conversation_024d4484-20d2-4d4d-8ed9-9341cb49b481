package com.heal.controlcenter.dao.mysql;

import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.pojo.ApplicationAnomalyConfiguration;
import com.heal.controlcenter.pojo.ClusterComponentDetails;
import com.heal.controlcenter.util.PaginationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Repository
@Slf4j
public class ControllerDao {

    private final JdbcTemplate jdbcTemplate;

    public ControllerDao(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public List<ControllerBean> getServicesList(Integer accountId) throws HealControlCenterException {
        String query = "select id, name, identifier, account_id accountId, user_details_id lastModifiedBy, created_time createdTime, " +
                "updated_time updatedTime, controller_type_id controllerTypeId, status from controller " +
                "where account_id = ? and controller_type_id = 192";
        try {
            log.debug("getting services list.");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ControllerBean.class), accountId);
        } catch (EmptyResultDataAccessException e) {
            log.info("Services not mapped to account [{}]", accountId);
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("Exception encountered while fetching services for accountId [{}]. Details: ", accountId, e);
            throw new HealControlCenterException("Error occurred while getting services list");
        }
    }

    public List<ControllerBean> getServicesList(int accountId, String searchTerm, List<Integer> serviceIds, Pageable pageable) throws HealControlCenterException {
        String baseQuery = "SELECT c.id, c.name, c.identifier, c.account_id AS accountId, c.user_details_id AS lastModifiedBy, " +
                "c.created_time AS createdTime, c.updated_time AS updatedTime, c.controller_type_id AS controllerTypeId, c.status " +
                "FROM controller c WHERE c.account_id = ? AND c.controller_type_id = 192 AND c.id IN (%s)";

        List<Object> params = new ArrayList<>();
        params.add(accountId);

        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            baseQuery += " AND (LOWER(c.name) LIKE ? OR LOWER(c.identifier) LIKE ?)";
            String likeTerm = "%" + searchTerm.toLowerCase().trim() + "%";
            params.add(likeTerm);
            params.add(likeTerm);
        }

        String serviceIdParams = String.join(",", Collections.nCopies(serviceIds.size(), "?"));
        String sql = String.format(baseQuery, serviceIdParams);
        params.addAll(serviceIds);

        sql = PaginationUtils.applyPagination(sql, pageable);
        params = PaginationUtils.buildPaginationParams(params, pageable);

        try {
            return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(ControllerBean.class), params.toArray());
        } catch (Exception e) {
            log.error("Error while fetching services for accountId: {}. Details: ", accountId, e);
            throw new HealControlCenterException("Error fetching services list");
        }
    }

    public int getServicesListCount(int accountId, String searchTerm, List<Integer> serviceIds) throws HealControlCenterException {
        String sql = "SELECT COUNT(*) FROM controller c WHERE c.account_id = ? AND c.controller_type_id = 192 AND c.id IN (%s)";

        List<Object> params = new ArrayList<>();
        params.add(accountId);

        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            sql += " AND (LOWER(c.name) LIKE ? OR LOWER(c.identifier) LIKE ?)";
            String likeTerm = "%" + searchTerm.toLowerCase().trim() + "%";
            params.add(likeTerm);
            params.add(likeTerm);
        }

        String serviceIdParams = String.join(",", Collections.nCopies(serviceIds.size(), "?"));
        sql = String.format(sql, serviceIdParams);
        params.addAll(serviceIds);

        try {
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, params.toArray());
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("Error while counting services for accountId: {}. Details: ", accountId, e);
            throw new HealControlCenterException("Error counting services");
        }
    }

    public List<ControllerBean> getControllerList(int accountId) throws HealControlCenterException {
        String query = "select id id, name name, controller_type_id controllerTypeId, identifier identifier, plugin_supr_interval pluginSuppressionInterval, plugin_whitelist_status pluginWhitelisted, " +
                "status status, user_details_id lastModifiedBy, created_time createdTime, updated_time updatedTime, account_id accountId " +
                "from controller where account_id = ? and status = 1";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ControllerBean.class), accountId);
        } catch (Exception ex) {
            log.error("Error in fetching controllers", ex);
            throw new HealControlCenterException("Error in fetching controllers");
        }
    }

    public ControllerBean getServiceById(int serviceId, int accountId) throws HealControlCenterException {
        String query = "SELECT id, name name, controller_type_id controllerTypeId, identifier identifier, plugin_supr_interval pluginSuppressionInterval, " +
                "plugin_whitelist_status pluginWhitelisted, status status, user_details_id lastModifiedBy, created_time createdTime, updated_time updatedTime, " +
                "account_id accountId FROM controller WHERE account_id = ? AND id = ? AND status = 1 and controller_type_id=192";
        try {
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(ControllerBean.class), accountId, serviceId);
        } catch (Exception ex) {
            log.error("Error occurred while fetching controller. Details: ", ex);
            throw new HealControlCenterException("Error occurred while fetching controller.");
        }
    }

    /**
     * Retrieves a ControllerBean by its ID.
     *
     * @param controllerId The ID of the controller to retrieve.
     * @return The ControllerBean object if found, null otherwise.
     */
    public ControllerBean getControllerById(int controllerId) throws HealControlCenterException {
        String query = "SELECT id, name, identifier, account_id as accountId, user_details_id as userDetailsId, " +
                "created_time as createdTime, updated_time as updatedTime, controller_type_id as controllerTypeId, status " +
                "FROM controller WHERE id = ?";
        try {
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(ControllerBean.class), controllerId);
        } catch (EmptyResultDataAccessException e) {
            log.info("No controller found with ID [{}]", controllerId);
            return null;
        } catch (Exception ex) {
            log.error("Error occurred while fetching controller by ID [{}]. Details: ", controllerId, ex);
            throw new HealControlCenterException("Error occurred while fetching controller by ID.");
        }
    }

    public List<String> getLayers(String servicesLayerType) throws HealControlCenterException {
        String query = "SELECT distinct mst.name FROM mst_type mt JOIN mst_sub_type mst on(mt.id = mst_type_id) WHERE mt.type = ?";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(String.class), servicesLayerType);
        } catch (Exception e) {
            log.error("Error while fetching layers for the service [{}]. Details: ", servicesLayerType, e);
            throw new HealControlCenterException("Error while fetching layers for the service.");
        }
    }

    /**
     * Retrieves a list of active applications for a specific account.
     *
     * @param accountId the account ID for which applications are to be fetched
     * @return list of {@link ControllerBean} objects representing the applications
     */
    public List<ControllerBean> getApplicationsList(int accountId) throws HealControlCenterException {
        String query = "select id, name, controller_type_id controllerTypeId, identifier, " +
                "status, user_details_id userDetailsId, created_time createdTime, updated_time updatedTime ,account_id accountId, environment " +
                "from controller where account_id = ? and status = 1 and controller_type_id = 191";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ControllerBean.class), accountId);
        } catch (EmptyResultDataAccessException e) {
            log.info("No applications mapped to ac  countId [{}]", accountId);
            return Collections.emptyList();
        } catch (Exception ex) {
            log.error("Error in fetching applications for accountId [{}]", accountId, ex);
            throw new HealControlCenterException("Error in fetching applications.");
        }
    }

    /**
     * Retrieves a list of applications with their identifiers for a specific account.
     *
     * @param accountId              the account ID for which applications are to be fetched
     * @param applicationIdentifiers list of application identifiers to filter the results
     * @return list of {@link ControllerBean} objects representing the applications
     * @throws HealControlCenterException if an error occurs while querying the database
     */
    public List<ControllerBean> getApplicationsListWithIdentifiers(int accountId, List<String> applicationIdentifiers) throws HealControlCenterException {
        if (applicationIdentifiers == null || applicationIdentifiers.isEmpty()) {
            return Collections.emptyList();
        }

        String query = "SELECT id, name, controller_type_id controllerTypeId, identifier, status, " +
                "user_details_id lastModifiedBy, created_time createdTime, updated_time updatedTime, " +
                "account_id accountId, environment " +
                "FROM controller WHERE account_id = ? AND status = 1 AND controller_type_id = 191";

        List<Object> params = new ArrayList<>();
        params.add(accountId);

        // Add IN clause for identifiers
        query += " AND identifier IN (" + String.join(",", Collections.nCopies(applicationIdentifiers.size(), "?")) + ")";
        params.addAll(applicationIdentifiers);

        query += " ORDER BY updated_time DESC";

        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ControllerBean.class), params.toArray());
        } catch (Exception e) {
            log.error("Error fetching applications with identifiers from DB", e);
            throw new HealControlCenterException("Error fetching applications with identifiers from DB");
        }
    }

    /**
     * Retrieves a paginated list of active applications for a specific account,
     * optionally filtered by a search term.
     *
     * @param accountId  the account ID
     * @param searchTerm optional filter for application name or identifier
     * @param pageable   pagination information (page number, size, sort)
     * @return list of {@link ControllerBean} matching the criteria
     */
    public List<ControllerBean> getApplicationsList(int accountId, String searchTerm, Pageable pageable) throws HealControlCenterException {
        long startTime = System.currentTimeMillis();

        String baseQuery = "SELECT id, name, controller_type_id AS controllerTypeId, identifier, status, " +
                "user_details_id AS lastModifiedBy, created_time AS createdTime, updated_time AS updatedTime, " +
                "account_id AS accountId, environment " +
                "FROM controller WHERE account_id = ? AND status = 1 AND controller_type_id = 191";

        List<Object> params = new ArrayList<>();
        params.add(accountId);

        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            baseQuery += " AND (LOWER(name) LIKE ? OR LOWER(identifier) LIKE ?)";
            String likeTerm = "%" + searchTerm.toLowerCase().trim() + "%";
            params.add(likeTerm);
            params.add(likeTerm);
        }

        String paginatedQuery = PaginationUtils.applyPagination(baseQuery, pageable);
        params = PaginationUtils.buildPaginationParams(params, pageable);

        try {
            log.debug("Fetching paginated application list for accountId={}, searchTerm={}", accountId, searchTerm);
            return jdbcTemplate.query(paginatedQuery, new BeanPropertyRowMapper<>(ControllerBean.class), params.toArray());
        } catch (Exception e) {
            log.error("Error fetching applications (accountId: {}, searchTerm: {}, pageable: {})", accountId, searchTerm, pageable, e);
            throw new HealControlCenterException("Error fetching applications.");
        } finally {
            long endTime = System.currentTimeMillis();
            log.info("getApplicationsList(accountId: {}, searchTerm: {}, pageable: {}) completed in {} ms",
                    accountId, searchTerm, pageable, (endTime - startTime));
        }
    }

    /**
     * Counts the number of active applications for a specific account,
     * optionally filtered by a search term.
     *
     * @param accountId  the account ID
     * @param searchTerm optional filter for application name or identifier
     * @return the total number of matching applications
     */
    public int countApplications(int accountId, String searchTerm) throws HealControlCenterException {

        String countQuery = "SELECT COUNT(*) FROM controller WHERE account_id = ? AND status = 1 AND controller_type_id = 191";
        List<Object> params = new ArrayList<>();
        params.add(accountId);

        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            countQuery += " AND (LOWER(name) LIKE ? OR LOWER(identifier) LIKE ?)";
            String likeTerm = "%" + searchTerm.toLowerCase().trim() + "%";
            params.add(likeTerm);
            params.add(likeTerm);
        }

        try {
            log.debug("Counting applications for accountId={}, searchTerm={}", accountId, searchTerm);
            Integer count = jdbcTemplate.queryForObject(countQuery, Integer.class, params.toArray());
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("Error counting applications (accountId: {}, searchTerm: {})", accountId, searchTerm, e);
            throw new HealControlCenterException("Error counting applications.");
        }
    }


    /**
     * Retrieves application details based on the application name.
     *
     * @param name the name of the application
     * @return {@link ControllerBean} containing application ID, name, and identifier, or null if not found
     * @throws HealControlCenterException if an error occurs while querying
     */
    public ControllerBean getApplicationIdByName(String name) throws HealControlCenterException {
        String query = "select id, name, identifier from controller where controller_type_id=191 and name = ?";
        try {
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(ControllerBean.class), name);
        } catch (EmptyResultDataAccessException e) {
            return null;
        } catch (Exception e) {
            log.error("Error while fetching application details for name [{}]", name);
            throw new HealControlCenterException("Error while fetching application details");
        }
    }

    /**
     * Inserts a new application (controller) record into the database.
     *
     * @param controllerBean the {@link ControllerBean} containing application details
     * @return the generated ID of the inserted application
     * @throws HealControlCenterException if insertion fails or generated key is not available
     */
    public int insertController(ControllerBean controllerBean) throws HealControlCenterException {
        String query = "INSERT INTO controller " +
                "(name, identifier, account_id, user_details_id, created_time, updated_time, controller_type_id, " +
                "monitor_enabled, status, plugin_supr_interval, plugin_whitelist_status, environment) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        KeyHolder keyHolder = new GeneratedKeyHolder();

        try {
            jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(query, Statement.RETURN_GENERATED_KEYS);
                ps.setString(1, controllerBean.getName());
                ps.setString(2, controllerBean.getIdentifier());
                ps.setInt(3, controllerBean.getAccountId());
                ps.setString(4, controllerBean.getLastModifiedBy());
                ps.setString(5, controllerBean.getCreatedTime());
                ps.setString(6, controllerBean.getUpdatedTime());
                ps.setInt(7, controllerBean.getControllerTypeId());
                ps.setBoolean(8, true);
                ps.setInt(9, controllerBean.getStatus());
                ps.setInt(10, controllerBean.getPluginSuppressionInterval());
                ps.setBoolean(11, controllerBean.isPluginWhitelisted());
                ps.setString(12, controllerBean.getEnvironment());
                return ps;
            }, keyHolder);

            Number generatedId = keyHolder.getKey();
            if (generatedId != null) {
                return generatedId.intValue();
            } else {
                throw new HealControlCenterException("Failed to retrieve generated controller ID.");
            }

        } catch (Exception e) {
            log.error("Error while inserting controller [{}]. Details: {}", controllerBean.getIdentifier(), e.getMessage(), e);
            throw new HealControlCenterException("Error while inserting controller into the database.");
        }
    }

    /**
     * Retrieves application details based on its unique identifier.
     *
     * @param identifier the application identifier
     * @return {@link ControllerBean} or null if no match is found
     * @throws HealControlCenterException if any error occurs during fetch
     */
    public ControllerBean getApplicationIdByIdentifier(String identifier) throws HealControlCenterException {
        String query = "select id, name, identifier from controller where controller_type_id=191 and identifier = ?";
        try {
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(ControllerBean.class), identifier);
        } catch (EmptyResultDataAccessException e) {
            return null;
        } catch (Exception e) {
            log.error("Error while fetching application details for identifier [{}]", identifier);
            throw new HealControlCenterException("Error while fetching application details");
        }
    }

    /**
     * Inserts anomaly detection configuration for an application.
     *
     * @param configBean the {@link ApplicationAnomalyConfiguration } containing configuration details
     * @throws HealControlCenterException if there is a database or data formatting error
     */
    public void insertApplicationAnomalyConfigurations(ApplicationAnomalyConfiguration configBean) throws HealControlCenterException {
        String query = "INSERT INTO application_anomaly_configurations " +
                "(account_id, application_id, user_details_id, created_time, updated_time, " +
                "low_enable, medium_enable, high_enable, closing_window, max_data_breaks) " +
                "VALUES (?, ?, ?, NOW(), NOW(), ?, ?, ?, ?, ?)";

        try {
            int lowEnable = configBean.isLowEnable() ? 1 : 0;
            int mediumEnable = configBean.isMediumEnable() ? 1 : 0;
            int highEnable = configBean.isHighEnable() ? 1 : 0;

            int closingWindow = configBean.getClosingWindow();
            int maxDataBreaks = configBean.getMaxDataBreaks();

            jdbcTemplate.update(query,
                    configBean.getAccountId(),
                    configBean.getApplicationId(),
                    configBean.getUserDetailsId(),
                    lowEnable,
                    mediumEnable,
                    highEnable,
                    closingWindow,
                    maxDataBreaks);

            log.debug("Successfully inserted anomaly configuration for accountId [{}], applicationId [{}]", configBean.getAccountId(), configBean.getApplicationId());
        } catch (DataAccessException e) {
            log.error("Database error while inserting anomaly configuration for accountId [{}], application_anomaly_configurations: {}", configBean.getAccountId(), configBean, e);
            throw new HealControlCenterException("Database error during anomaly configuration insert");
        } catch (NumberFormatException e) {
            log.error("Invalid number format in anomaly config for accountId [{}], application_anomaly_configurations: {}", configBean.getAccountId(), configBean, e);
            throw new HealControlCenterException("Invalid numeric value in request");
        } catch (Exception e) {
            log.error("Unexpected error while inserting anomaly configuration for accountId [{}], application_anomaly_configurations: {}", configBean.getAccountId(), configBean, e);
            throw new HealControlCenterException("Unexpected error occurred during anomaly configuration insert");
        }
    }

    /**
     * Updates anomaly detection configuration for an application.
     * Updates user_details_id, updated_time, severity flags, closing window, and max data breaks for the given application_id.
     *
     * @param configBean the {@link ApplicationAnomalyConfiguration} containing updated configuration details
     * @throws HealControlCenterException if there is a database or data formatting error
     */
    public void updateApplicationAnomalyConfigurations(ApplicationAnomalyConfiguration configBean) throws HealControlCenterException {
        String query = "UPDATE application_anomaly_configurations SET user_details_id = ?, updated_time = ?, low_enable = ?, medium_enable = ?, high_enable = ?, closing_window = ?, max_data_breaks = ? WHERE application_id = ?";
        try {
            int lowEnable = configBean.isLowEnable() ? 1 : 0;
            int mediumEnable = configBean.isMediumEnable() ? 1 : 0;
            int highEnable = configBean.isHighEnable() ? 1 : 0;
            int rows = jdbcTemplate.update(query,
                    configBean.getUserDetailsId(),
                    configBean.getUpdatedTime(),
                    lowEnable,
                    mediumEnable,
                    highEnable,
                    configBean.getClosingWindow(),
                    configBean.getMaxDataBreaks(),
                    configBean.getApplicationId());
            log.info("[updateApplicationAnomalyConfigurations] Anomaly configuration updated for application_id [{}] (low_enable={}, medium_enable={}, high_enable={}, closing_window={}, max_data_breaks={}). Rows affected: {}", configBean.getApplicationId(), lowEnable, mediumEnable, highEnable, configBean.getClosingWindow(), configBean.getMaxDataBreaks(), rows);
        } catch (DataAccessException e) {
            log.error("[updateApplicationAnomalyConfigurations] Database error while updating anomaly configuration for application_id [{}]: {}", configBean.getApplicationId(), e.getMessage(), e);
            throw new HealControlCenterException("Database error during anomaly configuration update");
        } catch (NumberFormatException e) {
            log.error("[updateApplicationAnomalyConfigurations] Invalid number format in anomaly config for application_id [{}]: {}", configBean.getApplicationId(), e.getMessage(), e);
            throw new HealControlCenterException("Invalid numeric value in request");
        } catch (Exception e) {
            log.error("[updateApplicationAnomalyConfigurations] Unexpected error while updating anomaly configuration for application_id [{}]: {}", configBean.getApplicationId(), e.getMessage(), e);
            throw new HealControlCenterException("Unexpected error occurred during anomaly configuration update");
        }
    }

    /**
     * Inserts a new record into the `application_aliases` table for linked DC/DR application mapping.
     *
     * @param applicationAliases the {@link ApplicationAliases} object with alias mapping details
     * @return the generated alias ID
     * @throws HealControlCenterException if the insert fails or an exception occurs
     */
    public int insertApplicationAlias(ApplicationAliases applicationAliases) throws HealControlCenterException {
        String query = "INSERT INTO application_aliases " +
                "(common_name, status, dc_application_identifier, dr_application_identifier, user_details_id, created_time, updated_time) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?)";

        KeyHolder keyHolder = new GeneratedKeyHolder();

        try {
            jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(query, Statement.RETURN_GENERATED_KEYS);
                ps.setString(1, applicationAliases.getCommonName());
                ps.setInt(2, applicationAliases.getStatus());
                ps.setString(3, applicationAliases.getDcApplicationIdentifier());
                ps.setString(4, applicationAliases.getDrApplicationIdentifier());
                ps.setString(5, applicationAliases.getUserDetailsId());
                ps.setString(6, applicationAliases.getCreatedTime());
                ps.setString(7, applicationAliases.getUpdatedTime());
                return ps;
            }, keyHolder);

            if (keyHolder.getKey() != null) {
                return keyHolder.getKey().intValue();
            } else {
                throw new HealControlCenterException("Failed to retrieve generated application_aliases ID.");
            }

        } catch (Exception e) {
            log.error("Error while adding application alias [{}]. Details: ", applicationAliases, e);
            throw new HealControlCenterException("Error while adding application alias");
        }
    }

    /**
     * Checks whether an application exists with the given identifier.
     *
     * @param identifier Unique identifier of the application
     * @return true if an application with the identifier exists, false otherwise
     */
    public boolean existsByIdentifier(String identifier) {
        String sql = "SELECT COUNT(1) FROM controller WHERE controller_type_id=191 AND identifier = ?";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class, identifier);
        return count != null && count > 0;
    }

    /**
     * Checks whether an application exists with the given name.
     *
     * @param name Name of the application
     * @return true if an application with the name exists, false otherwise
     */
    public boolean existsByName(String name) throws HealControlCenterException {
        String sql = "SELECT COUNT(1) FROM controller WHERE controller_type_id=191 AND name = ?";
        try {
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, name);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("Error checking existence of application by name [{}]. Details: ", name, e);
            throw new HealControlCenterException("Error checking existence of application by name.");
        }
    }

    /**
     * Checks if an application with the given ID and account ID exists and is active.
     *
     * @param applicationId The ID of the application.
     * @param accountId     The ID of the account.
     * @return true if the application exists and is active, false otherwise.
     * @throws HealControlCenterException if a database error occurs.
     */
    public boolean isApplicationExists(int applicationId, int accountId) throws HealControlCenterException {
        String query = "SELECT COUNT(*) FROM controller WHERE id = ? AND account_id = ? AND status = 1 AND controller_type_id = 191";
        try {
            Integer count = jdbcTemplate.queryForObject(query, Integer.class, applicationId, accountId);
            return count != null && count > 0;
        } catch (EmptyResultDataAccessException e) {
            log.info("Application with ID [{}] not found for account ID [{}].", applicationId, accountId);
            return false;
        } catch (Exception e) {
            log.error("Error checking if application exists for ID [{}] and account ID [{}]. Details: ", applicationId, accountId, e);
            throw new HealControlCenterException("Error checking application existence.");
        }
    }

    /**
     * Deletes application notification mappings for the given application ID.
     *
     * @param id Application ID
     * @return Number of rows affected
     * @throws HealControlCenterException if any SQL exception occurs
     */
    public int deleteApplicationNotificationMappingWithAppId(int id) throws HealControlCenterException {
        String query = "DELETE FROM application_notification_mapping where application_id = ? ";
        try {
            return jdbcTemplate.update(query, id);
        } catch (Exception ex) {
            log.error("Error in deleting application notification mapping with app id.");
            throw new HealControlCenterException("Error in deleting application notification mapping with app id.");
        }
    }

    /**
     * Deletes percentile data associated with the given application ID.
     *
     * @param id Application ID
     * @return Number of rows affected
     * @throws HealControlCenterException if any SQL exception occurs
     */
    public int deleteApplicationPercentilesWithAppId(int id) throws HealControlCenterException {
        String query = "DELETE FROM application_percentiles where application_id = ? ";
        try {
            return jdbcTemplate.update(query, id);
        } catch (Exception ex) {
            log.error("Error in deleting application percentile mapping with app id.");
            throw new HealControlCenterException("Error in deleting application percentile mapping with app id.");
        }
    }

    /**
     * Deletes tag mappings for a given object ID and reference table.
     *
     * @param id             Object ID (typically application ID)
     * @param objectRefTable Reference table name
     * @return Number of rows affected
     * @throws HealControlCenterException if any SQL exception occurs
     */
    public int deleteTagMappingByEntityId(int id, String objectRefTable) throws HealControlCenterException {
        String query = "DELETE FROM tag_mapping WHERE object_id = ? AND object_ref_table = ?";
        try {
            return jdbcTemplate.update(query, id, objectRefTable);
        } catch (Exception e) {
            log.error("Error deleting tag_mapping for id [{}] and objectRefTable [{}].", id, objectRefTable, e);
            throw new HealControlCenterException("Error deleting tag mapping by entity id: " + e.getMessage());
        }
    }

    /**
     * Deletes anomaly configurations associated with the given application ID.
     *
     * @param id Application ID
     * @return Number of rows affected
     * @throws HealControlCenterException if any SQL exception occurs
     */
    public int deleteApplicationAnomalyConfigByAppId(int id) throws HealControlCenterException {
        String query = "DELETE FROM application_anomaly_configurations WHERE application_id = ?";
        try {
            return jdbcTemplate.update(query, id);
        } catch (Exception ex) {
            log.error("Error in deleting anomaly config for application identifier [{}].", id, ex);
            throw new HealControlCenterException("Error in deleting anomaly config.");
        }
    }

    /**
     * Deletes alias entries where either DC or DR application identifier matches the given identifier.
     *
     * @param applicationIdentifier Application identifier (DC or DR)
     * @return Number of rows affected
     * @throws HealControlCenterException if any SQL exception occurs
     */
    public int deleteApplicationAliasByAppId(String applicationIdentifier) throws HealControlCenterException {
        String query = "DELETE FROM application_aliases WHERE dc_application_identifier = ? OR dr_application_identifier = ?";
        try {
            return jdbcTemplate.update(query, applicationIdentifier, applicationIdentifier);
        } catch (Exception ex) {
            log.error("Error in deleting application alias mapping with application identifier [{}].", applicationIdentifier, ex);
            throw new HealControlCenterException("Error in deleting application alias mapping.");
        }
    }

    /**
     * Deletes a controller entry with the given controller ID.
     *
     * @param id Controller ID
     * @return Number of rows affected
     * @throws HealControlCenterException if any SQL exception occurs
     */
    public int deleteControllerWithId(int id) throws HealControlCenterException {
        String query = "DELETE FROM controller where id = ?";
        try {
            return jdbcTemplate.update(query, id);
        } catch (Exception ex) {
            log.error("Error in deleting controller with id.");
            throw new HealControlCenterException("Error in deleting controller with id.");
        }
    }

    /**
     * Retrieves a list of services mapped to a given application in the account.
     *
     * @param accountId the account ID
     * @param appId     the application identifier
     * @return list of {@link ViewApplicationServiceMappingBean} or empty list if none found
     */
    public List<ViewApplicationServiceMappingBean> getServicesMappedToApplication(int accountId, String appId) {
        String query = "select application_id applicationId, application_name applicationName, application_identifier applicationIdentifier, " +
                "service_id serviceId, service_name serviceName,service_identifier serviceIdentifier from view_application_service_mapping " +
                "where account_id = ? and application_identifier = ?";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ViewApplicationServiceMappingBean.class), accountId, appId);
        } catch (EmptyResultDataAccessException e) {
            log.info("No services mapped to applicationId [{}] and accountId [{}]", appId, accountId);
            return Collections.emptyList();
        } catch (Exception ex) {
            log.error("No services mapped to applicationId [{}] and accountId [{}]. Details: ", appId, accountId, ex);
            //throw new HealControlCenterException("Error fetching services mapped to application.");
        }
        return Collections.emptyList();
    }

    public List<ViewApplicationServiceMappingBean> getServicesMappedToApplications(int accountId, List<String> appIds) throws HealControlCenterException {
        String query = "select application_id applicationId, application_name applicationName, application_identifier applicationIdentifier, " +
                "service_id serviceId, service_name serviceName,service_identifier serviceIdentifier from view_application_service_mapping " +
                "where account_id = ? and application_identifier IN (" + String.join(",", Collections.nCopies(appIds.size(), "?")) + ")";
        try {
            List<Object> params = new ArrayList<>();
            params.add(accountId);
            params.addAll(appIds);
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ViewApplicationServiceMappingBean.class), params.toArray());
        } catch (EmptyResultDataAccessException e) {
            log.info("No services mapped to applicationIds and accountId [{}]", accountId);
            return Collections.emptyList();
        } catch (Exception ex) {
            log.error("No services mapped to applicationIds and accountId [{}]. Details: ", accountId, ex);
            throw new HealControlCenterException("Error fetching services mapped to applications.");
        }
    }

    public List<ClusterComponentDetails> getHostClusterComponentDetailsForService(String serviceIdentifier) throws HealControlCenterException {
        String query = "select vc.id,vc.name,vc.identifier,vci.mst_component_id componentId," +
                " vci.component_name componentName, vci.mst_component_type_id componentTypeId, vci.component_type_name componentTypeName," +
                " vci.mst_component_version_id componentVersionId, vci.component_version_name componentVersionName, vci.common_version_id commonVersionId, " +
                " vci.common_version_name commonVersionName from" +
                " view_cluster_services vc, view_component_instance vci where vc.id = vci.id and vc.mst_component_type_id = 1" +
                " and vc.service_identifier= ? ";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ClusterComponentDetails.class), serviceIdentifier);
        } catch (EmptyResultDataAccessException e) {
            log.info("No host clusters/instances mapped to serviceIdentifier [{}]", serviceIdentifier);
            return Collections.emptyList();
        } catch (Exception ex) {
            log.error("Error in getting host cluster component details for service. Details: ", ex);
            throw new HealControlCenterException("Error in getting host cluster component details for service");
        }
    }

    public List<ClusterComponentDetails> getHostClusterComponentDetailsForServices(List<String> serviceIdentifiers) throws HealControlCenterException {
        String query = "select vc.id,vc.name,vc.identifier,vci.mst_component_id componentId," +
                " vci.component_name componentName, vci.mst_component_type_id componentTypeId, vci.component_type_name componentTypeName," +
                " vci.mst_component_version_id componentVersionId, vci.component_version_name componentVersionName, vci.common_version_id commonVersionId, " +
                " vci.common_version_name commonVersionName, vc.service_identifier serviceIdentifier from" +
                " view_cluster_services vc, view_component_instance vci where vc.id = vci.id and vc.mst_component_type_id = 1" +
                " and vc.service_identifier IN (" + String.join(",", Collections.nCopies(serviceIdentifiers.size(), "?")) + ")";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ClusterComponentDetails.class), serviceIdentifiers.toArray());
        } catch (EmptyResultDataAccessException e) {
            log.info("No host clusters/instances mapped to serviceIdentifiers");
            return Collections.emptyList();
        } catch (Exception ex) {
            log.error("Error in getting host cluster component details for services. Details: ", ex);
            throw new HealControlCenterException("Error in getting host cluster component details for services");
        }
    }

    public List<ClusterComponentDetails> getComponentClusterComponentDetailsForService(String serviceIdentifier) throws HealControlCenterException {
        String query = "select vc.id,vc.name,vc.identifier,vci.mst_component_id componentId," +
                " vci.component_name componentName, vci.mst_component_type_id componentTypeId, vci.component_type_name componentTypeName," +
                " vci.mst_component_version_id componentVersionId, vci.component_version_name componentVersionName,vci.common_version_id commonVersionId, " +
                " vci.common_version_name commonVersionName from" +
                " view_cluster_services vc, view_component_instance vci where vc.id = vci.id and vc.mst_component_type_id != 1" +
                " and vc.service_identifier= ? ";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ClusterComponentDetails.class), serviceIdentifier);
        } catch (EmptyResultDataAccessException e) {
            log.info("No component clusters/instances mapped to serviceIdentifier [{}]", serviceIdentifier);
            return Collections.emptyList();
        } catch (Exception ex) {
            log.error("Error in getting component cluster component details for service. Details: ", ex);
            throw new HealControlCenterException("Error in getting component cluster component details for service");
        }
    }

    public List<ClusterComponentDetails> getComponentClusterComponentDetailsForServices(List<String> serviceIdentifiers) throws HealControlCenterException {
        String query = "select vc.id,vc.name,vc.identifier,vci.mst_component_id componentId," +
                " vci.component_name componentName, vci.mst_component_type_id componentTypeId, vci.component_type_name componentTypeName," +
                " vci.mst_component_version_id componentVersionId, vci.component_version_name componentVersionName,vci.common_version_id commonVersionId, " +
                " vci.common_version_name commonVersionName, vc.service_identifier serviceIdentifier from" +
                " view_cluster_services vc, view_component_instance vci where vc.id = vci.id and vc.mst_component_type_id != 1" +
                " and vc.service_identifier IN (" + String.join(",", Collections.nCopies(serviceIdentifiers.size(), "?")) + ")";
        try {
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ClusterComponentDetails.class), serviceIdentifiers.toArray());
        } catch (EmptyResultDataAccessException e) {
            log.info("No component clusters/instances mapped to serviceIdentifiers");
            return Collections.emptyList();
        } catch (Exception ex) {
            log.error("Error in getting component cluster component details for services. Details: ", ex);
            throw new HealControlCenterException("Error in getting component cluster component details for services");
        }
    }

    public List<CompInstClusterDetailsBean> getCompInstanceDetails(int accountId) throws HealControlCenterException {
        String query = "select id instanceId,common_version_id commonVersionId, common_version_name commonVersionName,mst_component_id compId,component_name componentName, " +
                "mst_component_type_id mstComponentTypeId,component_type_name componentTypeName, " +
                "mst_component_version_id compVersionId,component_version_name componentVersionName,name instanceName,host_id hostId,status, " +
                "host_name hostName,is_cluster isCluster,identifier, " +
                "host_address hostAddress, supervisor_id supervisorId from view_component_instance where account_id = ? and status = 1";
        try {
            log.debug("getting component instance details.");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(CompInstClusterDetailsBean.class), accountId);
        } catch (Exception e) {
            log.error("Error occurred while fetching component instance list from 'view_component_instance' table for accountId [{}]. Details: ", accountId, e);
            throw new HealControlCenterException("Error occurred while fetching component instance details.");
        }
    }

    public ControllerBean getControllerByIdentifierOrName(String identifier, String name) throws HealControlCenterException {
        String query = "SELECT id, name, identifier, account_id as accountId, user_details_id as userDetailsId, " +
                "created_time as createdTime, updated_time as updatedTime, controller_type_id as controllerTypeId, status " +
                "FROM controller WHERE identifier = ? OR name = ?";

        List<ControllerBean> results;
        try {
            results = jdbcTemplate.query(
                    query,
                    new BeanPropertyRowMapper<>(ControllerBean.class),
                    identifier,
                    name
            );
        } catch (Exception e) {
            log.error("Error occurred while fetching controller by identifier [{}] or name [{}]. Details: ", identifier, name, e);
            throw new HealControlCenterException("Error occurred while fetching controller by identifier or name.");
        }
        return results.isEmpty() ? null : results.get(0);
    }

    public Integer getControllerApplicationId(int controllerId, int tagId, int accountId) throws HealControlCenterException {
        String query = "SELECT tm.object_id " +
                "FROM tag_mapping tm " +
                "JOIN controller c ON tm.tag_key = c.id AND tm.account_id = c.account_id " +
                "WHERE tm.account_id = ? AND c.id = ? AND tm.tag_id = ? AND tm.object_ref_table = 'controller'";
        try {
            return jdbcTemplate.queryForObject(query, Integer.class, accountId, controllerId, tagId);
        } catch (EmptyResultDataAccessException e) {
            log.info("No application mapping found for controllerId [{}], tagId [{}], accountId [{}]", controllerId, tagId, accountId);
            return null;
        } catch (Exception e) {
            log.error("Error while fetching applicationId for controllerId [{}], tagId [{}], accountId [{}]. Details: ", controllerId, tagId, accountId, e);
            throw new HealControlCenterException("Error while fetching controller application ID.");
        }
    }

    public int updateController(ControllerBean controllerBean) throws HealControlCenterException {
        String query = "UPDATE controller SET name = ?, identifier = ?, user_details_id = ?, updated_time = ?, " +
                "monitor_enabled = ?, status = ?, plugin_supr_interval = ?, plugin_whitelist_status = ?, environment = ? " +
                "WHERE id = ? AND account_id = ?";
        try {
            return jdbcTemplate.update(query,
                    controllerBean.getName(),
                    controllerBean.getIdentifier(),
                    controllerBean.getLastModifiedBy(),
                    controllerBean.getUpdatedTime(),
                    true,
                    controllerBean.getStatus(),
                    controllerBean.getPluginSuppressionInterval(),
                    controllerBean.isPluginWhitelisted(),
                    controllerBean.getEnvironment(),
                    controllerBean.getId(),
                    controllerBean.getAccountId());
        } catch (Exception e) {
            log.error("Error updating controller: {}", controllerBean.getIdentifier(), e);
            throw new HealControlCenterException("Error updating controller.");
        }
    }

    /**
     * Retrieves full details of an application (controller_type_id=191) by its unique identifier.
     * Returns a ControllerBean populated with all relevant fields, or null if not found.
     *
     * @param identifier Unique identifier of the application
     * @return ControllerBean with application details, or null if not found
     * @throws HealControlCenterException if any error occurs during fetch
     */
    public ControllerBean getFullApplicationDetailsByIdentifier(String identifier) throws HealControlCenterException {
        String query = "select id, name, identifier, account_id, user_details_id, created_time,updated_time,controller_type_id," +
                " status, plugin_supr_interval, environment from controller where controller_type_id=191 and identifier = ?";
        try {
            log.debug("[getFullApplicationDetailsByIdentifier] Fetching full application details for identifier: {}", identifier);
            return jdbcTemplate.queryForObject(query, (rs, rowNum) -> {
                ControllerBean controller = new ControllerBean();
                controller.setId(rs.getInt("id"));
                controller.setName(rs.getString("name"));
                controller.setIdentifier(rs.getString("identifier"));
                controller.setAccountId(rs.getInt("account_id"));
                controller.setLastModifiedBy(rs.getString("user_details_id"));
                controller.setCreatedTime(rs.getString("created_time"));
                controller.setUpdatedTime(rs.getString("updated_time"));
                controller.setControllerTypeId(rs.getInt("controller_type_id"));
                controller.setStatus(rs.getInt("status"));
                controller.setPluginSuppressionInterval(rs.getInt("plugin_supr_interval"));
                controller.setEnvironment(rs.getString("environment"));
                return controller;
            }, identifier);
        } catch (EmptyResultDataAccessException e) {
            log.warn("[getFullApplicationDetailsByIdentifier] No controller found with identifier: {}", identifier);
            return null;
        } catch (Exception e) {
            log.error("[getFullApplicationDetailsByIdentifier] Error while fetching controller for identifier: {}. Details: ", identifier, e);
            throw new HealControlCenterException("Error in fetching controller for identifier: " + identifier);
        }
    }

    /**
     * Checks whether an application exists with the given identifier.
     *
     * @param identifier Unique identifier of the application
     * @return true if an application with the identifier exists, false otherwise
     */
    public boolean existsByApplicationIdentifier(String identifier) throws HealControlCenterException {
        String sql = "SELECT COUNT(1) FROM controller WHERE controller_type_id=191 AND identifier = ?";
        try {
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, identifier);
            log.debug("[existsByApplicationIdentifier] Checking existence for identifier: {}. Count: {}", identifier, count);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("Error checking existence of application by identifier [{}]. Details: ", identifier, e);
            throw new HealControlCenterException("Error checking existence of application by identifier.");
        }
    }

    /**
     * Updates an existing application (controller_type_id=191) in the controller table.
     * Only updates name, user_details_id, updated_time, environment fields.
     *
     * @param controllerBean ControllerBean with updated values
     * @throws HealControlCenterException if update fails
     */
    public void updateApplication(ControllerBean controllerBean) throws HealControlCenterException {
        String query = "UPDATE controller SET name = ?, user_details_id = ?, updated_time = ? WHERE id = ? AND controller_type_id = 191";
        try {
            int rows = jdbcTemplate.update(query,
                    controllerBean.getName(),
                    controllerBean.getLastModifiedBy(),
                    controllerBean.getUpdatedTime(),
                    controllerBean.getId());
            log.info("[updateApplication] Updated application [{}] with id [{}]. Rows affected: {}", controllerBean.getName(), controllerBean.getId(), rows);
        } catch (Exception e) {
            log.error("[updateApplication] Error while updating application [{}]. Details: ", controllerBean, e);
            throw new HealControlCenterException("Error while updating application");
        }
    }

    /**
     * Fetches ApplicationAliases by dc_application_identifier.
     *
     * @param dcIdentifier DC application identifier
     * @return ApplicationAliases object or null if not found
     * @throws HealControlCenterException if any error occurs during fetch
     */
    public ApplicationAliases getApplicationAliasByDcIdentifier(String dcIdentifier) throws HealControlCenterException {
        String query = "SELECT id, common_name, status, dc_application_identifier, dr_application_identifier, user_details_id, created_time, updated_time " +
                "FROM application_aliases WHERE dc_application_identifier = ?";
        try {
            log.debug("[getApplicationAliasByDcIdentifier] Fetching alias for DC identifier: {}", dcIdentifier);
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(ApplicationAliases.class), dcIdentifier);
        } catch (EmptyResultDataAccessException e) {
            log.warn("[getApplicationAliasByDcIdentifier] No alias found for DC identifier: {}", dcIdentifier);
            return null;
        } catch (Exception e) {
            log.error("[getApplicationAliasByDcIdentifier] Error fetching application_aliases by dc_application_identifier [{}]", dcIdentifier, e);
            throw new HealControlCenterException("Error fetching application_aliases by dc_application_identifier");
        }
    }

    /**
     * Fetches ApplicationAliases by dr_application_identifier.
     *
     * @param drIdentifier DR application identifier
     * @return ApplicationAliases object or null if not found
     * @throws HealControlCenterException if any error occurs during fetch
     */
    public ApplicationAliases getApplicationAliasByDrIdentifier(String drIdentifier) throws HealControlCenterException {
        String query = "SELECT id, common_name, status, dc_application_identifier, dr_application_identifier, user_details_id, created_time, updated_time " +
                "FROM application_aliases WHERE dr_application_identifier = ?";
        try {
            log.debug("[getApplicationAliasByDrIdentifier] Fetching alias for DR identifier: {}", drIdentifier);
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(ApplicationAliases.class), drIdentifier);
        } catch (EmptyResultDataAccessException e) {
            log.warn("[getApplicationAliasByDrIdentifier] No alias found for DR identifier: {}", drIdentifier);
            return null;
        } catch (Exception e) {
            log.error("[getApplicationAliasByDrIdentifier] Error fetching application_aliases by dr_application_identifier [{}]", drIdentifier, e);
            throw new HealControlCenterException("Error fetching application_aliases by dr_application_identifier");
        }
    }

    /**
     * Updates an existing ApplicationAliases row by id.
     *
     * @param alias ApplicationAliases object with updated values
     * @throws HealControlCenterException if update fails
     */
    public void updateApplicationAlias(ApplicationAliases alias) throws HealControlCenterException {
        String query = "UPDATE application_aliases SET common_name = ?, status = ?, dc_application_identifier = ?, dr_application_identifier = ?, user_details_id = ?, updated_time = ? WHERE id = ?";
        try {
            int rows = jdbcTemplate.update(query,
                    alias.getCommonName(),
                    alias.getStatus(),
                    alias.getDcApplicationIdentifier(),
                    alias.getDrApplicationIdentifier(),
                    alias.getUserDetailsId(),
                    alias.getUpdatedTime(),
                    alias.getId());
            log.info("[updateApplicationAlias] Updated application_aliases [{}]. Rows affected: {}", alias.getId(), rows);
        } catch (Exception e) {
            log.error("[updateApplicationAlias] Error updating application_aliases [{}]", alias, e);
            throw new HealControlCenterException("Error updating application_aliases");
        }
    }

    /**
     * Counts the number of applications in the controller table that match the given accountId,
     * are active (status = 1), have controller_type_id = 191, and match the provided search term
     * and list of application identifiers.
     *
     * @param accountId              the ID of the account
     * @param searchTerm             optional search term to filter by name or identifier
     * @param applicationIdentifiers list of application identifiers to include
     * @return the count of matching applications; returns 0 if list is empty or an error occurs
     */
    public int countApplicationsWithIdentifiers(int accountId, String searchTerm, List<String> applicationIdentifiers) {
        if (applicationIdentifiers == null || applicationIdentifiers.isEmpty()) {
            return 0;
        }

        String query = "SELECT COUNT(*) FROM controller WHERE account_id = ? AND status = 1 AND controller_type_id = 191";
        List<Object> params = new ArrayList<>();
        params.add(accountId);

        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            query += " AND (LOWER(name) LIKE ? OR LOWER(identifier) LIKE ?)";
            params.add("%" + searchTerm.toLowerCase() + "%");
            params.add("%" + searchTerm.toLowerCase() + "%");
        }

        query += " AND identifier IN (" + String.join(",", Collections.nCopies(applicationIdentifiers.size(), "?")) + ")";
        params.addAll(applicationIdentifiers);

        try {
            return jdbcTemplate.queryForObject(query, Integer.class, params.toArray());
        } catch (Exception e) {
            log.error("Error counting applications with identifiers", e);
            return 0;
        }
    }

    /**
     * Retrieves a paginated list of applications from the controller table that match the given accountId,
     * are active (status = 1), have controller_type_id = 191, and match the provided search term and list
     * of application identifiers.
     *
     * @param accountId              the ID of the account
     * @param searchTerm             optional search term to filter by name or identifier
     * @param applicationIdentifiers list of application identifiers to include
     * @param pageable               pagination information (offset, limit, sort)
     * @return list of matching ControllerBean objects; empty list if no match or error
     */
    public List<ControllerBean> getApplicationsListWithIdentifiers(int accountId,
                                                                   String searchTerm,
                                                                   List<String> applicationIdentifiers,
                                                                   Pageable pageable) {
        long startTime = System.currentTimeMillis();

        if (applicationIdentifiers == null || applicationIdentifiers.isEmpty()) {
            return Collections.emptyList();
        }

        String baseQuery = "SELECT id, name, controller_type_id AS controllerTypeId, identifier, status, " +
                "user_details_id AS lastModifiedBy, created_time AS createdTime, updated_time AS updatedTime, " +
                "account_id AS accountId, environment " +
                "FROM controller WHERE account_id = ? AND status = 1 AND controller_type_id = 191";

        List<Object> params = new ArrayList<>();
        params.add(accountId);

        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            baseQuery += " AND (LOWER(name) LIKE ? OR LOWER(identifier) LIKE ?)";
            String likeTerm = "%" + searchTerm.toLowerCase().trim() + "%";
            params.add(likeTerm);
            params.add(likeTerm);
        }

        String inClause = String.join(",", Collections.nCopies(applicationIdentifiers.size(), "?"));
        baseQuery += " AND identifier IN (" + inClause + ")";
        params.addAll(applicationIdentifiers);

        String finalQuery = PaginationUtils.applyPagination(baseQuery, pageable);
        params = PaginationUtils.buildPaginationParams(params, pageable);

        try {
            log.debug("Fetching paginated application list with identifiers for accountId={}, searchTerm={}", accountId, searchTerm);
            return jdbcTemplate.query(finalQuery, new BeanPropertyRowMapper<>(ControllerBean.class), params.toArray());
        } catch (Exception e) {
            log.error("Error fetching applications with identifiers (accountId: {}, searchTerm: {}, pageable: {})", accountId, searchTerm, pageable, e);
            return Collections.emptyList();
        } finally {
            long endTime = System.currentTimeMillis();
            log.info("getApplicationsListWithIdentifiers(accountId: {}, searchTerm: {}, pageable: {}) completed in {} ms",
                    accountId, searchTerm, pageable, (endTime - startTime));
        }
    }

    /**
     * Performs a soft delete of an application by setting its status to 0 for the given controller ID.
     *
     * @param id the ID of the controller (application) to soft delete
     * @return number of rows updated (should be 1 if successful)
     * @throws HealControlCenterException if an error occurs during update
     */
    public int softDeleteApplicationById(int id) throws HealControlCenterException {
        String query = "UPDATE controller SET status = 0 WHERE id = ?";
        try {
            return jdbcTemplate.update(query, id);
        } catch (Exception ex) {
            log.error("Error in soft deleting controller with id.");
            throw new HealControlCenterException("Error in soft deleting controller with id.");
        }
    }

    /**
     * Updates the status of a controller in the database.
     * Throws a RuntimeException if any error occurs during update.
     *
     * @param controllerId The ID of the controller to update.
     * @param status       The new status to set.
     * @param userId       The ID of the user performing the update.
     */
    public void updateControllerStatus(int controllerId, int status, String userId) throws HealControlCenterException {
        String query = "UPDATE controller SET status = ?, user_details_id = ?, updated_time = NOW() WHERE id = ?";
        try {
            jdbcTemplate.update(query, status, userId, controllerId);
        } catch (Exception e) {
            log.error("Error updating controller status for ID [{}]. Details: ", controllerId, e);
            throw new HealControlCenterException("Error updating controller status for ID [" + controllerId + "]");
        }
    }

    /**
     * Performs a soft delete of application aliases by setting their status to 0
     * for the given application identifier.
     *
     * @param appIdentifier the identifier of the application whose aliases should be soft deleted
     * @return number of rows updated (may be 0 if no match)
     * @throws HealControlCenterException if an error occurs during update
     */
    public int softDeleteApplicationAliasByAppId(String appIdentifier) throws HealControlCenterException {
        String query = "UPDATE application_aliases SET status = 0 WHERE application_identifier = ?";
        try {
            return jdbcTemplate.update(query, appIdentifier);
        } catch (Exception ex) {
            log.error("Error in soft deleting application_aliases with identifier: {}", appIdentifier);
            throw new HealControlCenterException("Error in soft deleting application_aliases with identifier: " + appIdentifier);
        }
    }

    /* Deletes a controller from the database.
     * Throws a RuntimeException if any error occurs during deletion.
     *
     * @param controllerId The ID of the controller to delete.
     */
    public void deleteController(int controllerId) throws HealControlCenterException {
        String query = "DELETE FROM controller WHERE id = ?";
        try {
            jdbcTemplate.update(query, controllerId);
        } catch (Exception e) {
            log.error("Error deleting controller with ID [{}]. Details: ", controllerId, e);
            throw new HealControlCenterException("Error deleting controller with ID [" + controllerId + "]");
        }
    }

    /**
     * Retrieves a ServiceBean (ControllerBean) by its identifier and account ID.
     * Throws a RuntimeException if any error occurs during fetch.
     *
     * @param serviceIdentifier The identifier of the service.
     * @param accountId         The ID of the account.
     * @return ServiceBean object if found, null otherwise.
     */
    public ServiceBean getServiceBeanByIdentifierAndAccount(String serviceIdentifier, int accountId) throws HealControlCenterException {
        String query = "SELECT id, name, identifier, account_id AS accountId, user_details_id AS lastModifiedBy, " +
                "created_time AS createdTime, updated_time AS updatedTime, controller_type_id AS controllerTypeId, status, environment " +
                "FROM controller WHERE identifier = ? AND account_id = ? AND controller_type_id = 192"; // Assuming 192 is service type
        try {
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(ServiceBean.class), serviceIdentifier, accountId);
        } catch (EmptyResultDataAccessException e) {
            log.info("No service found with identifier [{}] for accountId [{}].", serviceIdentifier, accountId);
            return null;
        } catch (Exception e) {
            log.error("Error fetching service by identifier [{}] and accountId [{}]. Details: ", serviceIdentifier, accountId, e);
            throw new HealControlCenterException("Error fetching service by identifier [" + serviceIdentifier + "] and accountId [" + accountId + "]");
        }
    }

    /**
     * Retrieves all services (controller_type_id = 192) for the specified accountId.
     * Returns an empty list if no services are found.
     * Throws HealControlCenterException for any database errors.
     *
     * @param accountId the account ID
     * @return list of ControllerBean representing all services for the account
     * @throws HealControlCenterException if any error occurs during fetch
     */
    public List<ControllerBean> getAllServicesByAccountId(int accountId) throws HealControlCenterException {
        String query = "select id, name, identifier, account_id as accountId, user_details_id as userDetailsId, " +
                "created_time as createdTime, updated_time as updatedTime, controller_type_id as controllerTypeId, status " +
                "from controller where controller_type_id = 192 and account_id = ?";
        try {
            log.debug("Fetching all services for accountId={}", accountId);
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ControllerBean.class), accountId);
        } catch (EmptyResultDataAccessException e) {
            log.info("No services found for accountId [{}]", accountId);
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("Error occurred while fetching all services for accountId [{}]. Details: ", accountId, e);
            throw new HealControlCenterException("Error occurred while fetching all services for accountId: " + accountId);
        }
    }
}
