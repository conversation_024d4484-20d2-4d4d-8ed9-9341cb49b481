package com.heal.controlcenter.dao.mysql;

import com.heal.configuration.entities.ConnectionBean;
import com.heal.controlcenter.beans.ConnectionDetailsBean;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.util.PaginationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.ArrayList;

@Slf4j
@Repository
public class ConnectionDetailsDao {

    private final JdbcTemplate jdbcTemplate;

    public ConnectionDetailsDao(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public List<ConnectionDetailsBean> getConnectionsByAccountId(Integer accountId) throws HealControlCenterException {
        String query = "select cd.id, source_id sourceId, source_ref_object sourceRefObject, destination_id destinationId, " +
                "destination_ref_object destinationRefObject, cd.created_time createdTime, cd.updated_time updatedTime, " +
                "cd.account_id accountId, cd.user_details_id userDetailsId, is_discovery isDiscovery, c1.name sourceName, c1.identifier sourceIdentifier, " +
                "c2.name destinationName, c2.identifier destinationIdentifier from connection_details cd, controller c1, controller c2 " +
                "where  c1.id=source_id and c2.id=destination_id and cd.account_id = ?";
        try {
            log.debug("getting connections.");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ConnectionDetailsBean.class), accountId);
        } catch (EmptyResultDataAccessException e) {
            log.info("Service connections unavailable for accountId [{}]", accountId);
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("Error occurred while fetching connections for accountId [{}]. Details: ", accountId, e);
            throw new HealControlCenterException("Error occurred while fetching connections.");
        }
    }

    /**
     * Adds a list of connection details to the database.
     * <p>
     * Logs the operation and handles exceptions, returning an empty array if the operation fails.
     *
     * @param connectionDetailsBeanList List of ConnectionDetailsBean to add
     * @return Array of update counts for each insert operation, or empty array if failed
     */
    public int[] addConnection(List<ConnectionDetailsBean> connectionDetailsBeanList) throws HealControlCenterException {
        String sql = "insert into connection_details (source_id, source_ref_object, destination_id, destination_ref_object, created_time, updated_time, account_id, user_details_id, is_discovery) " +
                "values (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        try {
            log.info("Adding {} connection(s) to the database.", connectionDetailsBeanList.size());
            int[] result = new int[connectionDetailsBeanList.size()];
            for (int i = 0; i < connectionDetailsBeanList.size(); i++) {
                ConnectionDetailsBean bean = connectionDetailsBeanList.get(i);
                result[i] = jdbcTemplate.update(sql,
                        bean.getSourceId(),
                        bean.getSourceRefObject(),
                        bean.getDestinationId(),
                        bean.getDestinationRefObject(),
                        bean.getCreatedTime(),
                        bean.getUpdatedTime(),
                        bean.getAccountId(),
                        bean.getUserDetailsId(),
                        bean.getIsDiscovery()
                );
                log.debug("Inserted connection [{}] for sourceId [{}], destinationId [{}], accountId [{}]", result[i], bean.getSourceId(), bean.getDestinationId(), bean.getAccountId());
            }
            return result;
        } catch (Exception e) {
            log.error("Error occurred while adding connections. Details: {}", e.getMessage(), e);
            throw new HealControlCenterException("Error in adding connections");
        }
    }

    /**
     * Fetches all service connection beans for a given account.
     * <p>
     * Logs the operation and handles exceptions, returning an empty list if no connections are found or an error occurs.
     *
     * @param accountId Account ID
     * @return List of ConnectionBean for the account, or empty list if none found
     */
    public List<ConnectionBean> getServiceConnectionBeansForAccount(int accountId) throws HealControlCenterException {
        String query = "select cd.source_id as sourceId, cd.destination_id as destinationId, cd.created_time as createdTime, cd.updated_time as updatedTime, cd.user_details_id as modifiedBy, " +
                "c1.name as destinationName, c1.identifier as destinationIdentifier, c2.name as sourceName, c2.identifier as sourceIdentifier " +
                "from connection_details cd, controller c1, controller c2 where c1.id=cd.destination_id and c2.id=cd.source_id and cd.account_id=?";
        try {
            log.info("Fetching service connection beans for account [{}]", accountId);
            return jdbcTemplate.query(query, new org.springframework.jdbc.core.BeanPropertyRowMapper<>(com.heal.configuration.entities.ConnectionBean.class), accountId);
        } catch (EmptyResultDataAccessException e) {
            log.warn("No service connection beans found for account [{}]", accountId);
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("Error occurred while fetching service connection beans for account [{}]. Details: {}", accountId, e.getMessage(), e);
            throw new HealControlCenterException("Error in fetching service connection beans for accountId: " + accountId);
        }
    }

    /**
     * Retrieves a paginated and optionally filtered list of connection details for a given account ID.
     *
     * <p>This method fetches connection records by joining the `connection_details` table with the
     * `controller` table (as source and destination). If a search term is provided, it filters the results
     * based on a case-insensitive match on controller names and identifiers.</p>
     *
     * @param accountId  The ID of the account for which connections are to be fetched.
     * @param searchTerm An optional search term to filter results by source/destination names or identifiers.
     * @param pageable   Pagination information including page number, size, and sorting.
     * @return A list of {@link ConnectionDetailsBean} matching the account ID and search criteria.
     * Returns an empty list if no results are found or an error occurs.
     */
    public List<ConnectionDetailsBean> getConnectionsByAccountId(Integer accountId, String searchTerm, Pageable pageable)
            throws HealControlCenterException {
        String baseQuery = "select cd.id, source_id sourceId, source_ref_object sourceRefObject, destination_id destinationId, " +
                "destination_ref_object destinationRefObject, cd.created_time createdTime, cd.updated_time updatedTime, " +
                "cd.account_id accountId, cd.user_details_id userDetailsId, is_discovery isDiscovery, c1.name sourceName, c1.identifier sourceIdentifier, " +
                "c2.name destinationName, c2.identifier destinationIdentifier from connection_details cd, controller c1, controller c2 " +
                "where c1.id=source_id and c2.id=destination_id and cd.account_id = ?";
        List<Object> params = new ArrayList<>();
        params.add(accountId);
        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            baseQuery += " AND (LOWER(c1.name) LIKE ? OR LOWER(c1.identifier) LIKE ? OR LOWER(c2.name) LIKE ? OR LOWER(c2.identifier) LIKE ? )";
            String likeTerm = "%" + searchTerm.toLowerCase().trim() + "%";
            params.add(likeTerm);
            params.add(likeTerm);
            params.add(likeTerm);
            params.add(likeTerm);
        }
        String paginatedQuery = PaginationUtils.applyPagination(baseQuery, pageable);
        params = PaginationUtils.buildPaginationParams(params, pageable);
        try {
            log.debug("getting paginated and filtered connections.");
            return jdbcTemplate.query(paginatedQuery, new BeanPropertyRowMapper<>(ConnectionDetailsBean.class), params.toArray());
        } catch (EmptyResultDataAccessException e) {
            log.warn("No paginated/filtered connections found for accountId [{}]", accountId);
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("Error occurred while fetching connections for accountId [{}]. Details: ", accountId, e);
            throw new HealControlCenterException("Error in fetching paginated/filtered connections for accountId: " + accountId);
        }
    }

    /**
     * Counts the number of connections for the given account ID, optionally filtered by search term.
     * Adds debug/info logs for query execution and error handling.
     *
     * @param accountId  Account ID for which connections are to be counted
     * @param searchTerm Optional search term to filter by service name or identifier
     * @return Number of matching connections, or 0 if none found or error occurs
     */
    public int countConnectionsByAccountId(Integer accountId, String searchTerm) throws HealControlCenterException {
        String countQuery = "SELECT COUNT(*) FROM connection_details cd, controller c1, controller c2 WHERE c1.id=source_id AND c2.id=destination_id AND cd.account_id = ?";
        List<Object> params = new ArrayList<>();
        params.add(accountId);
        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            countQuery += " AND (LOWER(c1.name) LIKE ? OR LOWER(c1.identifier) LIKE ? OR LOWER(c2.name) LIKE ? OR LOWER(c2.identifier) LIKE ? )";
            String likeTerm = "%" + searchTerm.toLowerCase().trim() + "%";
            params.add(likeTerm);
            params.add(likeTerm);
            params.add(likeTerm);
            params.add(likeTerm);
        }
        try {
            log.debug("[countConnectionsByAccountId] Executing count query for accountId={}, searchTerm={}", accountId, searchTerm);
            Integer count = jdbcTemplate.queryForObject(countQuery, Integer.class, params.toArray());
            log.info("[countConnectionsByAccountId] Found {} connections for accountId={}", count, accountId);
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("[countConnectionsByAccountId] Error occurred while counting connections for accountId={}. Details: ", accountId, e);
            throw new HealControlCenterException("Error in counting connections for accountId: " + accountId);
        }
    }

    /**
     * Checks if a connection exists for the given sourceId, destinationId, and accountId.
     * Returns true if a connection exists, false otherwise.
     *
     * @param srcId     Source controller ID
     * @param destId    Destination controller ID
     * @param accountId Account ID
     * @return true if connection exists, false otherwise
     */
    public boolean connectionExists(int srcId, int destId, int accountId) {
        String query = "select count(*) from connection_details where source_id = ? and destination_id = ? and account_id = ?";
        try {
            log.info("Checking existence of connection for srcId [{}], destId [{}], accountId [{}]", srcId, destId, accountId);
            Integer count = jdbcTemplate.queryForObject(query, Integer.class, srcId, destId, accountId);
            boolean exists = count != null && count > 0;
            log.debug("Connection existence for srcId [{}], destId [{}], accountId [{}]: {}", srcId, destId, accountId, exists);
            return exists;
        } catch (Exception e) {
            log.error("Error occurred while checking connection existence for srcId [{}], destId [{}], accountId [{}]. Details: {}", srcId, destId, accountId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Deletes a connection by sourceId, destinationId, and accountId.
     * Returns number of rows affected: -1 for error, 0 for not found, >0 for success.
     */
    public int deleteConnectionBySrcDest(int srcId, int destId, int accountId) {
        String query = "DELETE FROM connection_details WHERE source_id = ? AND destination_id = ? AND account_id = ?";
        try {
            return jdbcTemplate.update(query, srcId, destId, accountId);
        } catch (Exception e) {
            log.error("Error deleting connection for srcId [{}], destId [{}], accountId [{}]", srcId, destId, accountId, e);
            return -1;
        }
    }
}
