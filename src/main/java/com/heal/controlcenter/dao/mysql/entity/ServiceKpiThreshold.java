package com.heal.controlcenter.dao.mysql.entity;

import lombok.*;

import java.sql.Timestamp;

/**
 * <AUTHOR> sri<PERSON>
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class ServiceKpiThreshold
{
    private int id;
    @EqualsAndHashCode.Include
    private int accountId;
    @EqualsAndHashCode.Include
    private int serviceId;
    @EqualsAndHashCode.Include
    private int kpiId;
    private int operationTypeId;
    private int status;
    private Double minThreshold;
    private Double maxThreshold;
    @EqualsAndHashCode.Include
    private String applicableTo;
    private String userDetailsId;
    private String kpiAttribute;
    private Timestamp createdTime;
    private Timestamp updatedTime ;
    private String definedBy;
    private Timestamp startTime;
    private Timestamp endTime;
    private int thresholdSeverityId;
    private String coverageWindow;
}
