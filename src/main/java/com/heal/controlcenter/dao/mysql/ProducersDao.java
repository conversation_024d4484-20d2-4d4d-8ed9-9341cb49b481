package com.heal.controlcenter.dao.mysql;

import com.heal.controlcenter.pojo.ProducerDetailsPojo;
import com.heal.controlcenter.pojo.ProducerKPIMappingDetailsPojo;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
public class ProducersDao {

    private final JdbcTemplate jdbcTemplate;

    public ProducersDao(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * Retrieves a paginated list of producer details for a given account.
     *
     * @param accountId The ID of the account.
     * @param producerId The ID of the producer (can be null).
     * @param name The name of the producer (can be null).
     * @param status The status of the producer (can be null).
     * @param producerType The type of the producer (can be null).
     * @param pageable Pagination and sorting information.
     * @return A list of ProducerDetailsPojo objects.
     */
    public List<ProducerDetailsPojo> getProducerDetailsWithAccId(int accountId, Integer producerId, String name, String status, String producerType, int isCustom, Pageable pageable) {
        List<Object> params = new ArrayList<>();
        String query = "SELECT p.id, p.name, p.description, pt.type as producer_type_name, p.is_custom, p.status, p.is_kpi_group, p.created_time, p.updated_time, ua.username as user_name " +
                "FROM mst_producer p " +
                "JOIN mst_producer_type pt ON p.mst_producer_type_id = pt.id " +
                "JOIN user_attributes ua ON p.user_details_id = ua.user_identifier " +
                "WHERE p.account_id IN (1, ?) AND p.is_custom = ?";
        params.add(accountId);
        params.add(isCustom);

        if (producerId != null) {
            query += " AND p.id = ?";
            params.add(producerId);
        }
        if (name != null && !name.isEmpty()) {
            query += " AND p.name LIKE ?";
            params.add("%" + name + "%");
        }
        if (status != null && !status.isEmpty()) {
            query += " AND p.status = ?";
            params.add(status);
        }
        if (producerType != null && !producerType.isEmpty()) {
            query += " AND pt.type = ?";
            params.add(producerType);
        }

        query += " LIMIT " + pageable.getOffset() + ", " + pageable.getPageSize();

        return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ProducerDetailsPojo.class), params.toArray());
    }

    /**
     * Retrieves the total count of producers for a given account.
     *
     * @param accountId The ID of the account.
     * @param name The name of the producer (can be null).
     * @param status The status of the producer (can be null).
     * @param producerType The type of the producer (can be null).
     * @return The total number of producers.
     */
    public int getProducersCount(int accountId, String name, String status, String producerType, int isCustom) {
        List<Object> params = new ArrayList<>();
        String query = "SELECT count(*) FROM mst_producer p " +
                "JOIN mst_producer_type pt ON p.mst_producer_type_id = pt.id " +
                "WHERE p.account_id IN (1, ?) AND p.is_custom = ?";
        params.add(accountId);
        params.add(isCustom);

        if (name != null && !name.isEmpty()) {
            query += " AND p.name LIKE ?";
            params.add("%" + name + "%");
        }
        if (status != null && !status.isEmpty()) {
            query += " AND p.status = ?";
            params.add(status);
        }
        if (producerType != null && !producerType.isEmpty()) {
            query += " AND pt.type = ?";
            params.add(producerType);
        }

        return jdbcTemplate.queryForObject(query, Integer.class, params.toArray());
    }

    /**
     * Retrieves a list of producer KPI mapping details for a given account.
     *
     * @param accountId The ID of the account.
     * @param producerId The ID of the producer (can be null).
     * @return A list of ProducerKPIMappingDetailsPojo objects.
     */
    public List<ProducerKPIMappingDetailsPojo> getProducerKPIMappingDetails(int accountId, Integer producerId) {
        String query = "select mpkm.producer_id producerId, mkd.name kpiName, mct.name componentTypeName, mc.name componentName," +
            " mcv.name componentVersionName from (((mst_producer_kpi_mapping mpkm join mst_component mc on mpkm.mst_component_id = mc.id)" +
            " join mst_component_version mcv on mpkm.mst_component_version_id = mcv.id) join mst_component_type mct on" +
            " mpkm.mst_component_type_id = mct.id) join mst_kpi_details mkd on mpkm.mst_kpi_details_id = mkd.id where" +
            " mpkm.account_id in (1, ?)";

        if (producerId != null) {
            query += " and producer_id = " + producerId;
        }

        return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ProducerKPIMappingDetailsPojo.class), accountId);
    }
}