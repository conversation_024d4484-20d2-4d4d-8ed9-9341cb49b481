package com.heal.controlcenter.dao.mysql;

import com.heal.controlcenter.dao.mysql.entity.*;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.pojo.ComponentKpiDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Repository
public class KpiDataDao {

    private final JdbcTemplate jdbcTemplate;

    public KpiDataDao(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * Retrieves a list of KPI details based on provided filters and pagination.
     *
     * @param accountId  the account ID
     * @param pageable   pagination information
     * @param searchTerm search term for KPI name or identifier
     * @param kpiType    KPI type filter
     * @param group      KPI group filter
     * @param category   KPI category filter
     * @param component  component filter
     * @return list of KPI details
     * @throws HealControlCenterException if any error occurs during query execution
     */
    public List<KpiListBean> getKpiList(int accountId, Pageable pageable, String searchTerm, String kpiType, String group, String category, String component) throws HealControlCenterException {
        StringBuilder sql = new StringBuilder("select k.id as id, k.name as name, k.identifier as identifier, k.description as description, k.data_type as dataType, value_type as valueType, cluster_operation as clusterOperation, " +
                "rollup_operation as rollupOperation, cluster_aggregation_type as clusterAggregation, instance_aggregation_type as instanceAggregation, measure_units as kpiUnit, k.kpi_group_id as groupKpiId, " +
                "k.is_custom as standardType, k.cron_expression as cronExpression, k.delta_per_sec as deltaPerSec, k.reset_delta_value as resetDeltaValue, " +
                "COALESCE(pkm.status, 0) as status, " +
                "COALESCE(g.is_custom, 0) as groupKpiStandardType, COALESCE(g.discovery, 0) as groupKpiDiscovery, g.identifier as groupKpiIdentifier, g.description as groupKpiDescription, g.name as groupKpiName, st.name as kpiType, " +
                "COALESCE(pkm.mst_component_id, 0) as componentId, COALESCE(pkm.default_collection_interval, 0) as collectionInterval, COALESCE(pkm.mst_common_version_id, 0) as commonVersionId, COALESCE(pkm.do_analytics, 0) as availableForAnalytics, " +
                "COALESCE(pkm.mst_component_type_id, 0) as componentTypeId " +
                "from mst_kpi_details k left outer join mst_component_version_kpi_mapping pkm on k.id=pkm.mst_kpi_details_id " +
                "left join mst_kpi_group g on k.kpi_group_id = g.id, mst_sub_type st where st.id = k.kpi_type_id and k.account_id in (1, ?)");
        List<Object> params = new ArrayList<>();
        params.add(accountId);
        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            sql.append(" and (LOWER(k.name) like ? or LOWER(k.identifier) like ?)");
            params.add("%" + searchTerm.trim().toLowerCase() + "%");
            params.add("%" + searchTerm.trim().toLowerCase() + "%");
        }
        if (kpiType != null && !kpiType.trim().isEmpty()) {
            sql.append(" and LOWER(st.name) = ?");
            params.add(kpiType.trim().toLowerCase());
        }
        if (group != null && !group.trim().isEmpty()) {
            sql.append(" and LOWER(g.name) = ?");
            params.add(group.trim().toLowerCase());
        }
        if (category != null && !category.trim().isEmpty()) {
            sql.append(" and k.id in (select kcd.kpi_id from view_kpi_category_details kcd where LOWER(kcd.name) = ?)");
            params.add(category.trim().toLowerCase());
        }
        if (component != null && !component.trim().isEmpty()) {
            sql.append(" and pkm.mst_component_id in (select id from mst_component where LOWER(name) = ?)");
            params.add(component.trim().toLowerCase());
        }
        String finalSql = com.heal.controlcenter.util.PaginationUtils.applyPagination(sql.toString(), pageable);
        List<Object> finalParams = com.heal.controlcenter.util.PaginationUtils.buildPaginationParams(params, pageable);
        log.debug("getKpiList SQL: {} | params: {}", finalSql, finalParams);
        try {
            List<KpiListBean> result = jdbcTemplate.query(finalSql, new org.springframework.jdbc.core.BeanPropertyRowMapper<>(KpiListBean.class), finalParams.toArray());
            log.debug("getKpiList result size: {}", result != null ? result.size() : 0);
            return result;
        } catch (Exception ex) {
            log.error("Error in fetching KPI list for accountId: {}. SQL: {}. Exception: {}", accountId, finalSql, ex.getMessage(), ex);
            throw new HealControlCenterException("Error in fetching KPI list: " + ex.getMessage());
        }
    }

    /**
     * Gets the count of KPIs based on provided filters.
     *
     * @param accountId  the account ID
     * @param searchTerm search term for KPI name or identifier
     * @param kpiType    KPI type filter
     * @param group      KPI group filter
     * @param category   KPI category filter
     * @param component  component filter
     * @return count of KPIs
     * @throws HealControlCenterException if any error occurs during query execution
     */
    public long getKpiListCount(int accountId, String searchTerm, String kpiType, String group, String category, String component) throws HealControlCenterException {
        StringBuilder sql = new StringBuilder("select count(distinct k.id) from mst_kpi_details k left outer join mst_component_version_kpi_mapping pkm on k.id=pkm.mst_kpi_details_id " +
                "left join mst_kpi_group g on k.kpi_group_id = g.id, mst_sub_type st where st.id = k.kpi_type_id and k.account_id in (1, ?)");
        List<Object> params = new ArrayList<>();
        params.add(accountId);
        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            sql.append(" and (LOWER(k.name) like ? or LOWER(k.identifier) like ?)");
            params.add("%" + searchTerm.trim().toLowerCase() + "%");
            params.add("%" + searchTerm.trim().toLowerCase() + "%");
        }
        if (kpiType != null && !kpiType.trim().isEmpty()) {
            sql.append(" and LOWER(st.name) = ?");
            params.add(kpiType.trim().toLowerCase());
        }
        if (group != null && !group.trim().isEmpty()) {
            sql.append(" and LOWER(g.name) = ?");
            params.add(group.trim().toLowerCase());
        }
        if (category != null && !category.trim().isEmpty()) {
            sql.append(" and k.id in (select kcd.kpi_id from view_kpi_category_details kcd where LOWER(kcd.name) = ?)");
            params.add(category.trim().toLowerCase());
        }
        if (component != null && !component.trim().isEmpty()) {
            sql.append(" and pkm.mst_component_id in (select id from mst_component where LOWER(name) = ?)");
            params.add(component.trim().toLowerCase());
        }
        log.debug("getKpiListCount SQL: {} | params: {}", sql, params);
        try {
            long count = jdbcTemplate.queryForObject(sql.toString(), Long.class, params.toArray());
            log.debug("getKpiListCount result: {}", count);
            return count;
        } catch (Exception ex) {
            log.error("Error in fetching KPI count for accountId: {}. SQL: {}. Exception: {}", accountId, sql, ex.getMessage(), ex);
            throw new HealControlCenterException("Error in fetching KPI count: " + ex.getMessage());
        }
    }

    /**
     * Retrieves category details for the given list of KPI IDs.
     *
     * @param kpiIds list of KPI IDs
     * @return list of KPI category mappings
     * @throws HealControlCenterException if any error occurs during query execution
     */
    public List<KpiCategoryMapping> getCategoryDetailsForKpis(List<Integer> kpiIds) throws HealControlCenterException {
        String inSql = String.join(",", java.util.Collections.nCopies(kpiIds.size(), "?"));
        String sql = "select kpi_id as kpiId, category_id as categoryId, category_identifier as categoryIdentifier, name as categoryName, is_workload as workLoad " +
                "from view_kpi_category_details where kpi_id in (" + inSql + ")";
        log.debug("getCategoryDetailsForKpis SQL: {} | kpiIds: {}", sql, kpiIds);
        try {
            List<KpiCategoryMapping> result = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(KpiCategoryMapping.class), kpiIds.toArray());
            log.debug("getCategoryDetailsForKpis result size: {}", result != null ? result.size() : 0);
            return result;
        } catch (Exception ex) {
            log.error("Error in fetching KPI category details for kpiIds: {}", kpiIds, ex);
            throw new HealControlCenterException("Error in fetching KPI category details");
        }
    }

    /**
     * Retrieves component details for the given account ID.
     *
     * @param accountId the account ID
     * @return list of component details
     * @throws HealControlCenterException if any error occurs during query execution
     */
    public List<ComponentBean> getComponentDetailsForAccount(int accountId) throws HealControlCenterException {
        String sql = "select mcv.mst_component_id as componentId, mc.id as commonVersionId, mc.name as commonVersionName, mcv.id as componentVersionId, " +
                "mcv.name as componentVersionName from mst_common_version mc, mst_component_version mcv where mcv.mst_common_version_id=mc.id " +
                "and mc.account_id=mcv.account_id and mcv.account_id in (1, ?)";
        log.debug("getComponentDetailsForAccount SQL: {} | accountId: {}", sql, accountId);
        try {
            List<ComponentBean> result = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(ComponentBean.class), accountId);
            log.debug("getComponentDetailsForAccount result size: {}", result != null ? result.size() : 0);
            return result;
        } catch (Exception ex) {
            log.error("Error in fetching component details for accountId: {}", accountId, ex);
            throw new HealControlCenterException("Error in fetching component details");
        }
    }

    /**
     * Retrieves computed KPI details for the given account ID.
     *
     * @param accountId the account ID
     * @return list of computed KPI details
     * @throws HealControlCenterException if any error occurs during query execution
     */
    public List<ComputedKpiBean> getComputedKpiDetails(int accountId) throws HealControlCenterException {
        String sql = "select mst_kpi_details_id as computedKpiId, formula, display_formula as displayFormula, base_kpi_id as kpiDetailsId " +
                "from mst_computed_kpi_details ckd, mst_computed_kpi_mappings ckm, mst_kpi_details mkd where " +
                "ckm.mst_computed_kpi_details_id=ckd.id and ckm.base_kpi_id=mkd.id and ckd.account_id in (1, ?)";
        log.debug("getComputedKpiDetails SQL: {} | accountId: {}", sql, accountId);
        try {
            List<ComputedKpiBean> result = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(ComputedKpiBean.class), accountId);
            log.debug("getComputedKpiDetails result size: {}", result != null ? result.size() : 0);
            return result;
        } catch (Exception ex) {
            log.error("Error in fetching computed KPI details for accountId: {}", accountId, ex);
            throw new HealControlCenterException("Error in fetching computed KPI details");
        }
    }

    /**
     * Retrieves active instance count for components for the given account ID.
     *
     * @param accountId the account ID
     * @return list of count beans for active instances
     * @throws HealControlCenterException if any error occurs during query execution
     */
    public List<CountBean> getActiveInstanceCountForComponents(int accountId) throws HealControlCenterException {
        String sql = "select count(*) as count, mst_component_id as id from comp_instance where status = 1 and account_id in (1, ?) group by mst_component_id";
        log.debug("getActiveInstanceCountForComponents SQL: {} | accountId: {}", sql, accountId);
        try {
            List<CountBean> result = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(CountBean.class), accountId);
            log.debug("getActiveInstanceCountForComponents result size: {}", result != null ? result.size() : 0);
            return result;
        } catch (Exception ex) {
            log.error("Error in fetching active instance count for components for accountId: {}", accountId, ex);
            throw new HealControlCenterException("Error in fetching active instance count for components");
        }
    }

    /**
     * Retrieves the count of KPIs mapped to computed KPIs for the given account ID.
     *
     * @param accountId the account ID
     * @return list of count beans for KPIs mapped to computed KPIs
     * @throws HealControlCenterException if any error occurs during query execution
     */
    public List<CountBean> getKPIsMappedToComputedKpi(int accountId) throws HealControlCenterException {
        String sql = "SELECT count(*) as count, base_kpi_id as id FROM mst_computed_kpi_mappings where account_id in (1, ?) group by base_kpi_id";
        log.debug("getKPIsMappedToComputedKpi SQL: {} | accountId: {}", sql, accountId);
        try {
            List<CountBean> result = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(CountBean.class), accountId);
            log.debug("getKPIsMappedToComputedKpi result size: {}", result != null ? result.size() : 0);
            return result;
        } catch (Exception ex) {
            log.error("Error in fetching KPIs mapped to computed KPI for accountId: {}", accountId, ex);
            throw new HealControlCenterException("Error in fetching KPIs mapped to computed KPI");
        }
    }

    /**
     * Retrieves component details for the given component ID.
     *
     * @param componentId the component ID
     * @return component KPI detail
     * @throws HealControlCenterException if any error occurs during query execution
     */
    public ComponentKpiDetail getComponentDetails(int componentId) throws HealControlCenterException {
        String sql = "select name, status from mst_component where id = ?";
        log.debug("getComponentDetails SQL: {} | componentId: {}", sql, componentId);
        try {
            ComponentKpiDetail result = jdbcTemplate.queryForObject(sql, new BeanPropertyRowMapper<>(ComponentKpiDetail.class), componentId);
            log.debug("getComponentDetails result: {}", result);
            return result;
        } catch (Exception ex) {
            log.error("Error in fetching component details for componentId: {}", componentId, ex);
            throw new HealControlCenterException("Error in fetching component details");
        }
    }
}
