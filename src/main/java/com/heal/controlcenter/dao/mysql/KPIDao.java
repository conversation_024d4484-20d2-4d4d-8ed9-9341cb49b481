package com.heal.controlcenter.dao.mysql;

import com.heal.configuration.entities.KpiCategoryDetailsBean;
import com.heal.configuration.pojos.KpiCategoryDetails;
import com.heal.controlcenter.dao.mysql.entity.KpiBean;
import com.heal.controlcenter.dao.mysql.entity.KpiDetailsBean;
import com.heal.controlcenter.exception.HealControlCenterException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

@Slf4j
@Repository
public class KPIDao {

    private final JdbcTemplate jdbcTemplate;

    public KPIDao(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public Integer getKpiCountForAccount(int accountId) throws HealControlCenterException {
        try {
            String query = "select count(1) from mst_kpi_details where account_id in (1, " + accountId + ")";
            return jdbcTemplate.queryForObject(query, Integer.class);
        } catch (Exception e) {
            log.error("Exception while getting KPI count for accountId [{}]. Details: ", accountId, e);
            throw new HealControlCenterException("Error occurred while getting KPI count.");
        }
    }

    public List<KpiDetailsBean> getAllKpiDetailsKpiList() throws HealControlCenterException {
        String query = "SELECT distinct kvm.mst_kpi_details_id id, mk.name name,kvm.mst_component_id componentId,mk.kpi_type_id typeId," +
                "mk.cluster_operation clusterOperation, mk.measure_units measureUnits,mk.data_type dataType, formula computedFormula, kvm.mst_common_version_id commonVersionId " +
                "FROM mst_component_version_kpi_mapping kvm " +
                "LEFT JOIN mst_kpi_details mk ON kvm.mst_kpi_details_id = mk.id " +
                "LEFT JOIN mst_computed_kpi_details mckd ON mckd.mst_kpi_details_id=mk.id " +
                "WHERE kvm.status = 1";
        try {
            log.debug("getting kpi list.");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(KpiDetailsBean.class));
        } catch (Exception e) {
            log.error("Error occurred while fetching all kpi details list ", e);
            throw new HealControlCenterException("Error occurred while fetching all kpi details list.");
        }
    }

    public KpiCategoryDetails getKpiCategoryDetails(int kpiId) throws HealControlCenterException {
        String query = "select category_id id, name, category_identifier categoryId, if(is_workload=1,true,false) isWorkLoad " +
                "from view_kpi_category_details where kpi_id = ?";
        try {
            log.debug("getting computed expression.");
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(KpiCategoryDetails.class), kpiId);
        } catch (Exception e) {
            log.error("Error occurred while fetching ComputerExpression [{}], Stack trace: ", kpiId, e);
            throw new HealControlCenterException("Error occurred while fetching KPI category details.");
        }
    }

    public List<KpiCategoryDetailsBean> getAllKpiCategoryDetails() throws HealControlCenterException {
        String query = "select kpi_id, category_id as id, name, category_identifier as identifier, if(is_workload=1,true,false) isWorkLoad, " +
                "kpi_type_id from view_kpi_category_details";
        try {
            log.trace("Fetching kpi category details");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(KpiCategoryDetailsBean.class));
        } catch (Exception e) {
            log.error("Error occurred while fetching kpi category details", e);
            throw new HealControlCenterException("Error occurred while fetching all KPI category details.");
        }
    }

    public KpiBean fetchKpiUsingKpiId(int id, int accountId) {
        String query = "SELECT distinct mk.id id, mk.name name, mk.kpi_type_id kpiTypeId, mk.cluster_operation clusterOperation, " +
                "mk.measure_units measureUnits, mk.data_type dataType, mk.status status, mk.kpi_group_id groupKpiId, " +
                "mk.value_type valueType, mk.rollup_operation rollupOperation, mk.cluster_aggregation_type clusterAggregation, " +
                "mk.instance_aggregation_type instanceAggregation, mk.is_computed isComputed, mk.is_custom isCustom, " +
                "mk.description description, kvm.mst_component_id componentId, kvm.mst_component_type_id componentTypeId, " +
                "kvm.mst_common_version_id commonVersionId, kvm.default_collection_interval collectionIntervalSeconds, " +
                "kvm.do_analytics availableForAnalytics " +
                "FROM mst_component_version_kpi_mapping kvm " +
                "JOIN mst_kpi_details mk ON kvm.mst_kpi_details_id = mk.id " +
                "WHERE mk.id = ? AND mk.account_id in (1, ?) ";
        try {
            List<KpiBean> kpis = jdbcTemplate.query(query, new BeanPropertyRowMapper<>(KpiBean.class), id, accountId);
            if(kpis == null || kpis.isEmpty()) {
                log.warn("No KPI found for id [{}] and accountId [{}].", id, accountId);
                return null;
            }
            return kpis.get(0);
        } catch (Exception e) {
            log.error("Error occurred while fetching KPI details for id [{}] and accountId [{}]. Details: {}", id, accountId, e.getMessage());
            return null;
        }
    }

    public int getGroupKpiDiscovery(int groupKpiId) {
        String query = "SELECT discovery FROM mst_kpi_group WHERE id = ?";
        try {
            return jdbcTemplate.queryForObject(query, Integer.class, groupKpiId);
        } catch (Exception e) {
            log.error("Error occurred while fetching discovery for groupKpiId [{}]. Details: {}", groupKpiId, e.getMessage());
            return 0;
        }
    }
}
