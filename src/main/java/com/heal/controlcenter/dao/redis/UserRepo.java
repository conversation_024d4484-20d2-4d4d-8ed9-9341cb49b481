package com.heal.controlcenter.dao.redis;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.entities.UserAccessDetails;
import com.heal.configuration.pojos.User;
import com.heal.controlcenter.util.HealthMetrics;
import com.heal.controlcenter.util.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Slf4j
public class UserRepo {

    @Autowired
    ObjectMapper objectMapper;
    @Autowired
    RedisUtilities redisUtilities;
    @Autowired
    HealthMetrics healthMetrics;

    public List<UserAccessDetails> getUserAccessDetails(String userIdentifier) {
        String key = "/users/" + userIdentifier + "/accessDetails";
        String hashKey = "USERS_" + userIdentifier + "_ACCESSDETAILS";
        try {
            String userAccessDetails = redisUtilities.getKey(key, hashKey);
            if (userAccessDetails == null || userAccessDetails.trim().isEmpty()) {
                log.warn("Null/Empty value found for key {} in redis.", key);
                return null;
            }

            return objectMapper.readValue(userAccessDetails, new TypeReference<List<UserAccessDetails>>() {
            });

        } catch (Exception e) {
            log.error("Exception encountered while getting user details from key:{}, hashKey:{}", key, hashKey, e);
            return null;
        }
    }

    /**
     * Retrieves user details from Redis for the given user identifier.
     *
     * @param userIdentifier the unique identifier of the user
     * @return User object if found, otherwise null
     */
    public User getUser(String userIdentifier) {
        String key = "/users/" + userIdentifier;
        String hashKey = "USERS_" + userIdentifier;
        try {
            String userDetails = redisUtilities.getKey(key, hashKey);
            if (userDetails == null || userDetails.trim().isEmpty()) {
                log.warn("Null/Empty value found for key {} in redis.", key);
                return null;
            }

            return objectMapper.readValue(userDetails, new TypeReference<>() {
            });

        } catch (Exception e) {
            healthMetrics.updateHealApiServiceErrors();
            log.error("Exception encountered while getting user details from key:{}, hashKey:{}", key, hashKey, e);
            return null;
        }
    }

    /**
     * Updates the user object in Redis with the provided last login time.
     *
     * @param user the User object to update
     * @param timeInGMT the last login time in GMT format
     */
    public void updateInRedis(User user, String timeInGMT) {

        user.setLastLoginTime(timeInGMT);

        String userIdentifier = user.getUserDetailsId();
        String key = "/users/" + userIdentifier;
        String hashKey = "USERS_" + userIdentifier;

        redisUtilities.updateKey(key, hashKey, user);
    }
}
