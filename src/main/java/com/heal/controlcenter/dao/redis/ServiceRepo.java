package com.heal.controlcenter.dao.redis;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.KpiDetails;

import com.heal.configuration.pojos.Rule;
import com.heal.configuration.pojos.Service;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.util.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@Slf4j
public class ServiceRepo {

    String RULES_KEY = "/rules";
    String RULES_HASH = "_RULES";
    String KPI_KEY = "/kpis";
    String KPI_HASH = "_KPIS";
    String ACCOUNTS_KEY = "/accounts";
    String ACCOUNTS_HASH = "ACCOUNTS";
    String SERVICES_KEY = "/services";
    String SERVICES_HASH = "_SERVICES";
    String APPLICATION_KEY = "/applications";
    String APPLICATION_HASH = "_APPLICATIONS";

    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private RedisUtilities redisUtilities;

    /**
     * Retrieves all service details for a given account from Redis.
     *
     * @param accountIdentifier The unique identifier of the account.
     * @return A list of {@link BasicEntity} objects representing all services for the account, or an empty list if none found.
     */
    public List<BasicEntity> getAllServicesDetails(String accountIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/services";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES";
        try {
            String serviceDetails = redisUtilities.getKey(key, hashKey);
            if (serviceDetails == null || serviceDetails.trim().isEmpty()) {
                log.warn("Null/Empty value found for key {} in redis.", key);
                return Collections.emptyList();
            }
            return objectMapper.readValue(serviceDetails, new TypeReference<List<BasicEntity>>() {
            });
        } catch (Exception e) {
            log.error("Exception encountered while getting key '{}': {}", key, hashKey, e);
            return Collections.emptyList();
        }
    }

    /**
     * Retrieves the full service configuration for a specific service identifier from Redis.
     *
     * @param accountIdentifier The unique identifier of the account.
     * @param serviceIdentifier The unique identifier of the service.
     * @return A {@link Service} object containing the service configuration, or null if not found.
     */
    public Service getServiceConfigurationByIdentifier(String accountIdentifier, String serviceIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier;
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier;
        try {
            String serviceDetails = redisUtilities.getKey(key, hashKey);
            if (serviceDetails == null || serviceDetails.trim().isEmpty()) {
                log.warn("Null/Empty value found for key {} in redis.", key);
                return null;
            }
            return objectMapper.readValue(serviceDetails, new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Exception encountered while getting key '{}': {}", key, hashKey, e);
            return null;
        }
    }

    /**
     * Updates the service configuration for a specific service identifier in Redis.
     *
     * @param accountIdentifier  The unique identifier of the account.
     * @param serviceIdentifier  The unique identifier of the service.
     * @param serviceConfiguration The {@link Service} object containing the updated configuration.
     */
    public void updateServiceConfigurationByServiceIdentifier(String accountIdentifier, String serviceIdentifier, Service serviceConfiguration) {
        String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier;
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier;
        try {
            redisUtilities.updateKey(key, hashKey,serviceConfiguration);
        } catch (Exception e) {
            log.error("Exception encountered while getting key '{}': {}", key, hashKey, e);
        }
    }

    /**
     * Retrieves KPI details for a specific service and KPI ID from Redis.
     *
     * @param accountIdentifier The unique identifier of the account.
     * @param serviceIdentifier The unique identifier of the service.
     * @param kpiId             The ID of the KPI.
     * @return A {@link KpiDetails} object, or null if not found.
     */
    public KpiDetails getServiceKPI(String accountIdentifier, String serviceIdentifier, int kpiId) {

        String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/kpis/" + kpiId;
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_KPIS_" + kpiId;
        try {
            String serviceKpi = redisUtilities.getKey(key, hashKey);
            if (serviceKpi == null || serviceKpi.trim().isEmpty()) {
                log.warn("Null/Empty value found for key {} in redis.", key);
                return null;
            }
            return objectMapper.readValue(serviceKpi, new TypeReference<KpiDetails>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting service KPI for service [{}] and KPI id [{}] ", serviceIdentifier, kpiId);
            return null;
        }
    }

    /**
     * Updates KPI details for a specific service and KPI ID in Redis.
     *
     * @param accountIdentifier The unique identifier of the account.
     * @param serviceIdentifier The unique identifier of the service.
     * @param kpiId             The ID of the KPI.
     * @param serviceKPI        The {@link KpiDetails} object with updated KPI information.
     */
    public void updateServiceKpiById(String accountIdentifier, String serviceIdentifier, int kpiId, KpiDetails serviceKPI) {
        String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/kpis/" + kpiId;
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_KPIS_" + kpiId;
        try {
            redisUtilities.updateKey(key, hashKey,serviceKPI);
        } catch (Exception e) {
            log.error("Error occurred while updating service Kpi for service: [{}] and kpiId: [{}]", serviceIdentifier, kpiId, e);
        }
    }

    /**
     * Updates KPI details for a specific service and KPI identifier in Redis.
     *
     * @param accountIdentifier The unique identifier of the account.
     * @param serviceIdentifier The unique identifier of the service.
     * @param kpiIdentifier     The identifier of the KPI.
     * @param serviceKPI        The {@link KpiDetails} object with updated KPI information.
     */
    public void updateServiceKpiByIdentifier(String accountIdentifier, String serviceIdentifier, String kpiIdentifier, KpiDetails serviceKPI) {
        String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/kpis/" + kpiIdentifier;
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_KPIS_" + kpiIdentifier;
        try {
            redisUtilities.updateKey(key, hashKey,serviceKPI);
        } catch (Exception e) {
            log.error("Error occurred while updating service Kpi for service: [{}] and kpiIdentifier: [{}]", serviceIdentifier, kpiIdentifier, e);
        }
    }

    /**
     * Updates the list of applications associated with a service in Redis.
     *
     * @param accountIdentifier The unique identifier of the account.
     * @param serviceIdentifier The unique identifier of the service.
     * @param applications      A list of {@link BasicEntity} objects representing the applications.
     */
    public void updateApplicationsByServiceIdentifier(String accountIdentifier, String serviceIdentifier, List<BasicEntity> applications) {
        String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/applications";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_APPLICATIONS";

        try {
            redisUtilities.updateKey(key, hashKey, applications);
        } catch (Exception e) {
            log.error("Error occurred while updating application details for service: [{}]", serviceIdentifier, e);
        }
    }

    /**
     * Updates the overall service configuration list for an account in Redis.
     *
     * @param accountIdentifier The unique identifier of the account.
     * @param basicEntities     A list of {@link BasicEntity} objects representing basic service details.
     */
    public void updateServiceConfiguration(String accountIdentifier, List<BasicEntity> basicEntities) {
        String key = "/accounts/" + accountIdentifier + "/services";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES";
        try {
            redisUtilities.updateKey(key, hashKey, basicEntities);
        } catch (Exception e) {
            log.error("Error occurred while updating service configurations for accountIdentifier: [{}]", accountIdentifier, e);
        }
    }

    /**
     * Updates the rules associated with a service in Redis.
     *
     * @param accountIdentifier The unique identifier of the account.
     * @param serviceIdentifier The unique identifier of the service.
     * @param rules             A list of {@link Rule} objects representing the service rules.
     */
    public void updateServiceRules(String accountIdentifier, String serviceIdentifier, List<Rule> rules) {
        String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/rules";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_RULES";
        try {
            redisUtilities.updateKey(key, hashKey, rules);
        } catch (Exception e) {
            log.error("Error occurred while updating Rules for service: [{}]", serviceIdentifier, e);
        }
    }

    /**
     * Deletes a service's configuration and rules from Redis.
     * Throws a RuntimeException if any error occurs during deletion.
     *
     * @param accountIdentifier The identifier of the account.
     * @param serviceIdentifier The identifier of the service to delete.
     */
    public void deleteService(String accountIdentifier, String serviceIdentifier) throws DataProcessingException {
        String serviceConfigKey = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier;
        String serviceConfigHashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier;

        String serviceRulesKey = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/rules";
        String serviceRulesHashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_RULES";

        try {
            redisUtilities.deleteKey(serviceConfigKey, serviceConfigHashKey);
            log.info("Deleted service configuration from Redis for service: [{}]", serviceIdentifier);

            redisUtilities.deleteKey(serviceRulesKey, serviceRulesHashKey);
            log.info("Deleted service rules from Redis for service: [{}]", serviceIdentifier);
        } catch (Exception e) {
            log.error("Error deleting service from Redis for service: [{}]", serviceIdentifier, e);
            throw new DataProcessingException("Error deleting service from Redis for service: " + serviceIdentifier);
        }
    }
}
