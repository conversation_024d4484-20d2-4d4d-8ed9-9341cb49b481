package com.heal.controlcenter.dao.redis;

import com.heal.configuration.entities.ConnectionBean;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.ControllerBean;
import com.heal.controlcenter.dao.mysql.TagsDao;
import com.heal.controlcenter.pojo.TagMappingDetails;
import com.heal.controlcenter.util.DateTimeUtil;
import com.heal.controlcenter.util.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Repository
public class ConnectionRepo {
    @Autowired
    private RedisUtilities redisUtilities;
    @Autowired
    private TagsDao tagsDao;

    /**
     * Updates the Redis cache with the service-to-service connections for each service in the account.
     * Each service's connections are grouped and stored under its own Redis key.
     *
     * @param accountIdentifier The account identifier.
     * @param connectionsList   List of all connection beans for the account.
     * @param servicesList      List of all services for the account.
     */
    public void updateServiceConnections(String accountIdentifier, List<ConnectionBean> connectionsList, List<ControllerBean> servicesList) {
        log.info("[updateServiceConnections] Initiating update for accountIdentifier: {}", accountIdentifier);
        try {
            Map<Integer, List<BasicEntity>> serviceConnectionsMapping = connectionsList.stream()
                    .collect(Collectors.groupingBy(ConnectionBean::getSourceId,
                            Collectors.mapping(connectionBean -> BasicEntity.builder()
                                    .id(connectionBean.getDestinationId())
                                    .name(connectionBean.getDestinationName())
                                    .identifier(connectionBean.getDestinationIdentifier())
                                    .lastModifiedBy(connectionBean.getLastModifiedBy())
                                    .createdTime(connectionBean.getCreatedTime())
                                    .updatedTime(connectionBean.getUpdatedTime())
                                    .build(), Collectors.toList())));
            for (ControllerBean s : servicesList) {
                String key = "/accounts/" + accountIdentifier + "/services/" + s.getIdentifier() + "/connections";
                String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + s.getIdentifier() + "_CONNECTIONS";
                List<BasicEntity> connections = serviceConnectionsMapping.getOrDefault(s.getId(), new ArrayList<>());
                log.debug("[updateServiceConnections] Updating Redis key: {} with {} connections for serviceId: {}", key, connections.size(), s.getId());
                redisUtilities.updateKey(key, hashKey, connections);
            }
            log.info("[updateServiceConnections] Successfully updated service connections for accountIdentifier: {}", accountIdentifier);
        } catch (Exception e) {
            log.error("[updateServiceConnections] Exception while updating service connections for account [{}]: {}", accountIdentifier, e.getMessage(), e);
        }
    }

    /**
     * Updates the Redis cache with neighbours for each entry point service in the account.
     * Neighbours are determined based on service connections and entry point tags.
     *
     * @param accountBean     The account entity.
     * @param connectionsList List of all connection beans for the account.
     * @param servicesList    List of all services for the account.
     */
    public void updateNeighbours(AccountBean accountBean, List<ConnectionBean> connectionsList, List<ControllerBean> servicesList) {
        log.info("[updateNeighbours] Initiating neighbour update for accountIdentifier: {}", accountBean.getIdentifier());
        String ACCOUNTS_KEY = "/accounts";
        String SERVICES_KEY = "/services";
        String NEIGHBOURS_KEY = "/neighbours";
        String ACCOUNTS_HASH = "ACCOUNTS";
        String SERVICES_HASH = "_SERVICES";
        String NEIGHBOURS_HASH = "_NEIGHBOURS";
        int entryPointTagId = 7;
        String objectRefTable = "controller";
        String tagKey = "Type";
        try {
            List<BasicEntity> entryPointServices = servicesList.stream().map(s -> {
                List<TagMappingDetails> tags = tagsDao.getTagKeyId(entryPointTagId, s.getId(), objectRefTable, tagKey, accountBean.getId());
                if (tags == null || tags.isEmpty()) {
                    return null;
                }
                return BasicEntity.builder()
                        .id(s.getId())
                        .name(s.getName())
                        .identifier(s.getIdentifier())
                        .createdTime(Objects.requireNonNull(DateTimeUtil.getCurrentTimestampInGMT()).toString())
                        .updatedTime(DateTimeUtil.getCurrentTimestampInGMT().toString())
                        .lastModifiedBy(s.getLastModifiedBy())
                        .build();
            }).filter(Objects::nonNull).collect(Collectors.toList());

            Map<String, Set<BasicEntity>> neighbours = new HashMap<>();
            Map<String, List<BasicEntity>> outboundConnections = connectionsList.stream()
                    .collect(Collectors.groupingBy(ConnectionBean::getSourceIdentifier,
                            Collectors.mapping(b -> BasicEntity.builder()
                                    .id(b.getDestinationId())
                                    .name(b.getDestinationName())
                                    .identifier(b.getDestinationIdentifier())
                                    .status(1)
                                    .createdTime(b.getCreatedTime())
                                    .updatedTime(b.getUpdatedTime())
                                    .lastModifiedBy(b.getLastModifiedBy())
                                    .build(), Collectors.toList())));

            for (BasicEntity service : entryPointServices) {
                String serviceId = service.getIdentifier();
                Set<BasicEntity> connectedServices = neighbours.getOrDefault(serviceId, new HashSet<>());
                connectedServices.add(BasicEntity.builder()
                        .id(service.getId())
                        .name(service.getName())
                        .identifier(service.getIdentifier())
                        .build());
                addPath(serviceId, outboundConnections, connectedServices);
                neighbours.put(serviceId, connectedServices);
                String key = ACCOUNTS_KEY + "/" + accountBean.getIdentifier() + SERVICES_KEY + "/" + service.getIdentifier() + NEIGHBOURS_KEY;
                String hashKey = ACCOUNTS_HASH + "_" + accountBean.getIdentifier() + SERVICES_HASH + "_" + service.getIdentifier() + NEIGHBOURS_HASH;
                log.debug("[updateNeighbours] Updating key: {} with {} neighbours", key, connectedServices.size());
                redisUtilities.updateKey(key, hashKey, connectedServices);
            }
            log.info("[updateNeighbours] Successfully updated neighbours for accountIdentifier: {}", accountBean.getIdentifier());
        } catch (Exception e) {
            log.error("[updateNeighbours] Exception while updating neighbours for account [{}]: {}", accountBean.getIdentifier(), e.getMessage(), e);
        }
    }

    /**
     * Recursively adds all reachable nodes from the current node to the visited set.
     * Used for neighbour calculation in service graphs.
     *
     * @param currentNode The identifier of the current node.
     * @param nodes       Map of node identifiers to their outbound connections.
     * @param visited     Set of already visited BasicEntity nodes.
     */
    private void addPath(String currentNode, Map<String, List<BasicEntity>> nodes, Set<BasicEntity> visited) {
        log.trace("[addPath] Traversing from node: {}", currentNode);
        List<BasicEntity> next = nodes.get(currentNode);
        if (next == null) {
            log.trace("[addPath] No outbound connections for node: {}", currentNode);
            return;
        }
        for (BasicEntity node : next) {
            if (!visited.contains(node)) {
                visited.add(node);
                addPath(node.getIdentifier(), nodes, visited);
            }
        }
    }

    /**
     * Updates the Redis cache with outbound connections for all services in the account.
     *
     * @param accountIdentifier The account identifier.
     * @param connectionsList   List of all connection beans for the account.
     */
    public void updateOutbounds(String accountIdentifier, List<ConnectionBean> connectionsList) {
        log.info("[updateOutbounds] Initiating outbound connections update for accountIdentifier: {}", accountIdentifier);
        String ACCOUNTS_KEY = "/accounts";
        String OUTBOUND_CONNECTIONS_KEY = "/outbounds";
        String ACCOUNTS_HASH = "ACCOUNTS";
        String OUTBOUND_CONNECTIONS_HASH = "_OUTBOUNDS";
        try {
            Map<Integer, List<BasicEntity>> outboundConnectionsMap = connectionsList.stream()
                    .collect(Collectors.groupingBy(ConnectionBean::getSourceId,
                            Collectors.mapping(b -> BasicEntity.builder()
                                    .id(b.getDestinationId())
                                    .name(b.getDestinationName())
                                    .identifier(b.getDestinationIdentifier())
                                    .status(1)
                                    .createdTime(b.getCreatedTime())
                                    .updatedTime(b.getUpdatedTime())
                                    .lastModifiedBy(b.getLastModifiedBy())
                                    .build(), Collectors.toList())));
            String key = ACCOUNTS_KEY + "/" + accountIdentifier + OUTBOUND_CONNECTIONS_KEY;
            String hashKey = ACCOUNTS_HASH + "_" + accountIdentifier + OUTBOUND_CONNECTIONS_HASH;
            log.debug("[updateOutbounds] Updating key: {} with {} outbound connections", key, outboundConnectionsMap.size());
            redisUtilities.updateKey(key, hashKey, outboundConnectionsMap);
            log.info("[updateOutbounds] Successfully updated outbound connections for accountIdentifier: {}", accountIdentifier);
        } catch (Exception e) {
            log.error("[updateOutbounds] Exception while updating outbound connections for account [{}]: {}", accountIdentifier, e.getMessage(), e);
        }
    }

    /**
     * Updates the Redis cache with inbound connections for all services in the account.
     *
     * @param accountIdentifier The account identifier.
     * @param connectionsList   List of all connection beans for the account.
     */
    public void updateInbounds(String accountIdentifier, List<ConnectionBean> connectionsList) {
        log.info("[updateInbounds] Initiating inbound connections update for accountIdentifier: {}", accountIdentifier);
        String ACCOUNTS_KEY = "/accounts";
        String INBOUND_CONNECTIONS_KEY = "/inbounds";
        String ACCOUNTS_HASH = "ACCOUNTS";
        String INBOUND_CONNECTIONS_HASH = "_INBOUNDS";
        try {
            Map<Integer, List<BasicEntity>> inboundConnectionsMap = connectionsList.stream()
                    .collect(Collectors.groupingBy(ConnectionBean::getDestinationId,
                            Collectors.mapping(b -> BasicEntity.builder()
                                    .id(b.getSourceId())
                                    .name(b.getSourceName())
                                    .identifier(b.getSourceIdentifier())
                                    .status(1)
                                    .createdTime(b.getCreatedTime())
                                    .updatedTime(b.getUpdatedTime())
                                    .lastModifiedBy(b.getLastModifiedBy())
                                    .build(), Collectors.toList())));
            String key = ACCOUNTS_KEY + "/" + accountIdentifier + INBOUND_CONNECTIONS_KEY;
            String hashKey = ACCOUNTS_HASH + "_" + accountIdentifier + INBOUND_CONNECTIONS_HASH;
            log.debug("[updateInbounds] Updating key: {} with {} inbound connections", key, inboundConnectionsMap.size());
            redisUtilities.updateKey(key, hashKey, inboundConnectionsMap);
            log.info("[updateInbounds] Successfully updated inbound connections for accountIdentifier: {}", accountIdentifier);
        } catch (Exception e) {
            log.error("[updateInbounds] Exception while updating inbound connections for account [{}]: {}", accountIdentifier, e.getMessage(), e);
        }
    }
}
