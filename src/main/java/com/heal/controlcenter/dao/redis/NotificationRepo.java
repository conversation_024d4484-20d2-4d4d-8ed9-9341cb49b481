package com.heal.controlcenter.dao.redis;

import com.heal.configuration.pojos.EscalationSettings;
import com.heal.controlcenter.util.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Slf4j
public class NotificationRepo {

    @Autowired
    RedisUtilities redisUtilities;

    /**
     * Save or update notification settings into Redis for the given accountIdentifier.
     */
    public void updateNotificationSettingsInRedis(String accountIdentifier, List<EscalationSettings> escalationSettings) {
        try {
            String key = "/accounts/" + accountIdentifier + "/signal/escalations";
            String hashKey = "ACCOUNTS_" + accountIdentifier + "_SIGNAL_ESCALATIONS";

            redisUtilities.updateKey(key, hashKey, escalationSettings);

            log.debug("Notification settings cached in Redis for account [{}]", accountIdentifier);
        } catch (Exception e) {
            log.error("Failed to update Redis cache for notification settings. Account [{}]. Details:", accountIdentifier, e);
        }
    }
}