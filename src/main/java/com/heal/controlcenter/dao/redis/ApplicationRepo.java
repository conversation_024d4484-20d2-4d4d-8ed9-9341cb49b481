package com.heal.controlcenter.dao.redis;

import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.Application;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Repository
@Slf4j
public class ApplicationRepo {

    private static final String ACCOUNTS_KEY = "/accounts";
    private static final String ACCOUNTS_HASH = "ACCOUNTS";
    private static final String SERVICES_KEY = "/services";
    private static final String SERVICES_HASH = "_SERVICES";
    private static final String APPLICATION_KEY = "/applications";
    private static final String APPLICATION_HASH = "_APPLICATIONS";

    @Autowired
    private RedisUtilities redisUtilities;

    /**
     * Retrieves the list of services associated with a specific application
     * from Redis for the given account and application identifiers.
     *
     * @param accountIdentifier     Account identifier
     * @param applicationIdentifier Application identifier
     * @return List of BasicEntity representing mapped services; empty if none found
     * @throws HealControlCenterException if Redis interaction fails
     */
    public List<BasicEntity> getServicesMappedToApplication(String accountIdentifier, String applicationIdentifier) {
        String key = ACCOUNTS_KEY + "/" + accountIdentifier + APPLICATION_KEY + "/" + applicationIdentifier + SERVICES_KEY;
        String hashKey = ACCOUNTS_HASH + "_" + accountIdentifier + APPLICATION_HASH + "_" + applicationIdentifier + SERVICES_HASH;

        try {
            String serviceApplicationDetails = redisUtilities.getKey(key, hashKey);
            if (serviceApplicationDetails == null) {
                log.debug("No services mapped to application [{}] for account [{}]", applicationIdentifier, accountIdentifier);
                return new ArrayList<>();
            }

            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(
                    serviceApplicationDetails, new TypeReference<List<BasicEntity>>() {
                    }
            );
        } catch (Exception e) {
            log.error("Error while fetching services for application [{}], account [{}]", applicationIdentifier, accountIdentifier, e);
            return new ArrayList<>();
        }
    }
    /**
     * Updates Redis with the given list of services for a specific application
     * under a specific account.
     *
     * @param accountIdentifier     Account identifier
     * @param applicationIdentifier Application identifier
     * @param serviceDetails        List of BasicEntity representing service details to update
     * @throws HealControlCenterException if Redis update fails
     */
    public void updateServiceApplication(String accountIdentifier, String applicationIdentifier, List<BasicEntity> serviceDetails) {
        String key = ACCOUNTS_KEY + "/" + accountIdentifier + APPLICATION_KEY + "/" + applicationIdentifier + SERVICES_KEY;
        String hashKey = ACCOUNTS_HASH + "_" + accountIdentifier + APPLICATION_HASH + "_" + applicationIdentifier + SERVICES_HASH;

        try {
            redisUtilities.updateKey(key, hashKey, serviceDetails);
        } catch (Exception e) {
            log.error("Error while updating services for application [{}], account [{}]", applicationIdentifier, accountIdentifier, e);
        }
    }
    /**
     * Updates Redis with the list of all applications for a given account.
     *
     * @param accountIdentifier Account identifier
     * @param applicationsList  List of Application objects to be saved
     * @throws HealControlCenterException if Redis update fails
     */
    public void updateApplicationDetailsForAccount(String accountIdentifier, List<Application> applicationsList)
            throws HealControlCenterException {
        try {
            redisUtilities.updateKey(
                    ACCOUNTS_KEY + "/" + accountIdentifier + APPLICATION_KEY,
                    ACCOUNTS_HASH + "_" + accountIdentifier + APPLICATION_HASH,
                    applicationsList
            );
        } catch (Exception e) {
            log.error("Error occurred while updating application details for accountIdentifier: {}", accountIdentifier, e);
            throw new HealControlCenterException("Failed to update application list for account.");
        }
    }

    /**
     * Updates Redis with the details of a single application for a specific account.
     *
     * @param accountIdentifier Account identifier
     * @param application       Application object to update
     * @throws HealControlCenterException if Redis update fails
     */
    public void updateApplication(String accountIdentifier, Application application) {
        try {
            redisUtilities.updateKey(
                    ACCOUNTS_KEY + "/" + accountIdentifier + APPLICATION_KEY + "/" + application.getIdentifier(),
                    ACCOUNTS_HASH + "_" + accountIdentifier + APPLICATION_HASH + "_" + application.getIdentifier(),
                    application
            );
        } catch (Exception e) {
            log.error("Error occurred while updating application for accountIdentifier: {} and applicationIdentifier: {}",
                    accountIdentifier, application.getIdentifier(), e);
            //throw new HealControlCenterException("Failed to update application in Redis.");
        }
    }

    /**
     * Retrieves the list of all applications associated with the given account from Redis.
     *
     * @param accountIdentifier Account identifier
     * @return List of Application objects, or an empty list if none found
     * @throws HealControlCenterException if Redis fetch fails
     */
    public List<Application> getApplicationsForAccount(String accountIdentifier)
            throws HealControlCenterException {
        try {
            String serviceApplicationDetails = redisUtilities.getKey(
                    ACCOUNTS_KEY + "/" + accountIdentifier + APPLICATION_KEY,
                    ACCOUNTS_HASH + "_" + accountIdentifier + APPLICATION_HASH
            );
            if (serviceApplicationDetails == null) {
                log.debug("Applications details not found for the accountIdentifier : [{}]", accountIdentifier);
                return Collections.emptyList();
            }

            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(
                    serviceApplicationDetails, new TypeReference<List<Application>>() {
                    }
            );
        } catch (Exception e) {
            log.error("Error occurred while getting application details for accountIdentifier: {}", accountIdentifier, e);
            throw new HealControlCenterException("Failed to fetch application list from Redis.");
        }
    }

    /**
     * Deletes application details (key and hash) for a specific application under a given account from Redis.
     *
     * @param accountIdentifier     Account identifier
     * @param applicationIdentifier Application identifier
     * @throws HealControlCenterException if Redis deletion fails
     */
    public void deleteApplication(String accountIdentifier, String applicationIdentifier)
            throws HealControlCenterException {
        try {
            redisUtilities.deleteKey(
                    ACCOUNTS_KEY + "/" + accountIdentifier + APPLICATION_KEY + "/" + applicationIdentifier,
                    ACCOUNTS_HASH + "_" + accountIdentifier + APPLICATION_HASH + "_" + applicationIdentifier
            );
        } catch (Exception e) {
            log.error("Error occurred while deleting the keys for applicationIdentifier: {} and accountIdentifier: {}",
                    applicationIdentifier, accountIdentifier, e);
            throw new HealControlCenterException("Error in deleting Redis keys for applicationIdentifier: "
                    + applicationIdentifier + " and accountIdentifier: " + accountIdentifier);
        }
    }

    /**
     * Removes a specific service from all applications it is mapped to in Redis.
     * Throws a RuntimeException if any error occurs during removal.
     *
     * @param accountIdentifier The identifier of the account.
     * @param serviceId         The ID of the service to remove.
     */
    public void removeServiceFromAllApplications(String accountIdentifier, int serviceId) throws HealControlCenterException {
        try {
            List<Application> applications = getApplicationsForAccount(accountIdentifier);
            if (applications.isEmpty()) {
                log.info("No applications found for account [{}]. Skipping service removal from applications.", accountIdentifier);
                return;
            }

            for (Application app : applications) {
                List<BasicEntity> mappedServices = getServicesMappedToApplication(accountIdentifier, app.getIdentifier());
                boolean removed = mappedServices.removeIf(service -> service.getId() == serviceId);
                if (removed) {
                    updateServiceApplication(accountIdentifier, app.getIdentifier(), mappedServices);
                    log.info("Removed service [id: {}] from application [{}] in Redis.", serviceId, app.getIdentifier());
                }
            }
        } catch (Exception e) {
            log.error("Error removing service [id: {}] from all applications in Redis for account [{}]. Details: ", serviceId, accountIdentifier, e);
            throw new HealControlCenterException("Error removing service [id: " + serviceId + "] from all applications in Redis for account [" + accountIdentifier + "]");
        }
    }
}
