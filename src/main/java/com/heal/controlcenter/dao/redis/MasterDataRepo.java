package com.heal.controlcenter.dao.redis;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.OSIndexZoneDetails;
import com.heal.configuration.pojos.ViewTypes;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Repository
@Slf4j
public class MasterDataRepo {

    @Autowired
    ObjectMapper objectMapper;
    @Autowired
    RedisUtilities redisUtilities;

    private List<ViewTypes> viewTypesList;

    public List<OSIndexZoneDetails> getHealOSIndexToZoneMapping() {
        String key = "/heal/index/zones";
        String hashKey = "HEAL_INDEX_ZONES";
        try {
            String osIndexToZoneMapping = redisUtilities.getKey(key, hashKey);
            if (osIndexToZoneMapping == null || osIndexToZoneMapping.trim().isEmpty()) {
                log.warn("Null/Empty value found for key {} in redis.", key);
                return null;
            }

            return objectMapper.readValue(osIndexToZoneMapping, new TypeReference<>() {
            });

        } catch (Exception e) {
            log.error("Exception encountered while getting key '{}': {}", key, hashKey, e);
            return null;
        }
    }

    public List<ViewTypes> getViewTypes() {
        String HEAL_KEY = "/heal";
        String TYPE_KEY = "/types";
        String HEAL_HASH = "HEAL";
        String TYPE_HASH = "_TYPES";
        try {
            String typesObject = redisUtilities.getKey(HEAL_KEY + TYPE_KEY, HEAL_HASH + TYPE_HASH);
            if (typesObject == null) {
                log.debug("Type Details not found.");
                return Collections.emptyList();
            }
            return objectMapper.readValue(typesObject, new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting All Types details.");
        }

        return Collections.emptyList();
    }

    public List<ViewTypes> getTypes() {
        String HEAL_KEY = "/heal";
        String TYPE_KEY = "/types";
        String HEAL_HASH = "HEAL";
        String TYPE_HASH = "_TYPES";
        try {
            String typesObject = redisUtilities.getKey(HEAL_KEY + TYPE_KEY, HEAL_HASH + TYPE_HASH);
            if (typesObject == null) {
                log.debug("Type Details not found.");
                return Collections.emptyList();
            }
            viewTypesList = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(typesObject, new TypeReference<List<ViewTypes>>() {});
        } catch (Exception e) {
            log.error("Error occurred while getting All Types details.");
        }
        return viewTypesList;
    }

    public List<ViewTypes> getTypeDetailsByTypeName(String typeName) {
        if(viewTypesList == null || viewTypesList.isEmpty()) {
            viewTypesList = new MasterDataRepo().getViewTypes();
        }
        return viewTypesList
                .parallelStream()
                .filter(a -> a.getTypeName().equalsIgnoreCase(typeName))
                .collect(Collectors.toList());
    }

    /**
     * Fetches all ViewTypes objects from Redis where typeName matches the provided argument.
     * <p>
     * Steps performed:
     * <ul>
     *   <li>Logs the start of the fetch process with the provided typeName.</li>
     *   <li>Fetches all ViewTypes from Redis.</li>
     *   <li>Filters the ViewTypes by the given typeName (case-insensitive).</li>
     *   <li>Logs the number of results fetched and the typeName requested.</li>
     *   <li>Handles and logs any exceptions, returning an empty list if an error occurs.</li>
     * </ul>
     *
     * @param typeName The typeName to filter ViewTypes by (case-insensitive).
     * @return List of ViewTypes with the given typeName or empty list if not found or error occurs.
     */
    public List<ViewTypes> getViewTypesByName(String typeName) {
        log.info("[DAO] Started fetching ViewTypes for typeName: {}", typeName);
        try {
            List<ViewTypes> allTypes = getViewTypes();
            if (typeName == null || typeName.trim().isEmpty()) {
                log.warn("[DAO] Type name is null or empty in getHealViewTypesByName.");
                return Collections.emptyList();
            }
            List<ViewTypes> filteredList = allTypes.stream()
                    .filter(vt -> typeName.equalsIgnoreCase(vt.getTypeName()))
                    .collect(Collectors.toList());
            log.info("[DAO] Successfully fetched {} ViewTypes for typeName '{}'.", filteredList.size(), typeName);
            return filteredList;
        } catch (Exception e) {
            log.error("[DAO] Exception occurred in getHealViewTypesByName for typeName '{}': {}", typeName, e.getMessage(), e);
            return Collections.emptyList();
        }
    }
}
