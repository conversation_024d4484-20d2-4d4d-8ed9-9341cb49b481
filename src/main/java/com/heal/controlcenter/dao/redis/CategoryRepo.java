package com.heal.controlcenter.dao.redis;

import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.Category;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

@Repository
@Slf4j
public class CategoryRepo {
    private static final String ACCOUNTS_KEY = "/accounts";
    private static final String CATEGORY_KEY = "/categories";
    private static final String ACCOUNTS_HASH = "ACCOUNTS";
    private static final String CATEGORY_HASH = "_CATEGORIES";

    @Autowired
    private RedisUtilities redisUtilities;

    /**
     * Retrieves the list of categories for a given account from Redis.
     *
     * @param accountIdentifier Account identifier
     * @return List of Category objects, or an empty list if none found
     * @throws HealControlCenterException if Redis fetch fails
     */
    public List<Category> getCategoryDetails(String accountIdentifier) throws HealControlCenterException {
        String key = ACCOUNTS_KEY + "/" + accountIdentifier + CATEGORY_KEY;
        String hashKey = ACCOUNTS_HASH + "_" + accountIdentifier + CATEGORY_HASH;
        log.debug("Fetching category details from Redis for accountIdentifier: {}, key: {}, hashKey: {}", accountIdentifier, key, hashKey);
        try {
            String categoryObject = redisUtilities.getKey(key, hashKey);
            if (categoryObject == null) {
                log.debug("Category details not found for account: [{}]", accountIdentifier);
                return Collections.emptyList();
            }
            log.debug("Category details found for account: [{}]", accountIdentifier);
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(
                    categoryObject, new TypeReference<List<Category>>() {
                    });
        } catch (Exception e) {
            log.error("Error occurred while getting categories details for account [{}]. Details: {}", accountIdentifier, e.getMessage(), e);
            throw new HealControlCenterException("Failed to fetch category list from Redis.");
        }
    }

    /**
     * Updates Redis with the list of all categories for a given account.
     *
     * @param accountIdentifier Account identifier
     * @param categories        List of Category objects to be saved
     * @throws HealControlCenterException if Redis update fails
     */
    public void updateCategoryDetails(String accountIdentifier, List<Category> categories) throws HealControlCenterException {
        String key = ACCOUNTS_KEY + "/" + accountIdentifier + CATEGORY_KEY;
        String hashKey = ACCOUNTS_HASH + "_" + accountIdentifier + CATEGORY_HASH;
        log.debug("Updating all category details in Redis for accountIdentifier: {}, key: {}, hashKey: {}", accountIdentifier, key, hashKey);
        try {
            redisUtilities.updateKey(key, hashKey, categories);
            log.debug("Successfully updated all category details in Redis for accountIdentifier: {}", accountIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while updating the category details for accountIdentifier: {}. Details: {}", accountIdentifier, e.getMessage(), e);
            throw new HealControlCenterException("Failed to update category list for account.");
        }
    }

    /**
     * Updates Redis with the details of a single category for a specific account.
     *
     * @param accountIdentifier Account identifier
     * @param categoryBean      Category object to update
     * @throws HealControlCenterException if Redis update fails
     */
    public void updateCategory(String accountIdentifier, Category categoryBean) throws HealControlCenterException {
        String key = ACCOUNTS_KEY + "/" + accountIdentifier + CATEGORY_KEY + "/" + categoryBean.getIdentifier();
        String hashKey = ACCOUNTS_HASH + "_" + accountIdentifier + CATEGORY_HASH + "_" + categoryBean.getIdentifier();
        log.debug("Updating single category in Redis for accountIdentifier: {}, categoryIdentifier: {}, key: {}, hashKey: {}", accountIdentifier, categoryBean.getIdentifier(), key, hashKey);
        try {
            redisUtilities.updateKey(key, hashKey, categoryBean);
            log.debug("Successfully updated category in Redis for accountIdentifier: {}, categoryIdentifier: {}", accountIdentifier, categoryBean.getIdentifier());
        } catch (Exception e) {
            log.error("Error occurred while updating the category details for the accountIdentifier: {} and categoryIdentifier: {}. Details: {}", accountIdentifier, categoryBean.getIdentifier(), e.getMessage(), e);
            throw new HealControlCenterException("Failed to update category in Redis.");
        }
    }

    /**
     * Deletes a single category from Redis for a specific account.
     *
     * @param accountIdentifier  Account identifier
     * @param categoryIdentifier Category identifier
     * @throws HealControlCenterException if Redis delete fails
     */
    public void deleteCategoryInRedis(String accountIdentifier, String categoryIdentifier) throws HealControlCenterException {
        log.debug("[deleteCategoryInRedis] Start for accountIdentifier: {}, categoryIdentifier: {}", accountIdentifier, categoryIdentifier);
        String key = ACCOUNTS_KEY + "/" + accountIdentifier + CATEGORY_KEY + "/" + categoryIdentifier;
        String hashKey = ACCOUNTS_HASH + "_" + accountIdentifier + CATEGORY_HASH + "_" + categoryIdentifier;
        log.debug("[deleteCategoryInRedis] Deleting category from Redis. key: {}, hashKey: {}", key, hashKey);
        try {
            redisUtilities.deleteKey(key, hashKey);
            log.debug("[deleteCategoryInRedis] Successfully deleted category from Redis for accountIdentifier: {}, categoryIdentifier: {}", accountIdentifier, categoryIdentifier);
        } catch (Exception e) {
            log.error("[deleteCategoryInRedis] Error occurred while deleting the category from Redis for accountIdentifier: {} and categoryIdentifier: {}. Details: {}", accountIdentifier, categoryIdentifier, e.getMessage(), e);
            throw new HealControlCenterException("Failed to delete category from Redis.");
        }
    }
}