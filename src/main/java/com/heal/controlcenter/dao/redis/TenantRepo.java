package com.heal.controlcenter.dao.redis;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.TenantOpenSearchDetails;
import com.heal.controlcenter.util.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@Slf4j
public class TenantRepo {
    @Autowired
    ObjectMapper objectMapper;
    @Autowired
    RedisUtilities redisUtilities;

    public List<TenantOpenSearchDetails> getTenantOpenSearchDetails(String tenantIdentifier) {
        String key = "/tenants/" + tenantIdentifier + "/opensearch";
        String hashKey = "TENANTS_" + tenantIdentifier + "_OPENSEARCH";
        try {
            String tenantOpenSearchDetails = redisUtilities.getKey(key, hashKey);
            if (tenantOpenSearchDetails == null || tenantOpenSearchDetails.trim().isEmpty()) {
                log.warn("Null/Empty value found for key {} in redis.", key);
                return Collections.emptyList();
            }

            return objectMapper.readValue(tenantOpenSearchDetails, new TypeReference<List<TenantOpenSearchDetails>>() {
            });

        } catch (Exception e) {
            log.error("Exception encountered while getting key '{}': {}", key, hashKey, e);
            return Collections.emptyList();
        }
    }
}
