package com.heal.controlcenter.service;

import com.appnomic.appsone.common.protbuf.CommandRequestProtos;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Slf4j
public class CommandForwarderToQueue {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Qualifier("commandMessagesQueue")
    @Autowired
    private Queue commandMessagesQueue;

    public void sendAgentCommandMessage(CommandRequestProtos.CommandRequest commandRequest) {
        log.debug("CommandRequest details: {}", commandRequest);
        this.rabbitTemplate.convertAndSend(commandMessagesQueue.getName(), commandRequest.toByteArray());
    }
}
