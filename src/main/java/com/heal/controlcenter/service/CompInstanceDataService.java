package com.heal.controlcenter.service;

import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.pojo.CompInstKpiMapping;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Service class converted from appsone-controlcenter JDBI to JDBC.
 * Original appsone-controlcenter class used JDBI with Handle and CompInstanceDataDao.
 * This implementation uses Spring JDBC with JdbcTemplate for database operations.
 */
@Slf4j
@Service
public class CompInstanceDataService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * Gets component instance KPI mapping - converted from appsone-controlcenter JDBI to JDBC.
     * 
     * Original appsone-controlcenter method signature:
     * public List<CompInstKpiMapping> getCompInstKpiMapping(int compInstanceId, <PERSON>le handle) throws ControlCenterException
     * 
     * Original JDBI query from appsone-controlcenter:
     * @SqlQuery("select id compInstKpiId, mst_kpi_details_id mstKpiId, mst_producer_kpi_mapping_id mstProducerKpiMappingId, " +
     *           "collection_interval collectionInterval, status, mst_producer_id mstProducerId, notification " +
     *           "from comp_instance_kpi_details where comp_instance_id = :compInstanceId")
     * 
     * @param compInstanceId Component instance ID
     * @param handle Handle parameter (removed in JDBC conversion)
     * @return List of CompInstKpiMapping matching original appsone-controlcenter behavior
     * @throws HealControlCenterException if error occurs during database operation
     */
    public List<CompInstKpiMapping> getCompInstKpiMapping(int compInstanceId, Object handle) throws HealControlCenterException {
        String sql = "SELECT id AS compInstKpiId, mst_kpi_details_id AS mstKpiId, " +
                     "mst_producer_kpi_mapping_id AS mstProducerKpiMappingId, " +
                     "collection_interval AS collectionInterval, status, " +
                     "mst_producer_id AS mstProducerId, notification " +
                     "FROM comp_instance_kpi_details WHERE comp_instance_id = ?";
        
        try {
            log.debug("Fetching component instance KPI mapping for compInstanceId [{}]", compInstanceId);
            
            List<CompInstKpiMapping> results = jdbcTemplate.query(sql, (rs, rowNum) -> {
                CompInstKpiMapping mapping = new CompInstKpiMapping();
                mapping.setCompInstKpiId(rs.getInt("compInstKpiId"));
                mapping.setMstKpiId(rs.getInt("mstKpiId"));
                mapping.setMstProducerKpiMappingId(rs.getInt("mstProducerKpiMappingId"));
                mapping.setCollectionInterval(rs.getInt("collectionInterval"));
                mapping.setStatus(rs.getInt("status"));
                mapping.setMstProducerId(rs.getInt("mstProducerId"));
                mapping.setNotification(rs.getInt("notification"));
                return mapping;
            }, compInstanceId);
            
            log.debug("Successfully fetched {} component instance KPI mappings for compInstanceId [{}]", results.size(), compInstanceId);
            return results;
            
        } catch (Exception e) {
            log.error("Error while fetching component instance KPI mapping for compInstanceId [{}]", compInstanceId, e);
            throw new HealControlCenterException("Error while fetching component instance KPI mapping for compInstanceId [" + compInstanceId + "]");
        }
    }
}
