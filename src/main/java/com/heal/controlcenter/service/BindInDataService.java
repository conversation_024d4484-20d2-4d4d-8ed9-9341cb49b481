package com.heal.controlcenter.service;

import com.heal.controlcenter.dao.mysql.entity.ComponentInstanceDao;
import com.heal.controlcenter.dao.mysql.CompInstanceDao;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.pojo.ClusterInstancePojo;
import com.heal.controlcenter.pojo.GroupKpiAttributeMapping;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.ArrayList;
import java.util.Collections;

/**
 * Service class converted from appsone-controlcenter JDBI to JDBC.
 * Original appsone-controlcenter class used JDBI with Handle and BindInDao.
 * This implementation uses Spring JDBC with JdbcTemplate for database operations.
 */
@Slf4j
@Service
public class BindInDataService {

    @Autowired
    private ComponentInstanceDao componentInstanceDao;

    @Autowired
    private CompInstanceDao compInstanceDao;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * Gets cluster instance mapping - converted from appsone-controlcenter JDBI to JDBC.
     * 
     * Original appsone-controlcenter method signature:
     * public ClusterInstancePojo getClusterInstanceMapping(int instanceId, Handle handle) throws ControlCenterException {
     *     BindInDao dao = getDaoConnection(handle, BindInDao.class);
     *     try {
     *         return dao.getClusterInstanceMapping(instanceId);
     *     } catch (Exception e) {
     *         LOGGER.error("Error while fetching cluster mapping details for instanceId [{}]", instanceId);
     *         throw new ControlCenterException("Error while fetching cluster mapping details for instanceId [{}]", String.valueOf(instanceId));
     *     } finally {
     *         closeDaoConnection(handle, dao);
     *     }
     * }
     * 
     * JDBC conversion removes the need for Handle parameter and manual connection management.
     * Spring's JdbcTemplate handles connection management automatically.
     * 
     * @param instanceId Component instance ID
     * @return ClusterInstancePojo matching original appsone-controlcenter behavior
     * @throws HealControlCenterException if error occurs during database operation
     */
    public ClusterInstancePojo getClusterInstanceMapping(int instanceId) throws HealControlCenterException {
        try {
            log.debug("Fetching cluster mapping details for instanceId [{}]", instanceId);
            
            // JDBC equivalent of original JDBI dao.getClusterInstanceMapping(instanceId)
            ClusterInstancePojo result = componentInstanceDao.getClusterInstanceMapping(instanceId);
            
            if (result != null) {
                log.debug("Successfully fetched cluster mapping for instanceId [{}]", instanceId);
                return result;
            } else {
                log.warn("No cluster mapping found for instanceId [{}]", instanceId);
                return null;
            }
            
        } catch (Exception e) {
            log.error("Error while fetching cluster mapping details for instanceId [{}]", instanceId, e);
            throw new HealControlCenterException("Error while fetching cluster mapping details for instanceId [" + instanceId + "]");
        }
        // Note: No finally block needed as Spring JdbcTemplate handles connection cleanup automatically
    }

    /**
     * Gets group KPI attribute mapping - converted from appsone-controlcenter JDBI to JDBC.
     *
     * Original appsone-controlcenter method signature:
     * public List<GroupKpiAttributeMapping> getGroupKpiAttributeMapping(int accountId, int compInstanceId, Handle handle) throws ControlCenterException
     *
     * Original JDBI query from appsone-controlcenter:
     * @SqlQuery("select attribute_value attributeValue, mst_kpi_details_id kpiId, alias_name aliasName from comp_instance_kpi_group_details where comp_instance_id = :compInstanceId")
     *
     * @param accountId Account ID (kept for compatibility but not used in query)
     * @param compInstanceId Component instance ID
     * @param handle Handle parameter (removed in JDBC conversion)
     * @return List of GroupKpiAttributeMapping matching original appsone-controlcenter behavior
     * @throws HealControlCenterException if error occurs during database operation
     */
    public List<GroupKpiAttributeMapping> getGroupKpiAttributeMapping(int accountId, int compInstanceId, Object handle) throws HealControlCenterException {
        String sql = "SELECT attribute_value AS attributeValue, mst_kpi_details_id AS kpiId, alias_name AS aliasName " +
                     "FROM comp_instance_kpi_group_details WHERE comp_instance_id = ?";

        try {
            log.debug("Fetching group KPI attribute mapping for compInstanceId [{}]", compInstanceId);

            List<GroupKpiAttributeMapping> results = jdbcTemplate.query(sql, (rs, rowNum) -> {
                GroupKpiAttributeMapping mapping = new GroupKpiAttributeMapping();
                mapping.setAttributeValue(rs.getString("attributeValue"));
                mapping.setKpiId(rs.getInt("kpiId"));
                mapping.setAliasName(rs.getString("aliasName"));
                return mapping;
            }, compInstanceId);

            log.debug("Successfully fetched {} group KPI attribute mappings for compInstanceId [{}]", results.size(), compInstanceId);
            return results;

        } catch (Exception e) {
            log.error("Error while fetching group KPI attribute mapping for compInstanceId [{}]", compInstanceId, e);
            throw new HealControlCenterException("Error while fetching group KPI attribute mapping for compInstanceId [" + compInstanceId + "]");
        }
    }
}
