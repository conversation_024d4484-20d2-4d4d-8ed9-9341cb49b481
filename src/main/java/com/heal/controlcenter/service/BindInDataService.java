package com.heal.controlcenter.service;

import com.heal.controlcenter.dao.mysql.entity.ComponentInstanceDao;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.pojo.ClusterInstancePojo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Service class converted from appsone-controlcenter JDBI to JDBC.
 * Original appsone-controlcenter class used JDBI with Handle and BindInDao.
 * This implementation uses Spring JDBC with JdbcTemplate for database operations.
 */
@Slf4j
@Service
public class BindInDataService {

    @Autowired
    private ComponentInstanceDao componentInstanceDao;

    /**
     * Gets cluster instance mapping - converted from appsone-controlcenter JDBI to JDBC.
     * 
     * Original appsone-controlcenter method signature:
     * public ClusterInstancePojo getClusterInstanceMapping(int instanceId, Handle handle) throws ControlCenterException {
     *     BindInDao dao = getDaoConnection(handle, BindInDao.class);
     *     try {
     *         return dao.getClusterInstanceMapping(instanceId);
     *     } catch (Exception e) {
     *         LOGGER.error("Error while fetching cluster mapping details for instanceId [{}]", instanceId);
     *         throw new ControlCenterException("Error while fetching cluster mapping details for instanceId [{}]", String.valueOf(instanceId));
     *     } finally {
     *         closeDaoConnection(handle, dao);
     *     }
     * }
     * 
     * JDBC conversion removes the need for Handle parameter and manual connection management.
     * Spring's JdbcTemplate handles connection management automatically.
     * 
     * @param instanceId Component instance ID
     * @return ClusterInstancePojo matching original appsone-controlcenter behavior
     * @throws HealControlCenterException if error occurs during database operation
     */
    public ClusterInstancePojo getClusterInstanceMapping(int instanceId) throws HealControlCenterException {
        try {
            log.debug("Fetching cluster mapping details for instanceId [{}]", instanceId);
            
            // JDBC equivalent of original JDBI dao.getClusterInstanceMapping(instanceId)
            ClusterInstancePojo result = componentInstanceDao.getClusterInstanceMapping(instanceId);
            
            if (result != null) {
                log.debug("Successfully fetched cluster mapping for instanceId [{}]", instanceId);
                return result;
            } else {
                log.warn("No cluster mapping found for instanceId [{}]", instanceId);
                return null;
            }
            
        } catch (Exception e) {
            log.error("Error while fetching cluster mapping details for instanceId [{}]", instanceId, e);
            throw new HealControlCenterException("Error while fetching cluster mapping details for instanceId [" + instanceId + "]");
        }
        // Note: No finally block needed as Spring JdbcTemplate handles connection cleanup automatically
    }
}
