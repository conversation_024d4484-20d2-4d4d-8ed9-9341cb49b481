package com.heal.controlcenter.service;

import com.heal.controlcenter.dao.mysql.InstallationAttributeDao;
import com.heal.controlcenter.exception.HealControlCenterException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class InstallationAttributeService {

    @Autowired
    private InstallationAttributeDao installationAttributeDao;

    public boolean checkForOfflineHeal(String userId) throws HealControlCenterException {
        boolean offlineHeal = false;
        try {
            String installationMode = installationAttributeDao.checkForInstallationMode(userId);
            if (installationMode != null && installationMode.length() != 0 && installationMode.equalsIgnoreCase("Offline")) {
                offlineHeal = true;
            }
            return offlineHeal;
        } catch (HealControlCenterException e) {
            log.error("Error in fetching installation mode for userId [{}]. Details: {}", userId, e.getMessage());
            throw e;
        }
    }

}
