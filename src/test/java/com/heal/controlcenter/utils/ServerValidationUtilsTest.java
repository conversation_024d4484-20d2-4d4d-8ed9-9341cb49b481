package com.heal.controlcenter.utils;

import com.heal.configuration.entities.UserAccessDetails;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.Service;
import com.heal.controlcenter.dao.redis.AccountRepo;
import com.heal.controlcenter.dao.redis.ServiceRepo;
import com.heal.controlcenter.dao.redis.UserRepo;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.ServerValidationUtils;
import com.heal.controlcenter.util.UIMessages;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class ServerValidationUtilsTest {

    @InjectMocks
    private ServerValidationUtils serverValidationUtils;

    @Mock
    private AccountRepo accountRepo;

    @Mock
    private ServiceRepo serviceRepo;

    @Mock
    private CommonUtils commonUtils;

    @Mock
    private UserRepo userRepo;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void accountValidation_ValidAccount_ReturnsAccount() throws ServerException {
        Account account = new Account();
        account.setId(2);
        account.setStatus(1);
        when(accountRepo.getAccount("validAccount")).thenReturn(account);

        Account result = serverValidationUtils.accountValidation("validAccount");

        assertNotNull(result);
        assertEquals(2, result.getId());
    }

    @Test
    void accountValidation_NullAccount_ThrowsServerException() {
        when(accountRepo.getAccount("invalidAccount")).thenReturn(null);

        ServerException exception = assertThrows(ServerException.class, () ->
                serverValidationUtils.accountValidation("invalidAccount"));
        assertTrue(exception.getMessage().contains("details unavailable"));
    }

    @Test
    void accountValidation_InactiveAccount_ThrowsServerException() {
        Account account = new Account();
        account.setStatus(0);
        when(accountRepo.getAccount("inactiveAccount")).thenReturn(account);

        ServerException exception = assertThrows(ServerException.class, () ->
                serverValidationUtils.accountValidation("inactiveAccount"));
        assertTrue(exception.getMessage().contains("is inactive"));
    }

    @Test
    void accountValidation_GlobalAccount_ThrowsServerException() {
        Account account = new Account();
        account.setId(1);
        account.setStatus(1);
        when(accountRepo.getAccount("globalAccount")).thenReturn(account);

        ServerException exception = assertThrows(ServerException.class, () ->
                serverValidationUtils.accountValidation("globalAccount"));
        assertTrue(exception.getMessage().contains("GLOBAL Account"));
    }

    @Test
    void serviceValidation_ValidService_AccountWiseAccess_ReturnsService() throws ServerException {
        Service service = new Service();
        service.setStatus(1);
        when(serviceRepo.getServiceConfigurationByIdentifier("acc", "svc")).thenReturn(service);

        UserAccessDetails userAccessDetails = mock(UserAccessDetails.class);
        when(userAccessDetails.isAccountWiseAccessible()).thenReturn(true);

        Service result = serverValidationUtils.serviceValidation("user", "acc", "svc", userAccessDetails);
        assertNotNull(result);
    }

    @Test
    void serviceValidation_ServiceNotFound_ThrowsServerException() {
        when(serviceRepo.getServiceConfigurationByIdentifier("acc", "svc")).thenReturn(null);

        UserAccessDetails userAccessDetails = mock(UserAccessDetails.class);

        ServerException exception = assertThrows(ServerException.class, () ->
                serverValidationUtils.serviceValidation("user", "acc", "svc", userAccessDetails));
        assertTrue(exception.getMessage().contains("detail not found"));
    }

    @Test
    void serviceValidation_InactiveService_ThrowsServerException() {
        Service service = new Service();
        service.setStatus(0);
        when(serviceRepo.getServiceConfigurationByIdentifier("acc", "svc")).thenReturn(service);

        UserAccessDetails userAccessDetails = mock(UserAccessDetails.class);

        ServerException exception = assertThrows(ServerException.class, () ->
                serverValidationUtils.serviceValidation("user", "acc", "svc", userAccessDetails));
        assertTrue(exception.getMessage().contains("is inactive"));
    }

    @Test
    void serviceValidation_UserHasNoServiceAccess_ThrowsServerException() {
        Service service = new Service();
        service.setStatus(1);
        when(serviceRepo.getServiceConfigurationByIdentifier("acc", "svc")).thenReturn(service);

        UserAccessDetails userAccessDetails = mock(UserAccessDetails.class);
        when(userAccessDetails.isAccountWiseAccessible()).thenReturn(false);
        List<BasicEntity> services = Arrays.asList(null, new Service()); // no matching identifier
        when(userAccessDetails.getServices()).thenReturn(services);

        // Service with identifier not matching "svc"
        services.get(1).setIdentifier("other");

        ServerException exception = assertThrows(ServerException.class, () ->
                serverValidationUtils.serviceValidation("user", "acc", "svc", userAccessDetails));
        assertTrue(exception.getMessage().contains("does not have access"));
    }

    @Test
    void serviceValidation_UserHasServiceAccess_ReturnsService() throws ServerException {
        Service service = new Service();
        service.setStatus(1);
        when(serviceRepo.getServiceConfigurationByIdentifier("acc", "svc")).thenReturn(service);

        UserAccessDetails userAccessDetails = mock(UserAccessDetails.class);
        when(userAccessDetails.isAccountWiseAccessible()).thenReturn(false);
        Service allowedService = new Service();
        allowedService.setIdentifier("svc");
        when(userAccessDetails.getServices()).thenReturn(Arrays.asList(allowedService));

        Service result = serverValidationUtils.serviceValidation("user", "acc", "svc", userAccessDetails);
        assertNotNull(result);
    }

    @Test
    void authKeyValidation_ValidKey_ReturnsUserId() throws Exception {
        when(commonUtils.getUserId("authKey")).thenReturn("userId");
        String result = serverValidationUtils.authKeyValidation("authKey");
        assertEquals("userId", result);
    }

    @Test
    void authKeyValidation_NullUserId_ThrowsServerException() throws Exception {
        when(commonUtils.getUserId("authKey")).thenReturn(null);
        ServerException ex = assertThrows(ServerException.class, () ->
                serverValidationUtils.authKeyValidation("authKey"));
        assertTrue(ex.getMessage().contains(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE));
    }

    @Test
    void authKeyValidation_EmptyUserId_ThrowsServerException() throws Exception {
        when(commonUtils.getUserId("authKey")).thenReturn("   ");
        ServerException ex = assertThrows(ServerException.class, () ->
                serverValidationUtils.authKeyValidation("authKey"));
        assertTrue(ex.getMessage().contains(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE));
    }

    @Test
    void authKeyValidation_ControlCenterException_ThrowsServerException() throws Exception {
        when(commonUtils.getUserId("authKey")).thenThrow(new HealControlCenterException("fail"));
        ServerException ex = assertThrows(ServerException.class, () ->
                serverValidationUtils.authKeyValidation("authKey"));
        assertTrue(ex.getMessage().contains(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE));
    }

    @Test
    void userAccessDetailsValidation_Valid_ReturnsDetails() throws ServerException {
        UserAccessDetails details = new UserAccessDetails();
        List<UserAccessDetails> detailsList = Arrays.asList(details);
        when(userRepo.getUserAccessDetails("user")).thenReturn(detailsList);

        Map<String, UserAccessDetails> map = new HashMap<>();
        map.put("acc", details);
        when(serverValidationUtils.getUserAccessDetailsByAccount(detailsList, "acc")).thenReturn(map);

        UserAccessDetails result = serverValidationUtils.userAccessDetailsValidation("user", "acc");
        assertNotNull(result);
    }

    @Test
    void userAccessDetailsValidation_NullList_ThrowsServerException() {
        when(userRepo.getUserAccessDetails("user")).thenReturn(null);

        ServerException ex = assertThrows(ServerException.class, () ->
                serverValidationUtils.userAccessDetailsValidation("user", "acc"));
        assertTrue(ex.getMessage().contains("not found"));
    }

    @Test
    void userAccessDetailsValidation_EmptyList_ThrowsServerException() {
        when(userRepo.getUserAccessDetails("user")).thenReturn(Collections.emptyList());

        ServerException ex = assertThrows(ServerException.class, () ->
                serverValidationUtils.userAccessDetailsValidation("user", "acc"));
        assertTrue(ex.getMessage().contains("not found"));
    }

    @Test
    void userAccessDetailsValidation_NoAccountAccess_ThrowsServerException() {
        List<UserAccessDetails> detailsList = Arrays.asList(new UserAccessDetails());
        when(userRepo.getUserAccessDetails("user")).thenReturn(detailsList);

        when(serverValidationUtils.getUserAccessDetailsByAccount(detailsList, "acc")).thenReturn(new HashMap<>());

        ServerException ex = assertThrows(ServerException.class, () ->
                serverValidationUtils.userAccessDetailsValidation("user", "acc"));
        assertTrue(ex.getMessage().contains("doesn't have access"));
    }
}
