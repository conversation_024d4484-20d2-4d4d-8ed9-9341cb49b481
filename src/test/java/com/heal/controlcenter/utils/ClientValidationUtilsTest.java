package com.heal.controlcenter.utils;

import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.UIMessages;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

class ClientValidationUtilsTest {

    private final ClientValidationUtils clientValidationUtils = new ClientValidationUtils();

    @Test
    void testAccountIdentifierValidation_ValidInput() {
        assertDoesNotThrow(() -> clientValidationUtils.accountIdentifierValidation("validAccount"));
    }

    @Test
    void testAccountIdentifierValidation_InvalidInput() {
        ClientException exception = assertThrows(ClientException.class,
                () -> clientValidationUtils.accountIdentifierValidation(null));
        assertTrue(exception.getMessage().contains(UIMessages.ACCOUNT_IDENTIFIER_INVALID));

        exception = assertThrows(ClientException.class,
                () -> clientValidationUtils.accountIdentifierValidation(""));
        assertTrue(exception.getMessage().contains(UIMessages.ACCOUNT_IDENTIFIER_INVALID));

        exception = assertThrows(ClientException.class,
                () -> clientValidationUtils.accountIdentifierValidation("   "));
        assertTrue(exception.getMessage().contains(UIMessages.ACCOUNT_IDENTIFIER_INVALID));
    }

    @Test
    void testServiceIdentifierValidation_ValidInput() {
        assertDoesNotThrow(() -> clientValidationUtils.serviceIdentifierValidation("service1"));
    }

    @Test
    void testServiceIdentifierValidation_InvalidInput() {
        ClientException exception = assertThrows(ClientException.class,
                () -> clientValidationUtils.serviceIdentifierValidation(null));
        assertTrue(exception.getMessage().contains(UIMessages.SERVICE_IDENTIFIER_INVALID));

        exception = assertThrows(ClientException.class,
                () -> clientValidationUtils.serviceIdentifierValidation(""));
        assertTrue(exception.getMessage().contains(UIMessages.SERVICE_IDENTIFIER_INVALID));

        exception = assertThrows(ClientException.class,
                () -> clientValidationUtils.serviceIdentifierValidation("   "));
        assertTrue(exception.getMessage().contains(UIMessages.SERVICE_IDENTIFIER_INVALID));
    }

    @Test
    void testInstanceIdentifierValidation_ValidInput() {
        assertDoesNotThrow(() -> clientValidationUtils.instanceIdentifierValidation("instance1"));
    }

    @Test
    void testInstanceIdentifierValidation_InvalidInput() {
        ClientException exception = assertThrows(ClientException.class,
                () -> clientValidationUtils.instanceIdentifierValidation(null));
        assertTrue(exception.getMessage().contains(UIMessages.INSTANCE_IDENTIFIER_INVALID));

        exception = assertThrows(ClientException.class,
                () -> clientValidationUtils.instanceIdentifierValidation(""));
        assertTrue(exception.getMessage().contains(UIMessages.INSTANCE_IDENTIFIER_INVALID));

        exception = assertThrows(ClientException.class,
                () -> clientValidationUtils.instanceIdentifierValidation("   "));
        assertTrue(exception.getMessage().contains(UIMessages.INSTANCE_IDENTIFIER_INVALID));
    }

    @Test
    void testAuthKeyValidation_ValidInput() {
        assertDoesNotThrow(() -> clientValidationUtils.authKeyValidation("authKey"));
    }

    @Test
    void testAuthKeyValidation_InvalidInput() {
        ClientException exception = assertThrows(ClientException.class,
                () -> clientValidationUtils.authKeyValidation(null));
        assertTrue(exception.getMessage().contains(UIMessages.AUTH_KEY_INVALID));

        exception = assertThrows(ClientException.class,
                () -> clientValidationUtils.authKeyValidation(""));
        assertTrue(exception.getMessage().contains(UIMessages.AUTH_KEY_INVALID));

        exception = assertThrows(ClientException.class,
                () -> clientValidationUtils.authKeyValidation("   "));
        assertTrue(exception.getMessage().contains(UIMessages.AUTH_KEY_INVALID));
    }

    @Test
    void testNullOrEmptyCheck_ValidInput() {
        assertDoesNotThrow(() -> clientValidationUtils.nullOrEmptyCheck("notEmpty", "Custom error"));
    }

    @Test
    void testNullOrEmptyCheck_InvalidInput() {
        String errorMsg = "Custom error";
        ClientException exception = assertThrows(ClientException.class,
                () -> clientValidationUtils.nullOrEmptyCheck(null,  "Custom error"));
        assertEquals("ClientException : Custom error", exception.getMessage());

        exception = assertThrows(ClientException.class,
                () -> clientValidationUtils.nullOrEmptyCheck("", errorMsg));
        assertEquals("ClientException : Custom error", exception.getMessage());

        exception = assertThrows(ClientException.class,
                () -> clientValidationUtils.nullOrEmptyCheck("   ", errorMsg));
        assertEquals("ClientException : Custom error", exception.getMessage());
    }

    @Test
    void testNullRequestBodyCheck_ValidInput() {
        assertDoesNotThrow(() -> clientValidationUtils.nullRequestBodyCheck(new Object(), "Body is null"));
    }

    @Test
    void testNullRequestBodyCheck_InvalidInput() {
        String errorMsg = "Body is null";
        ClientException exception = assertThrows(ClientException.class,
                () -> clientValidationUtils.nullRequestBodyCheck(null, errorMsg));
        assertEquals("ClientException : " + errorMsg, exception.getMessage());
    }
}