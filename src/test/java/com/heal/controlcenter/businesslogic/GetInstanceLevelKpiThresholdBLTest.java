package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.ViewTypes;
import com.heal.controlcenter.beans.CompInstanceKpiGroupDetailsBean;
import com.heal.controlcenter.beans.ComponentInstanceBean;
import com.heal.controlcenter.beans.InstancesKpisBean;
import com.heal.controlcenter.dao.mysql.CompInstanceDao;
import com.heal.controlcenter.dao.mysql.KPIDao;
import com.heal.controlcenter.dao.mysql.ThresholdDao;
import com.heal.controlcenter.dao.mysql.entity.InstanceKpiAttributeThresholdBean;
import com.heal.controlcenter.dao.mysql.entity.KpiBean;
import com.heal.controlcenter.dao.opensearch.CollatedKpiRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.KpiAttrThresholdInfo;
import com.heal.controlcenter.pojo.KpiAttributeThresholdInfo;
import com.heal.controlcenter.util.*;
import com.heal.controlcenter.beans.UtilityBean;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class GetInstanceLevelKpiThresholdBLTest {

    @InjectMocks
    private GetInstanceLevelKpiThresholdBL getInstanceLevelKpiThresholdBL;

    @Spy
    private ClientValidationUtils clientValidationUtils;

    @Mock
    private CommonUtils commonUtils;

    @Mock
    private CompInstanceDao compInstanceDao;

    @Mock
    private ThresholdDao thresholdDao;

    @Mock
    private KPIDao kpiDao;

    @Mock
    private CacheWrapper cacheWrapper;

    @Mock
    private ServerValidationUtils serverValidationUtils;

    @Mock
    private CollatedKpiRepo collatedKpiRepo;

    @Mock
    private DateTimeUtil dateTimeUtil;

    InstancesKpisBean sampleInstanceKpisBean = InstancesKpisBean.builder().build();

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    // client validation unit test cases
    @Test
    void testClientValidation_ValidInputs() throws ClientException {
        String authKey = "auth";
        String accountIdentifier = "acc";
        String instanceIds = "1,2,3";
        String kpiId = "10";
        String[] params = {authKey, accountIdentifier, instanceIds, kpiId};

        UtilityBean<InstancesKpisBean> result = getInstanceLevelKpiThresholdBL.clientValidation(sampleInstanceKpisBean, params);

        assertNotNull(result);
        assertEquals(List.of(1,2,3), result.getPojoObject().getInstanceIds());
        assertEquals(10, result.getPojoObject().getKpiId());
        assertEquals(authKey, result.getRequestParams().get(Constants.AUTH_KEY));
        assertEquals(accountIdentifier, result.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
    }

    @Test
    void testClientValidation_NullRequestBody() throws ClientException {
        String[] params = {"auth", "acc", "1", "10"};
        doThrow(new ClientException(UIMessages.REQUEST_BODY_NULL))
                .when(clientValidationUtils).nullRequestBodyCheck(null, UIMessages.REQUEST_BODY_NULL);

        ClientException ex = assertThrows(ClientException.class,
                () -> getInstanceLevelKpiThresholdBL.clientValidation(null, params));
        assertEquals("ClientException : " + UIMessages.REQUEST_BODY_NULL, ex.getMessage());
    }

    @Test
    void testClientValidation_InvalidInstanceIds_NullOrEmpty() {
        String[] params = {"auth", "acc", "", "10"};
        ClientException ex = assertThrows(ClientException.class,
                () -> getInstanceLevelKpiThresholdBL.clientValidation(sampleInstanceKpisBean, params));
        assertTrue(ex.getMessage().contains("Invalid instanceId"));
    }

    @Test
    void testClientValidation_InvalidInstanceIds_ZeroOrNegative() {
        String[] params = {"auth", "acc", "0,-1", "10"};
        ClientException ex = assertThrows(ClientException.class,
                () -> getInstanceLevelKpiThresholdBL.clientValidation(sampleInstanceKpisBean, params));
        assertTrue(ex.getMessage().contains("Invalid instance ID"));
    }

    @Test
    void testClientValidation_InvalidKpiId_Empty() {
        String[] params = {"auth", "acc", "1", ""};
        ClientException ex = assertThrows(ClientException.class,
                () -> getInstanceLevelKpiThresholdBL.clientValidation(sampleInstanceKpisBean, params));
        assertTrue(ex.getMessage().contains("Invalid kpiId"));
    }

    @Test
    void testClientValidation_InvalidKpiId_Zero() {
        String[] params = {"auth", "acc", "1", "0"};
        ClientException ex = assertThrows(ClientException.class,
                () -> getInstanceLevelKpiThresholdBL.clientValidation(sampleInstanceKpisBean, params));
        assertTrue(ex.getMessage().contains("Invalid kpiId"));
    }

    @Test
    void testClientValidation_InvalidKpiId_Negative() {
        String[] params = {"auth", "acc", "1", "-5"};
        ClientException ex = assertThrows(ClientException.class,
                () -> getInstanceLevelKpiThresholdBL.clientValidation(sampleInstanceKpisBean, params));
        assertTrue(ex.getMessage().contains("Invalid kpiId"));
    }

    // server validation unit test cases
    private UtilityBean<InstancesKpisBean> buildUtilityBean(List<Integer> instanceIds, int kpiId) {
        InstancesKpisBean bean = InstancesKpisBean.builder()
                .instanceIds(instanceIds)
                .kpiId(kpiId)
                .build();
        HashMap<String, String> params = new HashMap<>();
        params.put(Constants.AUTH_KEY, "auth");
        params.put(Constants.ACCOUNT_IDENTIFIER, "acc");
        return UtilityBean.<InstancesKpisBean>builder()
                .pojoObject(bean)
                .requestParams(params)
                .build();
    }

    private void fillViewTypeData(Map<String, List<ViewTypes>> viewTypesIdMap, Map<String, Object> metadata) {
        ViewTypes low = ViewTypes.builder().subTypeId(1).subTypeName(Constants.THRESHOLD_SEVERITY_TYPE_LOW).build();
        ViewTypes med = ViewTypes.builder().subTypeId(2).subTypeName(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM).build();
        ViewTypes high = ViewTypes.builder().subTypeId(3).subTypeName(Constants.THRESHOLD_SEVERITY_TYPE_HIGH).build();
        ViewTypes less = ViewTypes.builder().subTypeId(4).subTypeName(Constants.OPERATIONS_TYPE_LESSER_THAN).build();
        ViewTypes great = ViewTypes.builder().subTypeId(5).subTypeName(Constants.OPERATIONS_TYPE_GREATER_THAN).build();
        ViewTypes notBetween = ViewTypes.builder().subTypeId(6).subTypeName(Constants.OPERATIONS_TYPE_NOT_BETWEEN).build();

        viewTypesIdMap.put(Constants.THRESHOLD_SEVERITY_TYPE, Arrays.asList(low, med, high));
        viewTypesIdMap.put(Constants.OPERATIONS_TYPE, Arrays.asList(less, great, notBetween));
        metadata.put(Constants.THRESHOLD_SEVERITY_TYPE_LOW, low);
        metadata.put(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM, med);
        metadata.put(Constants.THRESHOLD_SEVERITY_TYPE_HIGH, high);
        metadata.put(Constants.OPERATIONS_TYPE_LESSER_THAN, less);
        metadata.put(Constants.OPERATIONS_TYPE_GREATER_THAN, great);
        metadata.put(Constants.OPERATIONS_TYPE_NOT_BETWEEN, notBetween);
    }

    @Test
    void testServerValidation_ValidInputs() throws Exception {
        List<Integer> instanceIds = Arrays.asList(1, 2);
        int kpiId = 10;
        UtilityBean<InstancesKpisBean> utilityBean = buildUtilityBean(instanceIds, kpiId);

        // Mock view types
        Map<String, List<ViewTypes>> viewTypesIdMap = new HashMap<>();
        Map<String, Object> metadata = new HashMap<>();
        fillViewTypeData(viewTypesIdMap, metadata);
        when(cacheWrapper.getAllViewTypesIdMap()).thenReturn(viewTypesIdMap);

        // Mock account and user
        Account account = Account.builder().id(100).identifier("acc").build();
        when(serverValidationUtils.authKeyValidation("auth")).thenReturn("user1");
        when(serverValidationUtils.accountValidation("acc")).thenReturn(account);

        // Mock kpi
        KpiBean kpiBean = new KpiBean();
        kpiBean.setGroupKpiId(0);
        kpiBean.setDataType("numeric");
        kpiBean.setMeasureUnits("ms");
        when(kpiDao.fetchKpiUsingKpiId(kpiId, 100)).thenReturn(kpiBean);

        // Mock component instances
        ComponentInstanceBean inst1 = new ComponentInstanceBean(); inst1.setId(1); inst1.setIdentifier("i1");
        ComponentInstanceBean inst2 = new ComponentInstanceBean(); inst2.setId(2); inst2.setIdentifier("i2");
        when(compInstanceDao.getComponentInstancesByIdsAndAccountId(instanceIds, 100)).thenReturn(Arrays.asList(inst1, inst2));

        // Mock thresholds
        InstanceKpiAttributeThresholdBean th1 = new InstanceKpiAttributeThresholdBean();
        th1.setCompInstanceId(1); th1.setKpiId(kpiId); th1.setAttributeValue("attr"); th1.setThresholdSeverityId(1); th1.setOperationId(4); th1.setStatus(1); th1.setMaxThreshold(10.0); th1.setMinThreshold(1.0);
        InstanceKpiAttributeThresholdBean th2 = new InstanceKpiAttributeThresholdBean();
        th2.setCompInstanceId(2); th2.setKpiId(kpiId); th2.setAttributeValue("attr"); th2.setThresholdSeverityId(1); th2.setOperationId(4); th2.setStatus(1); th2.setMaxThreshold(10.0); th2.setMinThreshold(1.0);
        when(thresholdDao.getCompInstanceThresholdDetail(100, kpiId, instanceIds)).thenReturn(Arrays.asList(th1, th2));
        metadata.put(Constants.ALL_THRESHOLDS, Arrays.asList(th1, th2));
        utilityBean.setMetadata(metadata);
        // All operation types valid
        when(commonUtils.getViewTypeByNameAndSubType(any(), any(), any())).thenAnswer(invocation -> {
            String type = invocation.getArgument(1);
            String subType = invocation.getArgument(2);
            return viewTypesIdMap.get(type).stream().filter(v -> v.getSubTypeName().equals(subType)).findFirst().orElse(null);
        });

        UtilityBean<InstancesKpisBean> result = getInstanceLevelKpiThresholdBL.serverValidation(utilityBean);

        assertNotNull(result);
        assertTrue(result.getMetadata().containsKey(Constants.THRESHOLD_SEVERITY_TYPE_LOW));
        assertTrue(result.getMetadata().containsKey(Constants.COMP_INSTANCE_ID_VS_IDENTIFIER));
    }

    @Test
    void testServerValidation_KpiBeanNull() throws ServerException {
        List<Integer> instanceIds = List.of(1);
        int kpiId = 10;
        UtilityBean<InstancesKpisBean> utilityBean = buildUtilityBean(instanceIds, kpiId);

        Map<String, List<ViewTypes>> viewTypesIdMap = new HashMap<>();
        Map<String, Object> metadata = new HashMap<>();
        fillViewTypeData(viewTypesIdMap, metadata);
        when(cacheWrapper.getAllViewTypesIdMap()).thenReturn(viewTypesIdMap);

        Account account = Account.builder().id(100).identifier("acc").build();
        when(serverValidationUtils.authKeyValidation("auth")).thenReturn("user1");
        when(serverValidationUtils.accountValidation("acc")).thenReturn(account);

        when(commonUtils.getViewTypeByNameAndSubType(any(), any(), any())).thenReturn((ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_LOW));
        when(kpiDao.fetchKpiUsingKpiId(kpiId, 100)).thenReturn(null);

        ServerException ex = assertThrows(ServerException.class, () ->
                getInstanceLevelKpiThresholdBL.serverValidation(utilityBean));
        assertTrue(ex.getMessage().contains("KPI with ID [10] is unavailable"));
    }

    @Test
    void testServerValidation_MissingLowThresholdSeverityType() throws ServerException {
        List<Integer> instanceIds = List.of(1);
        int kpiId = 10;
        UtilityBean<InstancesKpisBean> utilityBean = buildUtilityBean(instanceIds, kpiId);

        Map<String, List<ViewTypes>> viewTypesIdMap = new HashMap<>();
        Map<String, Object> metadata = new HashMap<>();
        fillViewTypeData(viewTypesIdMap, metadata);
        // Remove low threshold
        List<ViewTypes> thresholdTypes = viewTypesIdMap.get(Constants.THRESHOLD_SEVERITY_TYPE);
        if (thresholdTypes != null) {
            List<ViewTypes> mutableThresholdTypes = new ArrayList<>(thresholdTypes);
            mutableThresholdTypes.removeIf(v -> v.getSubTypeName().equals(Constants.THRESHOLD_SEVERITY_TYPE_LOW));
            viewTypesIdMap.put(Constants.THRESHOLD_SEVERITY_TYPE, mutableThresholdTypes);
        }
        when(cacheWrapper.getAllViewTypesIdMap()).thenReturn(viewTypesIdMap);

        Account account = Account.builder().id(100).identifier("acc").build();
        when(serverValidationUtils.authKeyValidation("auth")).thenReturn("user1");
        when(serverValidationUtils.accountValidation("acc")).thenReturn(account);

        when(commonUtils.getViewTypeByNameAndSubType(any(), any(), any())).thenReturn(null);

        ServerException ex = assertThrows(ServerException.class, () ->
                getInstanceLevelKpiThresholdBL.serverValidation(utilityBean));
        assertTrue(ex.getMessage().contains("Failure in data validation"));
    }

    @Test
    void testServerValidation_MissingMediumThresholdSeverityType() throws ServerException {
        List<Integer> instanceIds = List.of(1);
        int kpiId = 10;
        UtilityBean<InstancesKpisBean> utilityBean = buildUtilityBean(instanceIds, kpiId);

        Map<String, List<ViewTypes>> viewTypesIdMap = new HashMap<>();
        Map<String, Object> metadata = new HashMap<>();
        fillViewTypeData(viewTypesIdMap, metadata);
        // Remove medium threshold
        List<ViewTypes> thresholdTypes = viewTypesIdMap.get(Constants.THRESHOLD_SEVERITY_TYPE);
        if (thresholdTypes != null) {
            List<ViewTypes> mutableThresholdTypes = new ArrayList<>(thresholdTypes);
            Iterator<ViewTypes> iterator = mutableThresholdTypes.iterator();
            while (iterator.hasNext()) {
                ViewTypes v = iterator.next();
                if (v.getSubTypeName().equals(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM)) {
                    iterator.remove();
                }
            }
            viewTypesIdMap.put(Constants.THRESHOLD_SEVERITY_TYPE, mutableThresholdTypes);
        }
        when(cacheWrapper.getAllViewTypesIdMap()).thenReturn(viewTypesIdMap);

        Account account = Account.builder().id(100).identifier("acc").build();
        when(serverValidationUtils.authKeyValidation("auth")).thenReturn("user1");
        when(serverValidationUtils.accountValidation("acc")).thenReturn(account);

        // First call returns low, second call returns null (for medium)
        when(commonUtils.getViewTypeByNameAndSubType(any(), any(), any()))
                .thenReturn((ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_LOW))
                .thenReturn(null);

        ServerException ex = assertThrows(ServerException.class, () ->
                getInstanceLevelKpiThresholdBL.serverValidation(utilityBean));
        assertTrue(ex.getMessage().contains("Failure in data validation"));
    }

    @Test
    void testServerValidation_MissingHighThresholdSeverityType() throws ServerException {
        List<Integer> instanceIds = List.of(1);
        int kpiId = 10;
        UtilityBean<InstancesKpisBean> utilityBean = buildUtilityBean(instanceIds, kpiId);

        Map<String, List<ViewTypes>> viewTypesIdMap = new HashMap<>();
        Map<String, Object> metadata = new HashMap<>();
        fillViewTypeData(viewTypesIdMap, metadata);
        // Remove high threshold
        List<ViewTypes> thresholdTypes = viewTypesIdMap.get(Constants.THRESHOLD_SEVERITY_TYPE);
        if (thresholdTypes != null) {
            List<ViewTypes> mutableThresholdTypes = new ArrayList<>(thresholdTypes);
            mutableThresholdTypes.removeIf(v -> v.getSubTypeName().equals(Constants.THRESHOLD_SEVERITY_TYPE_HIGH));
            viewTypesIdMap.put(Constants.THRESHOLD_SEVERITY_TYPE, mutableThresholdTypes);
        }
        when(cacheWrapper.getAllViewTypesIdMap()).thenReturn(viewTypesIdMap);

        Account account = Account.builder().id(100).identifier("acc").build();
        when(serverValidationUtils.authKeyValidation("auth")).thenReturn("user1");
        when(serverValidationUtils.accountValidation("acc")).thenReturn(account);

        // First two calls return low, medium; third returns null (for high)
        when(commonUtils.getViewTypeByNameAndSubType(any(), any(), any()))
                .thenReturn((ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_LOW))
                .thenReturn((ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM))
                .thenReturn(null);

        ServerException ex = assertThrows(ServerException.class, () ->
                getInstanceLevelKpiThresholdBL.serverValidation(utilityBean));
        assertTrue(ex.getMessage().contains("Failure in data validation"));
    }

    @Test
    void testServerValidation_MissingLessThanOperationType() throws ServerException {
        List<Integer> instanceIds = List.of(1);
        int kpiId = 10;
        UtilityBean<InstancesKpisBean> utilityBean = buildUtilityBean(instanceIds, kpiId);

        Map<String, List<ViewTypes>> viewTypesIdMap = new HashMap<>();
        Map<String, Object> metadata = new HashMap<>();
        fillViewTypeData(viewTypesIdMap, metadata);
        // Remove less than operation
        List<ViewTypes> opTypes = viewTypesIdMap.get(Constants.OPERATIONS_TYPE);
        if (opTypes != null) {
            List<ViewTypes> mutableOpTypes = new ArrayList<>(opTypes);
            mutableOpTypes.removeIf(v -> v.getSubTypeName().equals(Constants.OPERATIONS_TYPE_LESSER_THAN));
            viewTypesIdMap.put(Constants.OPERATIONS_TYPE, mutableOpTypes);
        }
        when(cacheWrapper.getAllViewTypesIdMap()).thenReturn(viewTypesIdMap);

        Account account = Account.builder().id(100).identifier("acc").build();
        when(serverValidationUtils.authKeyValidation("auth")).thenReturn("user1");
        when(serverValidationUtils.accountValidation("acc")).thenReturn(account);

        // First three calls for threshold types, fourth returns null (for less than)
        when(commonUtils.getViewTypeByNameAndSubType(any(), any(), any()))
                .thenReturn((ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_LOW))
                .thenReturn((ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM))
                .thenReturn((ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_HIGH))
                .thenReturn(null);

        ServerException ex = assertThrows(ServerException.class, () ->
                getInstanceLevelKpiThresholdBL.serverValidation(utilityBean));
        assertTrue(ex.getMessage().contains("Failure in data validation"));
    }

    @Test
    void testServerValidation_MissingGreaterThanOperationType() throws ServerException {
        List<Integer> instanceIds = List.of(1);
        int kpiId = 10;
        UtilityBean<InstancesKpisBean> utilityBean = buildUtilityBean(instanceIds, kpiId);

        Map<String, List<ViewTypes>> viewTypesIdMap = new HashMap<>();
        Map<String, Object> metadata = new HashMap<>();
        fillViewTypeData(viewTypesIdMap, metadata);
        // Remove greater than operation
        List<ViewTypes> opTypes = viewTypesIdMap.get(Constants.OPERATIONS_TYPE);
        if (opTypes != null) {
            List<ViewTypes> mutableOpTypes = new ArrayList<>(opTypes);
            mutableOpTypes.removeAll(
                mutableOpTypes.stream()
                    .filter(v -> v.getSubTypeName().equals(Constants.OPERATIONS_TYPE_GREATER_THAN))
                    .toList()
            );
            viewTypesIdMap.put(Constants.OPERATIONS_TYPE, mutableOpTypes);
        }
        when(cacheWrapper.getAllViewTypesIdMap()).thenReturn(viewTypesIdMap);

        Account account = Account.builder().id(100).identifier("acc").build();
        when(serverValidationUtils.authKeyValidation("auth")).thenReturn("user1");
        when(serverValidationUtils.accountValidation("acc")).thenReturn(account);

        // First four calls for threshold types and less than, fifth returns null (for greater than)
        when(commonUtils.getViewTypeByNameAndSubType(any(), any(), any()))
                .thenReturn((ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_LOW))
                .thenReturn((ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM))
                .thenReturn((ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_HIGH))
                .thenReturn((ViewTypes) metadata.get(Constants.OPERATIONS_TYPE_LESSER_THAN))
                .thenReturn(null);

        ServerException ex = assertThrows(ServerException.class, () ->
                getInstanceLevelKpiThresholdBL.serverValidation(utilityBean));
        assertTrue(ex.getMessage().contains("Failure in data validation"));
    }

    @Test
    void testServerValidation_MissingNotBetweenOperationType() throws ServerException {
        List<Integer> instanceIds = List.of(1);
        int kpiId = 10;
        UtilityBean<InstancesKpisBean> utilityBean = buildUtilityBean(instanceIds, kpiId);

        Map<String, List<ViewTypes>> viewTypesIdMap = new HashMap<>();
        Map<String, Object> metadata = new HashMap<>();
        fillViewTypeData(viewTypesIdMap, metadata);
        // Remove not between operation
        List<ViewTypes> opTypes = viewTypesIdMap.get(Constants.OPERATIONS_TYPE);
        if (opTypes != null) {
            opTypes = new ArrayList<>(opTypes);
            opTypes.removeIf(v -> v.getSubTypeName().equals(Constants.OPERATIONS_TYPE_NOT_BETWEEN));
            viewTypesIdMap.put(Constants.OPERATIONS_TYPE, opTypes);
        }
        when(cacheWrapper.getAllViewTypesIdMap()).thenReturn(viewTypesIdMap);

        Account account = Account.builder().id(100).identifier("acc").build();
        when(serverValidationUtils.authKeyValidation("auth")).thenReturn("user1");
        when(serverValidationUtils.accountValidation("acc")).thenReturn(account);

        // First five calls for threshold types and less/greater than, sixth returns null (for not between)
        when(commonUtils.getViewTypeByNameAndSubType(any(), any(), any()))
                .thenReturn((ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_LOW))
                .thenReturn((ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM))
                .thenReturn((ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_HIGH))
                .thenReturn((ViewTypes) metadata.get(Constants.OPERATIONS_TYPE_LESSER_THAN))
                .thenReturn((ViewTypes) metadata.get(Constants.OPERATIONS_TYPE_GREATER_THAN))
                .thenReturn(null);

        when(commonUtils.getViewTypeByNameAndSubType(any(), any(), any()))
                .thenReturn((ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_LOW))
                .thenReturn((ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM))
                .thenReturn((ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_HIGH))
                .thenReturn((ViewTypes) metadata.get(Constants.OPERATIONS_TYPE_LESSER_THAN))
                .thenReturn((ViewTypes) metadata.get(Constants.OPERATIONS_TYPE_GREATER_THAN))
                .thenReturn(null);

        ServerException ex = assertThrows(ServerException.class, () ->
                getInstanceLevelKpiThresholdBL.serverValidation(utilityBean));
        assertTrue(ex.getMessage().contains("Failure in data validation"));
    }

    @Test
    void testServerValidation_ComponentInstanceNotFound() throws ServerException {
        List<Integer> instanceIds = List.of(1);
        int kpiId = 10;
        UtilityBean<InstancesKpisBean> utilityBean = buildUtilityBean(instanceIds, kpiId);

        Map<String, List<ViewTypes>> viewTypesIdMap = new HashMap<>();
        Map<String, Object> metadata = new HashMap<>();
        fillViewTypeData(viewTypesIdMap, metadata);
        when(cacheWrapper.getAllViewTypesIdMap()).thenReturn(viewTypesIdMap);

        Account account = Account.builder().id(100).identifier("acc").build();
        when(serverValidationUtils.authKeyValidation("auth")).thenReturn("user1");
        when(serverValidationUtils.accountValidation("acc")).thenReturn(account);

        KpiBean kpiBean = new KpiBean();
        kpiBean.setGroupKpiId(0);
        kpiBean.setDataType("numeric");
        kpiBean.setMeasureUnits("ms");
        when(kpiDao.fetchKpiUsingKpiId(kpiId, 100)).thenReturn(kpiBean);

        when(compInstanceDao.getComponentInstancesByIdsAndAccountId(instanceIds, 100)).thenReturn(Collections.emptyList());

        when(commonUtils.getViewTypeByNameAndSubType(any(), any(), any())).thenAnswer(invocation -> {
            String type = invocation.getArgument(1);
            String subType = invocation.getArgument(2);
            return viewTypesIdMap.get(type).stream().filter(v -> v.getSubTypeName().equals(subType)).findFirst().orElse(null);
        });

        when(thresholdDao.getCompInstanceThresholdDetail(100, kpiId, instanceIds)).thenReturn(Collections.emptyList());
        metadata.put(Constants.ALL_THRESHOLDS, Collections.emptyList());
        utilityBean.setMetadata(metadata);
        ServerException ex = assertThrows(ServerException.class, () ->
                getInstanceLevelKpiThresholdBL.serverValidation(utilityBean));
        assertTrue(ex.getMessage().contains("Invalid instanceId provided"));
    }

    @Test
    void testServerValidation_InvalidOperationId() throws ServerException {
        List<Integer> instanceIds = List.of(1);
        int kpiId = 10;
        UtilityBean<InstancesKpisBean> utilityBean = buildUtilityBean(instanceIds, kpiId);

        Map<String, List<ViewTypes>> viewTypesIdMap = new HashMap<>();
        Map<String, Object> metadata = new HashMap<>();
        fillViewTypeData(viewTypesIdMap, metadata);
        when(cacheWrapper.getAllViewTypesIdMap()).thenReturn(viewTypesIdMap);

        Account account = Account.builder().id(100).identifier("acc").build();
        when(serverValidationUtils.authKeyValidation("auth")).thenReturn("user1");
        when(serverValidationUtils.accountValidation("acc")).thenReturn(account);

        KpiBean kpiBean = new KpiBean();
        kpiBean.setGroupKpiId(0);
        kpiBean.setDataType("numeric");
        kpiBean.setMeasureUnits("ms");
        when(kpiDao.fetchKpiUsingKpiId(kpiId, 100)).thenReturn(kpiBean);

        ComponentInstanceBean inst1 = new ComponentInstanceBean(); inst1.setId(1); inst1.setIdentifier("i1");
        when(compInstanceDao.getComponentInstancesByIdsAndAccountId(instanceIds, 100)).thenReturn(List.of(inst1));

        // Threshold with invalid operationId (not present in viewSubTypeIdMap)
        InstanceKpiAttributeThresholdBean th1 = new InstanceKpiAttributeThresholdBean();
        th1.setCompInstanceId(1); th1.setKpiId(kpiId); th1.setAttributeValue("attr"); th1.setThresholdSeverityId(1); th1.setOperationId(999); th1.setStatus(1); th1.setMaxThreshold(10.0); th1.setMinThreshold(1.0);
        when(thresholdDao.getCompInstanceThresholdDetail(100, kpiId, instanceIds)).thenReturn(List.of(th1));
        metadata.put(Constants.ALL_THRESHOLDS, List.of(th1));
        utilityBean.setMetadata(metadata);
        when(commonUtils.getViewTypeByNameAndSubType(any(), any(), any())).thenAnswer(invocation -> {
            String type = invocation.getArgument(1);
            String subType = invocation.getArgument(2);
            return viewTypesIdMap.get(type).stream().filter(v -> v.getSubTypeName().equals(subType)).findFirst().orElse(null);
        });

        ServerException ex = assertThrows(ServerException.class, () ->
                getInstanceLevelKpiThresholdBL.serverValidation(utilityBean));
        assertTrue(ex.getMessage().contains("KPI(s) have invalid operationId"));
    }

    // process test cases
    private UtilityBean<InstancesKpisBean> buildUtilityBean(List<Integer> instanceIds, int kpiId, Map<String, Object> metadata) {
        InstancesKpisBean bean = InstancesKpisBean.builder().instanceIds(instanceIds).kpiId(kpiId).groupKpiId(0).dataType("numeric")
                .unit("ms").discovery(0).build();
        return UtilityBean.<InstancesKpisBean>builder().pojoObject(bean).requestParams(new HashMap<>()).metadata(metadata).build();
    }

    private void fillViewTypeData(Map<String, Object> metadata) {
        ViewTypes low = new ViewTypes(); low.setSubTypeId(1); low.setSubTypeName(Constants.THRESHOLD_SEVERITY_TYPE_LOW);
        ViewTypes med = new ViewTypes(); med.setSubTypeId(2); med.setSubTypeName(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
        ViewTypes high = new ViewTypes(); high.setSubTypeId(3); high.setSubTypeName(Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
        ViewTypes less = new ViewTypes(); less.setSubTypeId(5); less.setSubTypeName(Constants.OPERATIONS_TYPE_LESSER_THAN);
        ViewTypes great = new ViewTypes(); great.setSubTypeId(6); great.setSubTypeName(Constants.OPERATIONS_TYPE_GREATER_THAN);
        ViewTypes notBetween = new ViewTypes(); notBetween.setSubTypeId(7); notBetween.setSubTypeName(Constants.OPERATIONS_TYPE_NOT_BETWEEN);

        metadata.put(Constants.THRESHOLD_SEVERITY_TYPE_LOW, low);
        metadata.put(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM, med);
        metadata.put(Constants.THRESHOLD_SEVERITY_TYPE_HIGH, high);
        metadata.put(Constants.OPERATIONS_TYPE_LESSER_THAN, less);
        metadata.put(Constants.OPERATIONS_TYPE_GREATER_THAN, great);
        metadata.put(Constants.OPERATIONS_TYPE_NOT_BETWEEN, notBetween);

        Account account = Account.builder().id(100).identifier("acc").build();
        metadata.put(Constants.ACCOUNT, account);

        Map<Integer, String> compInstanceIdVsIdentifier = new HashMap<>();
        compInstanceIdVsIdentifier.put(1, "i1");
        metadata.put(Constants.COMP_INSTANCE_ID_VS_IDENTIFIER, compInstanceIdVsIdentifier);
    }

    @Test
    void testProcess_SingleInstance_Normal() throws Exception {
        Map<String, Object> metadata = new HashMap<>();
        fillViewTypeData(metadata);
        List<Integer> instanceIds = List.of(1);
        int kpiId = 10;
        UtilityBean<InstancesKpisBean> utilityBean = buildUtilityBean(instanceIds, kpiId, metadata);

        // Mock threshold beans for single instance, all severities
        InstanceKpiAttributeThresholdBean thLow = InstanceKpiAttributeThresholdBean.builder()
            .compInstanceId(1).kpiId(kpiId).attributeValue("attr").thresholdSeverityId(1).operationId(5)
            .status(1).maxThreshold(10.0).minThreshold(1.0).build();

        InstanceKpiAttributeThresholdBean thMed = InstanceKpiAttributeThresholdBean.builder()
            .compInstanceId(1).kpiId(kpiId).attributeValue("attr").thresholdSeverityId(2).operationId(5)
            .status(1).maxThreshold(20.0).minThreshold(2.0).build();

        InstanceKpiAttributeThresholdBean thHigh = InstanceKpiAttributeThresholdBean.builder()
            .compInstanceId(1).kpiId(kpiId).attributeValue("attr").thresholdSeverityId(3).operationId(5)
            .status(1).maxThreshold(30.0).minThreshold(3.0).build();

        when(thresholdDao.getCompInstanceThresholdDetail(100, kpiId, instanceIds))
            .thenReturn(List.of(thLow, thMed, thHigh));
        metadata.put(Constants.ALL_THRESHOLDS, List.of(thLow, thMed, thHigh));
        utilityBean.setMetadata(metadata);
        when(compInstanceDao.getGroupKpiListForCompInstance(0)).thenReturn(Collections.emptyList());

        // Return a non-empty list for non-group KPI mapping
        CompInstanceKpiGroupDetailsBean kpiGroupDetails = CompInstanceKpiGroupDetailsBean.builder()
            .compInstanceId(1).mstKpiDetailsId(kpiId).attributeValue("attr").build();
        when(compInstanceDao.getNonGroupKpiListForCompInstance(kpiId)).thenReturn(List.of(kpiGroupDetails));

        when(collatedKpiRepo.getGroupKpiAttributesWithDataCollected(anyString(), anySet(), anyLong(), anyInt()))
            .thenReturn(Collections.emptyMap());

        KpiAttrThresholdInfo result = getInstanceLevelKpiThresholdBL.process(utilityBean);

        assertNotNull(result);
        assertTrue(result.getAttributes().contains("attr"));
        assertEquals(1, result.getThresholds().size());
        KpiAttributeThresholdInfo info = result.getThresholds().iterator().next();
        assertEquals("attr", info.getAttributeValue());
        assertNotNull(info.getLowThreshold());
        assertNotNull(info.getMediumThreshold());
        assertNotNull(info.getHighThreshold());
    }

    @Test
    void testProcess_MultiInstance_GroupKpi_Discovery() throws Exception {
        Map<String, Object> metadata = new HashMap<>();
        fillViewTypeData(metadata);
        List<Integer> instanceIds = List.of(1, 2);
        int kpiId = 10;
        UtilityBean<InstancesKpisBean> utilityBean = buildUtilityBean(instanceIds, kpiId, metadata);
        utilityBean.getPojoObject().setGroupKpiId(20);
        utilityBean.getPojoObject().setDiscovery(1);

        // Group mapped KPIs
        CompInstanceKpiGroupDetailsBean groupKpi1 = CompInstanceKpiGroupDetailsBean.builder()
                .compInstanceId(1).mstKpiGroupId(20).mstKpiDetailsId(kpiId).attributeValue("attr1").build();
        CompInstanceKpiGroupDetailsBean groupKpi2 = CompInstanceKpiGroupDetailsBean.builder()
                .compInstanceId(2).mstKpiGroupId(20).mstKpiDetailsId(kpiId).attributeValue("attr2").build();
        when(compInstanceDao.getGroupKpiListForCompInstance(20)).thenReturn(List.of(groupKpi1, groupKpi2));
        when(compInstanceDao.getNonGroupKpiListForCompInstance(kpiId)).thenReturn(Collections.emptyList());

        // Discovery attributes
        Set<String> attrs1 = new HashSet<>(List.of("attr1"));
        Set<String> attrs2 = new HashSet<>(List.of("attr2"));
        Map<String, Set<String>> groupAttrs = new HashMap<>();
        groupAttrs.put("i1", attrs1);
        groupAttrs.put("i2", attrs2);
        when(collatedKpiRepo.getGroupKpiAttributesWithDataCollected(anyString(), anySet(), anyLong(), anyInt()))
                .thenReturn(groupAttrs);

        // Thresholds
        InstanceKpiAttributeThresholdBean th1 = InstanceKpiAttributeThresholdBean.builder()
            .compInstanceId(1).kpiId(kpiId).attributeValue("attr1").thresholdSeverityId(1)
            .operationId(5).status(1).maxThreshold(10.0).minThreshold(1.0).build();

        InstanceKpiAttributeThresholdBean th2 = InstanceKpiAttributeThresholdBean.builder()
            .compInstanceId(2).kpiId(kpiId).attributeValue("attr2").thresholdSeverityId(2)
            .operationId(5).status(1).maxThreshold(20.0).minThreshold(2.0).build();

        when(thresholdDao.getCompInstanceThresholdDetail(100, kpiId, instanceIds))
                .thenReturn(List.of(th1, th2));
        metadata.put(Constants.ALL_THRESHOLDS, List.of(th1, th2));
        utilityBean.setMetadata(metadata);

        Map<Integer, String> compInstanceIdVsIdentifier = new HashMap<>();
        compInstanceIdVsIdentifier.put(1, "i1");
        compInstanceIdVsIdentifier.put(2, "i2");
        metadata.put(Constants.COMP_INSTANCE_ID_VS_IDENTIFIER, compInstanceIdVsIdentifier);

        KpiAttrThresholdInfo result = getInstanceLevelKpiThresholdBL.process(utilityBean);

        assertNotNull(result);
        assertTrue(result.getAttributes().contains("attr1"));
        assertTrue(result.getAttributes().contains("attr2"));
        assertFalse(result.getThresholds().isEmpty());
    }

    @Test
    void testProcess_MultiInstance_NonGroupKpi_MissingThresholds() throws Exception {
        Map<String, Object> metadata = new HashMap<>();
        fillViewTypeData(metadata);
        List<Integer> instanceIds = List.of(1, 2);
        int kpiId = 10;
        UtilityBean<InstancesKpisBean> utilityBean = buildUtilityBean(instanceIds, kpiId, metadata);

        // Non-group mapped KPIs
        CompInstanceKpiGroupDetailsBean nonGroupKpi1 = CompInstanceKpiGroupDetailsBean.builder()
                .compInstanceId(1).mstKpiDetailsId(kpiId).attributeValue("attr1").build();
        CompInstanceKpiGroupDetailsBean nonGroupKpi2 = CompInstanceKpiGroupDetailsBean.builder()
                .compInstanceId(2).mstKpiDetailsId(kpiId).attributeValue("attr2").build();
        when(compInstanceDao.getGroupKpiListForCompInstance(0)).thenReturn(Collections.emptyList());
        when(compInstanceDao.getNonGroupKpiListForCompInstance(kpiId)).thenReturn(List.of(nonGroupKpi1, nonGroupKpi2));

        // Thresholds: only for one attribute, missing for another
        InstanceKpiAttributeThresholdBean th1 = new InstanceKpiAttributeThresholdBean();
        th1.setCompInstanceId(1); th1.setKpiId(kpiId); th1.setAttributeValue("attr1");
        th1.setThresholdSeverityId(1); th1.setOperationId(5); th1.setStatus(1);
        th1.setMaxThreshold(10.0); th1.setMinThreshold(1.0);

        when(thresholdDao.getCompInstanceThresholdDetail(100, kpiId, instanceIds))
                .thenReturn(List.of(th1));
        metadata.put(Constants.ALL_THRESHOLDS, List.of(th1));
        utilityBean.setMetadata(metadata);

        when(collatedKpiRepo.getGroupKpiAttributesWithDataCollected(anyString(), anySet(), anyLong(), anyInt()))
                .thenReturn(Collections.emptyMap());

        KpiAttrThresholdInfo result = getInstanceLevelKpiThresholdBL.process(utilityBean);

        assertNotNull(result);
        assertTrue(result.getAttributes().contains("attr1"));
        assertFalse(result.getAttributes().contains("attr2"));
        assertFalse(result.getThresholds().isEmpty());
    }

    @Test
    void testProcess_EmptyThresholds() throws Exception {
        Map<String, Object> metadata = new HashMap<>();
        fillViewTypeData(metadata);
        List<Integer> instanceIds = List.of(1);
        int kpiId = 10;
        UtilityBean<InstancesKpisBean> utilityBean = buildUtilityBean(instanceIds, kpiId, metadata);

        when(thresholdDao.getCompInstanceThresholdDetail(100, kpiId, instanceIds))
                .thenReturn(Collections.emptyList());
        metadata.put(Constants.ALL_THRESHOLDS, Collections.emptyList());
        utilityBean.setMetadata(metadata);
        when(compInstanceDao.getGroupKpiListForCompInstance(0)).thenReturn(Collections.emptyList());
        when(compInstanceDao.getNonGroupKpiListForCompInstance(kpiId)).thenReturn(Collections.emptyList());
        when(collatedKpiRepo.getGroupKpiAttributesWithDataCollected(anyString(), anySet(), anyLong(), anyInt()))
                .thenReturn(Collections.emptyMap());

        KpiAttrThresholdInfo result = getInstanceLevelKpiThresholdBL.process(utilityBean);

        assertNotNull(result);
        assertTrue(result.getAttributes().isEmpty());
        assertTrue(result.getThresholds().isEmpty());
    }

    @Test
    void testProcess_GroupKpi_AddsAllAttributeWhenMissing() throws Exception {
        Map<String, Object> metadata = new HashMap<>();
        fillViewTypeData(metadata);
        List<Integer> instanceIds = List.of(1);
        int kpiId = 10;
        int groupKpiId = 20;
        UtilityBean<InstancesKpisBean> utilityBean = buildUtilityBean(instanceIds, kpiId, metadata);
        utilityBean.getPojoObject().setGroupKpiId(groupKpiId);

        // Group mapped KPIs: no attributeValue == Constants.ALL
        CompInstanceKpiGroupDetailsBean groupKpi = CompInstanceKpiGroupDetailsBean.builder()
                .compInstanceId(1).mstKpiDetailsId(kpiId).attributeValue("attr1").mstProducerKpiMappingId(100)
                .collectionInterval(60).kpiGroupName("group").mstKpiGroupId(groupKpiId).mstProducerId(200)
                .notification(1).build();
        when(compInstanceDao.getGroupKpiListForCompInstance(groupKpiId)).thenReturn(List.of(groupKpi));
        when(compInstanceDao.getNonGroupKpiListForCompInstance(kpiId)).thenReturn(Collections.emptyList());

        // No discovery
        utilityBean.getPojoObject().setDiscovery(1);

        // Thresholds
        InstanceKpiAttributeThresholdBean th = InstanceKpiAttributeThresholdBean.builder()
                .compInstanceId(1).kpiId(kpiId).attributeValue("attr1").thresholdSeverityId(1).operationId(5)
                .status(1).maxThreshold(10.0).minThreshold(1.0).build();

        when(thresholdDao.getCompInstanceThresholdDetail(100, kpiId, instanceIds)).thenReturn(List.of(th));
        metadata.put(Constants.ALL_THRESHOLDS, List.of(th));
        utilityBean.setMetadata(metadata);
        when(collatedKpiRepo.getGroupKpiAttributesWithDataCollected(anyString(), anySet(), anyLong(), anyInt()))
                .thenReturn(Collections.emptyMap());

        KpiAttrThresholdInfo result = getInstanceLevelKpiThresholdBL.process(utilityBean);

        assertNotNull(result);
        // Should also contain the original attribute
        assertTrue(result.getAttributes().contains("attr1"));
    }

}