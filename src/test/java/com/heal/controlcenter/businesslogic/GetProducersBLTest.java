package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.beans.ProducerValidationBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.ProducersDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.GetProducerPojo;
import com.heal.controlcenter.pojo.ProducerDetailsPojo;
import com.heal.controlcenter.pojo.ProducerKPIMappingDetailsPojo;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.DateTimeUtil;
import com.heal.controlcenter.util.ServerValidationUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class GetProducersBLTest {

    private ProducersDao producersDao;
    private ClientValidationUtils clientValidationUtils;
    private ServerValidationUtils serverValidationUtils;
    private GetProducersBL getProducersBL;
    private MockedStatic<DateTimeUtil> dateTimeUtilMock;

    @BeforeEach
    void setUp() {
        producersDao = mock(ProducersDao.class);
        clientValidationUtils = mock(ClientValidationUtils.class);
        serverValidationUtils = mock(ServerValidationUtils.class);
        getProducersBL = new GetProducersBL(producersDao, clientValidationUtils, serverValidationUtils);
        dateTimeUtilMock = mockStatic(DateTimeUtil.class);
    }

    @AfterEach
    void tearDown() {
        dateTimeUtilMock.close();
    }

    @Test
    void testClientValidation_success() throws ClientException {
        String[] params = {"acc", "name", "status", "type", "1"};
        UtilityBean<String> bean = getProducersBL.clientValidation(null, params);
        assertNotNull(bean);
        assertEquals("acc", bean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
        assertEquals("1", bean.getRequestParams().get("isCustom"));
        assertNotNull(bean.getMetadata());
    }

    @Test
    void testClientValidation_invalidAccount() throws ClientException {
        doThrow(new ClientException("Invalid")).when(clientValidationUtils).accountIdentifierValidation("bad");
        assertThrows(ClientException.class, () -> getProducersBL.clientValidation(null, "bad", "n", "s", "t", "1"));
    }

    @Test
    void testServerValidation_missingIsCustom() {
        UtilityBean<String> bean = UtilityBean.<String>builder()
                .requestParams(Collections.singletonMap(Constants.ACCOUNT_IDENTIFIER, "acc"))
                .metadata(Collections.emptyMap())
                .build();
        assertThrows(ServerException.class, () -> getProducersBL.serverValidation(bean), "isCustom parameter is required.");
    }

    @Test
    void testServerValidation_invalidIsCustom_nonInteger() throws ServerException {
        UtilityBean<String> bean = UtilityBean.<String>builder()
                .requestParams(new java.util.HashMap<>() {{
                    put(Constants.ACCOUNT_IDENTIFIER, "acc");
                    put("isCustom", "abc");
                }})
                .metadata(Collections.emptyMap())
                .build();
        when(serverValidationUtils.accountValidation(anyString())).thenReturn(new Account());
        assertThrows(ServerException.class, () -> getProducersBL.serverValidation(bean), "isCustom parameter must be an integer.");
    }

    @Test
    void testServerValidation_invalidIsCustom_wrongValue() throws ServerException {
        UtilityBean<String> bean = UtilityBean.<String>builder()
                .requestParams(new java.util.HashMap<>() {{
                    put(Constants.ACCOUNT_IDENTIFIER, "acc");
                    put("isCustom", "2");
                }})
                .metadata(Collections.emptyMap())
                .build();
        when(serverValidationUtils.accountValidation(anyString())).thenReturn(new Account());
        assertThrows(ServerException.class, () -> getProducersBL.serverValidation(bean), "isCustom parameter must be 0 or 1.");
    }

    @Test
    void testServerValidation_success() throws ServerException {
        Account account = Account.builder().id(1).name("acc").build();
        when(serverValidationUtils.accountValidation("acc")).thenReturn(account);
        UtilityBean<String> bean = UtilityBean.<String>builder()
                .requestParams(
                        new java.util.HashMap<>() {{
                            put(Constants.ACCOUNT_IDENTIFIER, "acc");
                            put("isCustom", "1");
                        }}
                )
                .metadata(Collections.emptyMap())
                .build();
        UtilityBean<ProducerValidationBean> result = getProducersBL.serverValidation(bean);
        assertNotNull(result.getPojoObject());
        assertEquals(account, result.getPojoObject().getAccount());
        assertEquals(1, result.getPojoObject().getIsCustom());
    }

    @Test
    void testProcess_emptyList() throws DataProcessingException {
        ProducerValidationBean validationBean = ProducerValidationBean.builder()
                .account(Account.builder().id(1).name("acc").build())
                .isCustom(1)
                .build();
        UtilityBean<ProducerValidationBean> bean = UtilityBean.<ProducerValidationBean>builder()
                .pojoObject(validationBean)
                .pageable(PageRequest.of(0, 10))
                .build();
        when(producersDao.getProducersCount(anyInt(), any(), any(), any(), anyInt())).thenReturn(0);
        when(producersDao.getProducerDetailsWithAccId(anyInt(), any(), any(), any(), any(), anyInt(), any())).thenReturn(Collections.emptyList());
        Page<GetProducerPojo> page = getProducersBL.process(bean);
        assertTrue(page.isEmpty());
    }

    @Test
    void testProcess_withData() throws DataProcessingException {
        ProducerValidationBean validationBean = ProducerValidationBean.builder()
                .account(Account.builder().id(1).name("acc").build())
                .isCustom(1)
                .build();
        UtilityBean<ProducerValidationBean> bean = UtilityBean.<ProducerValidationBean>builder()
                .pojoObject(validationBean)
                .pageable(PageRequest.of(0, 10))
                .build();
        LocalDateTime now = LocalDateTime.now();
        ProducerDetailsPojo details = ProducerDetailsPojo.builder()
                .id(1).name("p1").description("desc").producerTypeName("type")
                .isCustom(1).status(1).isKpiGroup(1)
                .createdTime(Timestamp.valueOf(now)).updatedTime(Timestamp.valueOf(now))
                .userName("user").build();
        when(producersDao.getProducersCount(anyInt(), any(), any(), any(), anyInt())).thenReturn(1);
        when(producersDao.getProducerDetailsWithAccId(anyInt(), any(), any(), any(), any(), anyInt(), any()))
                .thenReturn(List.of(details));
        when(producersDao.getProducerKPIMappingDetails(anyInt(), any())).thenReturn(Collections.emptyList());
        dateTimeUtilMock.when(() -> DateTimeUtil.getGMTToEpochTime(anyString())).thenReturn(12345L);

        Page<GetProducerPojo> page = getProducersBL.process(bean);

        assertEquals(1, page.getTotalElements());
        assertEquals("p1", page.getContent().get(0).getName());
        assertFalse(page.getContent().get(0).isKpiMapped());
    }

    @Test
    void testProcess_withKpiMapping() throws DataProcessingException {
        ProducerValidationBean validationBean = ProducerValidationBean.builder()
                .account(Account.builder().id(1).name("acc").build())
                .isCustom(1)
                .build();
        UtilityBean<ProducerValidationBean> bean = UtilityBean.<ProducerValidationBean>builder()
                .pojoObject(validationBean)
                .pageable(PageRequest.of(0, 10))
                .build();
        LocalDateTime now = LocalDateTime.now();
        ProducerDetailsPojo details = ProducerDetailsPojo.builder()
                .id(1).name("p1").description("desc").producerTypeName("type")
                .isCustom(1).status(1).isKpiGroup(1)
                .createdTime(Timestamp.valueOf(now)).updatedTime(Timestamp.valueOf(now))
                .userName("user").build();
        ProducerKPIMappingDetailsPojo kpiMapping = new ProducerKPIMappingDetailsPojo();
        kpiMapping.setProducerId(1);
        when(producersDao.getProducersCount(anyInt(), any(), any(), any(), anyInt())).thenReturn(1);
        when(producersDao.getProducerDetailsWithAccId(anyInt(), any(), any(), any(), any(), anyInt(), any()))
                .thenReturn(List.of(details));
        when(producersDao.getProducerKPIMappingDetails(anyInt(), any())).thenReturn(List.of(kpiMapping));
        dateTimeUtilMock.when(() -> DateTimeUtil.getGMTToEpochTime(anyString())).thenReturn(12345L);

        Page<GetProducerPojo> page = getProducersBL.process(bean);

        assertEquals(1, page.getTotalElements());
        assertTrue(page.getContent().get(0).isKpiMapped());
    }

    @Test
    void testProcess_daoException() {
        ProducerValidationBean validationBean = ProducerValidationBean.builder()
                .account(Account.builder().id(1).name("acc").build())
                .isCustom(1)
                .build();
        UtilityBean<ProducerValidationBean> bean = UtilityBean.<ProducerValidationBean>builder()
                .pojoObject(validationBean)
                .pageable(PageRequest.of(0, 10))
                .build();
        when(producersDao.getProducersCount(anyInt(), any(), any(), any(), anyInt())).thenThrow(new RuntimeException("DB Error"));

        assertThrows(DataProcessingException.class, () -> getProducersBL.process(bean));
    }
}