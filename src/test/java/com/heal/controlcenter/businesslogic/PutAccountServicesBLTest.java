package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.*;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountServiceDao;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.redis.AccountRepo;
import com.heal.controlcenter.dao.redis.ApplicationRepo;
import com.heal.controlcenter.dao.redis.MasterDataRepo;
import com.heal.controlcenter.dao.redis.ServiceRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.LinkedEnvironmentPojo;
import com.heal.controlcenter.pojo.RulesHelperPojo;
import com.heal.controlcenter.pojo.ServiceGroupPojo;
import com.heal.controlcenter.pojo.ServicePojo;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.ServerValidationUtils;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PutAccountServicesBLTest {

    @Spy
    @InjectMocks
    private PutAccountServicesBL putAccountServicesBL;

    @Mock
    private AccountServiceDao accountServiceDao;
    @Mock
    private ControllerDao controllerDao;
    @Mock
    private ServerValidationUtils serverValidationUtils;
    @Mock
    private ControllerBL controllerBL;
    @Mock
    private TagMappingBL tagMappingBL;
    @Mock
    private MasterDataDao masterDataDao;
    @Mock
    private ServiceRepo serviceRepo;
    @Mock
    private AccountRepo accountRepo;
    @Mock
    private MasterDataRepo masterDataRepo;
    @Mock
    private ApplicationRepo applicationRepo;

    private ServicePojo servicePojo;
    private List<ServicePojo> servicePojos;
    private Account account;
    private UtilityBean<List<ServicePojo>> utilityBean;
    private ServiceBean serviceBean;
    private ControllerBean existingController;

    @BeforeEach
    void setUp() {
        servicePojo = new ServicePojo();
        servicePojo.setIdentifier("test-service-id");
        servicePojo.setName("Test Service");
        servicePojo.setAppIdentifiers(Collections.singletonList("app-id"));
        servicePojo.setType("REST-API");
        servicePojo.setLayer("Application Tier");
        servicePojo.setEnvironment("DC");
        servicePojo.setIsEntryPointService((short) 1);

        servicePojos = Collections.singletonList(servicePojo);

        account = new Account();
        account.setId(1);
        account.setIdentifier("test-account");

        utilityBean = UtilityBean.<List<ServicePojo>>builder()
                .pojoObject(servicePojos)
                .requestParams(new HashMap<String, String>() {{ put(Constants.ACCOUNT_IDENTIFIER, "test-account"); }})
                .metadata(new HashMap<String, Object>() {{ put(Constants.USER_ID_KEY, "test-user"); }})
                .build();

        existingController = new ControllerBean();
        existingController.setId(101);
        existingController.setIdentifier("test-service-id_DC");
        existingController.setName("Test Service_DC");
        existingController.setAccountId(1);
        existingController.setType("REST-API");
        existingController.setLayer("Application Tier");
        existingController.setEnvironment("DC");
        existingController.setStatus(1);

        serviceBean = ServiceBean.builder()
                .id(101)
                .name("Test Service_DC")
                .identifier("test-service-id_DC")
                .accountId(1)
                .accountIdentifier("test-account")
                .userId("test-user")
                .appIds(Collections.singletonList(201))
                .type("REST-API")
                .layer("Application Tier")
                .environment("DC")
                .status(1)
                .entryPointService(true)
                .build();
    }

    // clientValidation tests
    @Test
    @DisplayName("clientValidation: Should successfully validate client input")
    void clientValidation_Success() throws ClientException {
        UtilityBean<List<ServicePojo>> result = putAccountServicesBL.clientValidation(servicePojos, "test-account");
        assertNotNull(result);
        assertEquals(servicePojos, result.getPojoObject());
        assertEquals("test-account", result.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
    }

    @Test
    @DisplayName("clientValidation: Should throw ClientException if service payload is null")
    void clientValidation_NullPayload_ThrowsClientException() {
        ClientException exception = assertThrows(ClientException.class, () -> putAccountServicesBL.clientValidation(null, "test-account"));
        assertTrue(exception.getMessage().contains("Service payload cannot be null or empty"));
    }

    @Test
    @DisplayName("clientValidation: Should throw ClientException if service payload is empty")
    void clientValidation_EmptyPayload_ThrowsClientException() {
        ClientException exception = assertThrows(ClientException.class, () -> putAccountServicesBL.clientValidation(Collections.emptyList(), "test-account"));
        assertTrue(exception.getMessage().contains("Service payload cannot be null or empty"));
    }

    @Test
    @DisplayName("clientValidation: Should throw ClientException if service identifier is null in payload")
    void clientValidation_NullServiceIdentifier_ThrowsClientException() {
        servicePojo.setIdentifier(null);
        ClientException exception = assertThrows(ClientException.class, () -> putAccountServicesBL.clientValidation(servicePojos, "test-account"));
        assertTrue(exception.getMessage().contains("Service identifier cannot be null or empty in the payload"));
    }

    @Test
    @DisplayName("clientValidation: Should throw ClientException if service identifier is empty in payload")
    void clientValidation_EmptyServiceIdentifier_ThrowsClientException() {
        servicePojo.setIdentifier("");
        ClientException exception = assertThrows(ClientException.class, () -> putAccountServicesBL.clientValidation(servicePojos, "test-account"));
        assertTrue(exception.getMessage().contains("Service identifier cannot be null or empty in the payload"));
    }

    @Test
    @DisplayName("clientValidation: Should throw ClientException if account identifier is null")
    void clientValidation_NullAccountIdentifier_ThrowsClientException() {
        ClientException exception = assertThrows(ClientException.class, () -> putAccountServicesBL.clientValidation(servicePojos, (String) null));
        assertTrue(exception.getMessage().contains("Account identifier cannot be null or empty"));
    }

    @Test
    @DisplayName("clientValidation: Should throw ClientException if account identifier is empty")
    void clientValidation_EmptyAccountIdentifier_ThrowsClientException() {
        ClientException exception = assertThrows(ClientException.class, () -> putAccountServicesBL.clientValidation(servicePojos, ""));
        assertTrue(exception.getMessage().contains("Account identifier cannot be null or empty"));
    }

    // serverValidation tests
    @Test
    @DisplayName("serverValidation: Should successfully validate server input")
    void serverValidation_Success() throws ServerException, HealControlCenterException {
        ControllerBean appController = new ControllerBean();
        appController.setId(201);

        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);
        when(controllerDao.getControllerByIdentifierOrName(eq("test-service-id"), isNull())).thenReturn(existingController);
        when(controllerDao.getControllerByIdentifierOrName(eq("app-id"), isNull())).thenReturn(appController);

        UtilityBean<List<ServiceBean>> result = putAccountServicesBL.serverValidation(utilityBean);

        assertNotNull(result);
        assertEquals(1, result.getPojoObject().size());
        ServiceBean resultBean = result.getPojoObject().get(0);
        assertEquals("Test Service_DC", resultBean.getName());
        assertEquals("test-service-id_DC", resultBean.getIdentifier());
        assertEquals("DC", resultBean.getEnvironment());
        assertEquals(201, resultBean.getAppIds().get(0));
    }

    @Test
    @DisplayName("serverValidation: Should handle null appIdentifiers gracefully")
    void serverValidation_NullAppIdentifiers_Success() throws ServerException, HealControlCenterException {
        servicePojo.setAppIdentifiers(null);
        utilityBean.setPojoObject(Collections.singletonList(servicePojo));

        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);
        when(controllerDao.getControllerByIdentifierOrName(eq("test-service-id"), isNull())).thenReturn(existingController);

        UtilityBean<List<ServiceBean>> result = putAccountServicesBL.serverValidation(utilityBean);
        assertNotNull(result);
        assertTrue(result.getPojoObject().get(0).getAppIds().isEmpty());
    }

    @Test
    @DisplayName("serverValidation: Should handle empty appIdentifiers gracefully")
    void serverValidation_EmptyAppIdentifiers_Success() throws ServerException, HealControlCenterException {
        servicePojo.setAppIdentifiers(Collections.emptyList());
        utilityBean.setPojoObject(Collections.singletonList(servicePojo));

        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);
        when(controllerDao.getControllerByIdentifierOrName(eq("test-service-id"), isNull())).thenReturn(existingController);

        UtilityBean<List<ServiceBean>> result = putAccountServicesBL.serverValidation(utilityBean);
        assertNotNull(result);
        assertTrue(result.getPojoObject().get(0).getAppIds().isEmpty());
    }

    @Test
    @DisplayName("serverValidation: Should correctly set environment suffix for name and identifier")
    void serverValidation_EnvironmentSuffix_Success() throws ServerException, HealControlCenterException {
        servicePojo.setEnvironment("DR");
        servicePojo.setIdentifier("test-service-id"); // Ensure original identifier is without suffix
        servicePojo.setName("Test Service"); // Ensure original name is without suffix
        utilityBean.setPojoObject(Collections.singletonList(servicePojo));

        existingController.setIdentifier("test-service-id"); // Original identifier without suffix
        existingController.setName("Test Service"); // Original name without suffix
        existingController.setEnvironment("DR"); // Existing environment

        ControllerBean appController = new ControllerBean();
        appController.setId(201);

        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);
        when(controllerDao.getControllerByIdentifierOrName(eq("test-service-id"), isNull())).thenReturn(existingController);
        when(controllerDao.getControllerByIdentifierOrName(eq("app-id"), isNull())).thenReturn(appController);

        UtilityBean<List<ServiceBean>> result = putAccountServicesBL.serverValidation(utilityBean);

        assertNotNull(result);
        ServiceBean resultBean = result.getPojoObject().get(0);
        assertEquals("Test Service", resultBean.getName());
        assertEquals("test-service-id", resultBean.getIdentifier());
        assertEquals("DR", resultBean.getEnvironment());
    }

    @Test
    @DisplayName("serverValidation: Should use existing controller's type and layer if not provided in payload")
    void serverValidation_UseExistingTypeAndLayer_Success() throws ServerException, HealControlCenterException {
        servicePojo.setType(null);
        servicePojo.setLayer(null);
        utilityBean.setPojoObject(Collections.singletonList(servicePojo));

        existingController.setType("EXISTING_TYPE");
        existingController.setLayer("EXISTING_LAYER");

        ControllerBean appController = new ControllerBean();
        appController.setId(201);

        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);
        when(controllerDao.getControllerByIdentifierOrName(eq("test-service-id"), isNull())).thenReturn(existingController);
        when(controllerDao.getControllerByIdentifierOrName(eq("app-id"), isNull())).thenReturn(appController);

        UtilityBean<List<ServiceBean>> result = putAccountServicesBL.serverValidation(utilityBean);

        assertNotNull(result);
        ServiceBean resultBean = result.getPojoObject().get(0);
        assertEquals("EXISTING_TYPE", resultBean.getType());
        assertEquals("EXISTING_LAYER", resultBean.getLayer());
    }

    @Test
    @DisplayName("serverValidation: Should correctly populate linkedEnvironment details")
    void serverValidation_LinkedEnvironmentDetails_Success() throws ServerException, HealControlCenterException {
        servicePojo.setLinkedEnvironment(new LinkedEnvironmentPojo("DR", "linked-account", "linked-service-id"));
        utilityBean.setPojoObject(Collections.singletonList(servicePojo));

        ControllerBean appController = new ControllerBean();
        appController.setId(201);

        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);
        when(controllerDao.getControllerByIdentifierOrName(eq("test-service-id"), isNull())).thenReturn(existingController);
        when(controllerDao.getControllerByIdentifierOrName(eq("app-id"), isNull())).thenReturn(appController);

        UtilityBean<List<ServiceBean>> result = putAccountServicesBL.serverValidation(utilityBean);

        assertNotNull(result);
        ServiceBean resultBean = result.getPojoObject().get(0);
        assertEquals("linked-account", resultBean.getLinkedIdentifier());
        assertEquals("linked-service-id", resultBean.getMappedServiceIdentifiers());
    }

    // updateService tests
    @Test
    @DisplayName("updateService: Should successfully update service details")
    void updateService_Success() throws HealControlCenterException, DataProcessingException {
        ViewTypesBean serviceControllerType = new ViewTypesBean();
        serviceControllerType.setSubTypeId(1);

        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(anyString(), anyString())).thenReturn(serviceControllerType);
        when(controllerBL.updateControllerDetails(any(ServiceBean.class), anyInt())).thenReturn(existingController);

        putAccountServicesBL.updateService(serviceBean);

        verify(controllerBL, times(1)).updateControllerDetails(eq(serviceBean), eq(1));
    }

    @Test
    @DisplayName("updateService: Should throw HealControlCenterException if service controller type not found")
    void updateService_ServiceControllerTypeNotFound_ThrowsControlCenterException() throws HealControlCenterException {
        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(anyString(), anyString())).thenReturn(null);

        HealControlCenterException exception = assertThrows(HealControlCenterException.class, () -> putAccountServicesBL.updateService(serviceBean));
        assertTrue(exception.getMessage().contains("Configuration error: 'Service' controller type not found."));
    }

    @Test
    @DisplayName("handleServiceGroupMapping: Should successfully map service to existing group")
    void handleServiceGroupMapping_Success() throws HealControlCenterException {
        ServiceGroupBean serviceGroup = new ServiceGroupBean();
        serviceGroup.setId(201);
        serviceBean.setServiceGroup(new ServiceGroupPojo("201", "group-id", "group-name"));

        when(accountServiceDao.findServiceGroupByIdentifier(anyString(), anyInt())).thenReturn(serviceGroup);
        when(accountServiceDao.createServiceGroupMapping(anyInt(), anyInt(), anyString())).thenReturn(1);

        putAccountServicesBL.handleServiceGroupMapping(serviceBean);

        verify(accountServiceDao, times(1)).deleteServiceGroupMapping(eq(101));
        verify(accountServiceDao, times(1)).createServiceGroupMapping(eq(101), eq(201), eq("test-user"));
    }

    @Test
    @DisplayName("handleServiceGroupMapping: Should not map if service group is null")
    void handleServiceGroupMapping_NullServiceGroup_NoMapping() throws HealControlCenterException {
        serviceBean.setServiceGroup(null);

        putAccountServicesBL.handleServiceGroupMapping(serviceBean);

        verify(accountServiceDao, times(1)).deleteServiceGroupMapping(eq(101));
        verify(accountServiceDao, never()).findServiceGroupByIdentifier(anyString(), anyInt());
        verify(accountServiceDao, never()).createServiceGroupMapping(anyInt(), anyInt(), anyString());
    }

    @Test
    @DisplayName("handleServiceGroupMapping: Should not map if service group identifier is empty")
    void handleServiceGroupMapping_EmptyServiceGroupIdentifier_NoMapping() throws HealControlCenterException {
        serviceBean.setServiceGroup(new ServiceGroupPojo("","",""));

        putAccountServicesBL.handleServiceGroupMapping(serviceBean);

        verify(accountServiceDao, times(1)).deleteServiceGroupMapping(eq(101));
        verify(accountServiceDao, never()).findServiceGroupByIdentifier(anyString(), anyInt());
        verify(accountServiceDao, never()).createServiceGroupMapping(anyInt(), anyInt(), anyString());
    }

    @Test
    @DisplayName("handleServiceGroupMapping: Should throw HealControlCenterException if mapping fails")
    void handleServiceGroupMapping_MappingFails_ThrowsControlCenterException() throws HealControlCenterException {
        ServiceGroupBean serviceGroup = new ServiceGroupBean();
        serviceGroup.setId(201);
        serviceBean.setServiceGroup(new ServiceGroupPojo("201", "group-id", "group-name"));

        when(accountServiceDao.findServiceGroupByIdentifier(anyString(), anyInt())).thenReturn(serviceGroup);
        when(accountServiceDao.createServiceGroupMapping(anyInt(), anyInt(), anyString())).thenReturn(0); // Simulate failure

        HealControlCenterException exception = assertThrows(HealControlCenterException.class, () -> putAccountServicesBL.handleServiceGroupMapping(serviceBean));
        assertTrue(exception.getMessage().contains("Failed to map service to service group"));
    }

    @Test
    @DisplayName("handleEntryPointTag: Should add entry point tag if not already mapped")
    void handleEntryPointTag_AddTag_Success() throws HealControlCenterException {
        TagDetailsBean entryPointTagDetails = new TagDetailsBean();
        entryPointTagDetails.setId(301);

        when(accountServiceDao.getTagDetails(anyString(), anyInt())).thenReturn(entryPointTagDetails);
        when(tagMappingBL.addTagMapping(anyInt(), anyInt(), anyString(), anyString(), anyString(), anyString(), anyInt())).thenReturn(1);
        putAccountServicesBL.handleEntryPointTag(serviceBean);

        verify(accountServiceDao, times(1)).deleteTagMapping(eq(301), eq(101), eq(Constants.CONTROLLER), eq(1));
        verify(tagMappingBL, times(1)).addTagMapping(eq(301), eq(101), eq(Constants.CONTROLLER), eq("Type"), eq("1"), eq("test-user"), eq(1));
    }

    @Test
    @DisplayName("handleEntryPointTag: Should throw HealControlCenterException if tag details not found")
    void handleEntryPointTag_TagDetailsNotFound_ThrowsControlCenterException() {
        when(accountServiceDao.getTagDetails(anyString(), anyInt())).thenReturn(null);

        HealControlCenterException exception = assertThrows(HealControlCenterException.class, () -> putAccountServicesBL.handleEntryPointTag(serviceBean));
        assertTrue(exception.getMessage().contains("Unable to fetch tag details for Entry-Point."));
    }

    @Test
    @DisplayName("handleEntryPointTag: Should throw HealControlCenterException if tag mapping fails")
    void handleEntryPointTag_TagMappingFails_ThrowsControlCenterException() throws HealControlCenterException {
        TagDetailsBean entryPointTagDetails = new TagDetailsBean();
        entryPointTagDetails.setId(301);

        when(accountServiceDao.getTagDetails(anyString(), anyInt())).thenReturn(entryPointTagDetails);
        when(tagMappingBL.addTagMapping(anyInt(), anyInt(), anyString(), anyString(), anyString(), anyString(), anyInt())).thenReturn(-1); // Simulate failure

        HealControlCenterException exception = assertThrows(HealControlCenterException.class, () -> putAccountServicesBL.handleEntryPointTag(serviceBean));
        assertTrue(exception.getMessage().contains("Unable to save entry point tag mapping details."));
    }

    @Test
    @DisplayName("handleLinkedServices: Should successfully insert new linked service")
    void handleLinkedServices_NewLink_Success() throws HealControlCenterException {
        serviceBean.setLinkedIdentifier("linked-account");
        serviceBean.setMappedServiceIdentifiers("mapped-service-id");
        serviceBean.setEnvironment("DC"); // Ensure environment is set for DC/DR logic

        ControllerBean linkedController = new ControllerBean();
        linkedController.setId(202);

        when(controllerDao.getControllerByIdentifierOrName(eq("mapped-service-id"), isNull())).thenReturn(linkedController);
//        when(accountServiceDao.deleteServiceAliasesByIdentifiers(anyString(), anyString())).thenReturn(1); // Simulate deletion
        when(accountServiceDao.insertLinkedService(anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyInt())).thenReturn(1);

        putAccountServicesBL.handleLinkedServices(serviceBean);

        verify(accountServiceDao, times(1)).deleteServiceAliasesByIdentifiers(eq("test-service-id_DC"), eq("mapped-service-id"));
        verify(accountServiceDao, times(1)).insertLinkedService(eq("Test Service_DC"), eq("test-service-id_DC"), eq("mapped-service-id"), eq("test-user"), anyString(), anyString(), eq(1));
    }

    @Test
    @DisplayName("handleLinkedServices: Should return early if no linkedIdentifier or mappedServiceIdentifiers")
    void handleLinkedServices_NoLinkedInfo_ReturnsEarly() throws HealControlCenterException {
        serviceBean.setLinkedIdentifier(null);
        serviceBean.setMappedServiceIdentifiers(null);

        putAccountServicesBL.handleLinkedServices(serviceBean);

        verify(controllerDao, never()).getControllerByIdentifierOrName(anyString(), any());
        verify(accountServiceDao, never()).deleteServiceAliasesByIdentifiers(anyString(), anyString());
        verify(accountServiceDao, never()).insertLinkedService(anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyInt());
    }

    @Test
    @DisplayName("handleLinkedServices: Should throw HealControlCenterException if linked controller not found")
    void handleLinkedServices_LinkedControllerNotFound_ThrowsControlCenterException() throws HealControlCenterException {
        serviceBean.setLinkedIdentifier("linked-account");
        serviceBean.setMappedServiceIdentifiers("non-existent-id");

        when(controllerDao.getControllerByIdentifierOrName(eq("non-existent-id"), isNull())).thenReturn(null);

        HealControlCenterException exception = assertThrows(HealControlCenterException.class, () -> putAccountServicesBL.handleLinkedServices(serviceBean));
        assertTrue(exception.getMessage().contains("The specified service to link with identifier does not exist."));
    }

    @Test
    @DisplayName("handleLinkedServices: Should throw HealControlCenterException if insert fails")
    void handleLinkedServices_InsertFails_ThrowsControlCenterException() throws HealControlCenterException {
        serviceBean.setLinkedIdentifier("linked-account");
        serviceBean.setMappedServiceIdentifiers("mapped-service-id");
        serviceBean.setEnvironment("DC");

        ControllerBean linkedController = new ControllerBean();
        linkedController.setId(202);

        when(controllerDao.getControllerByIdentifierOrName(eq("mapped-service-id"), isNull())).thenReturn(linkedController);
//        when(accountServiceDao.deleteServiceAliasesByIdentifiers(anyString(), anyString())).thenReturn(1);
        when(accountServiceDao.insertLinkedService(anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyInt())).thenReturn(0); // Simulate failure

        HealControlCenterException exception = assertThrows(HealControlCenterException.class, () -> putAccountServicesBL.handleLinkedServices(serviceBean));
        assertTrue(exception.getMessage().contains("Failed to insert new service alias for common name [Test Service_DC]."));
    }

    // updateServiceInRedis tests
    @Test
    @DisplayName("updateServiceInRedis: Should successfully update service in Redis")
    void updateServiceInRedis_Success() { // Added throws HealControlCenterException
        List<PersistenceSuppressionConfiguration> suppressionConfigs = Collections.emptyList();
        List<Tags> tags = Collections.emptyList();
        List<Rule> rules = Collections.emptyList();
        BasicEntity basicEntity = BasicEntity.builder().build();

        when(accountServiceDao.getAnomalySuppressionForService(anyInt())).thenReturn(suppressionConfigs);
        doReturn(new ServiceConfiguration()).when(putAccountServicesBL).buildServiceConfigurations(any(ServiceBean.class));
        doReturn(tags).when(putAccountServicesBL).buildTags(any(ServiceBean.class));
        doReturn(rules).when(putAccountServicesBL).buildRulesForService(any(ServiceBean.class));
        doReturn(basicEntity).when(putAccountServicesBL).updateServiceInList(any(ServiceRepo.class), any(ServiceBean.class));
        doNothing().when(putAccountServicesBL).updateServiceMappedToApplications(any(ServiceBean.class), any(BasicEntity.class));
        doNothing().when(putAccountServicesBL).updateApplicationsToService(any(ServiceBean.class));
        doNothing().when(serviceRepo).updateServiceRules(anyString(), anyString(), anyList());
        doNothing().when(serviceRepo).updateServiceConfigurationByServiceIdentifier(anyString(), anyString(), any(com.heal.configuration.pojos.Service.class));

        putAccountServicesBL.updateServiceInRedis(serviceBean);

        verify(accountServiceDao, times(1)).getAnomalySuppressionForService(eq(101));
        verify(serviceRepo, times(1)).updateServiceRules(eq("test-account"), eq("test-service-id_DC"), eq(rules));
        verify(serviceRepo, times(1)).updateServiceConfigurationByServiceIdentifier(eq("test-account"), eq("101"), any(com.heal.configuration.pojos.Service.class));
        verify(putAccountServicesBL, times(1)).updateServiceInList(eq(serviceRepo), eq(serviceBean));
        verify(putAccountServicesBL, times(1)).updateServiceMappedToApplications(eq(serviceBean), eq(basicEntity));
        verify(putAccountServicesBL, times(1)).updateApplicationsToService(eq(serviceBean));
    }

    // buildServiceConfigurations tests
    @Test
    @DisplayName("buildServiceConfigurations: Should correctly build ServiceConfiguration")
    void buildServiceConfigurations_Success() {
        ServiceConfiguration config = putAccountServicesBL.buildServiceConfigurations(serviceBean);
        assertNotNull(config);
        assertEquals("test-user", config.getLastModifiedBy());
        assertTrue(config.isLowEnable());
        assertTrue(config.isMediumEnable());
        assertTrue(config.isHighEnable());
        assertNotNull(config.getCreatedTime());
        assertNotNull(config.getUpdatedTime());
    }

    // buildTags tests
    @Test
    @DisplayName("buildTags: Should build all tags correctly")
    void buildTags_AllTags_Success() {
        List<Tags> tags = putAccountServicesBL.buildTags(serviceBean);
        assertNotNull(tags);
        assertEquals(3, tags.size());
        assertTrue(tags.stream().anyMatch(t -> t.getType().equals(Constants.LAYER_TAG) && t.getValue().equals("Application Tier")));
        assertTrue(tags.stream().anyMatch(t -> t.getType().equals(Constants.SERVICE_TYPE_TAG) && t.getValue().equals("REST-API")));
        assertTrue(tags.stream().anyMatch(t -> t.getType().equals(Constants.ENTRY_POINT) && t.getValue().equals("1")));
    }

    @Test
    @DisplayName("buildTags: Should handle null layer")
    void buildTags_NullLayer() {
        serviceBean.setLayer(null);
        List<Tags> tags = putAccountServicesBL.buildTags(serviceBean);
        assertNotNull(tags);
        assertEquals(2, tags.size()); // Only type and entry point
        assertFalse(tags.stream().anyMatch(t -> t.getType().equals(Constants.LAYER_TAG)));
    }

    @Test
    @DisplayName("buildTags: Should handle empty layer")
    void buildTags_EmptyLayer() {
        serviceBean.setLayer("");
        List<Tags> tags = putAccountServicesBL.buildTags(serviceBean);
        assertNotNull(tags);
        assertEquals(2, tags.size()); // Only type and entry point
        assertFalse(tags.stream().anyMatch(t -> t.getType().equals(Constants.LAYER_TAG)));
    }

    @Test
    @DisplayName("buildTags: Should handle null type")
    void buildTags_NullType() {
        serviceBean.setType(null);
        List<Tags> tags = putAccountServicesBL.buildTags(serviceBean);
        assertNotNull(tags);
        assertEquals(2, tags.size()); // Only layer and entry point
        assertFalse(tags.stream().anyMatch(t -> t.getType().equals(Constants.SERVICE_TYPE_TAG)));
    }

    @Test
    @DisplayName("buildTags: Should handle empty type")
    void buildTags_EmptyType() {
        serviceBean.setType("");
        List<Tags> tags = putAccountServicesBL.buildTags(serviceBean);
        assertNotNull(tags);
        assertEquals(2, tags.size()); // Only layer and entry point
        assertFalse(tags.stream().anyMatch(t -> t.getType().equals(Constants.SERVICE_TYPE_TAG)));
    }

    @Test
    @DisplayName("buildTags: Should handle non-entry point service")
    void buildTags_NotEntryPoint() {
        serviceBean.setEntryPointService(false);
        List<Tags> tags = putAccountServicesBL.buildTags(serviceBean);
        assertNotNull(tags);
        assertEquals(2, tags.size()); // Only layer and type
        assertFalse(tags.stream().anyMatch(t -> t.getType().equals(Constants.ENTRY_POINT)));
    }

    // buildRulesForService tests
    @Test
    @DisplayName("buildRulesForService: Should return empty list if no rules helper pojo")
    void buildRulesForService_NoRulesHelperPojo_ReturnsEmptyList() {
        when(accountServiceDao.getRulesHelperPojo(anyInt(), anyInt())).thenReturn(Collections.emptyList());
        List<Rule> rules = putAccountServicesBL.buildRulesForService(serviceBean);
        assertNotNull(rules);
        assertTrue(rules.isEmpty());
    }

    @Test
    @DisplayName("buildRulesForService: Should build rules correctly with data")
    void buildRulesForService_WithRules_Success() {
        RulesHelperPojo ruleHelper = getRulesHelperPojo();

        ViewTypes ruleType = new ViewTypes();
        ruleType.setSubTypeId(10);
        ruleType.setSubTypeName("RULE_TYPE");

        ViewTypes payloadType = new ViewTypes();
        payloadType.setSubTypeId(30);
        payloadType.setSubTypeName("PAYLOAD_TYPE");

        when(accountServiceDao.getRulesHelperPojo(anyInt(), anyInt())).thenReturn(Collections.singletonList(ruleHelper));
        when(masterDataRepo.getViewTypes()).thenReturn(Arrays.asList(ruleType, payloadType));
        when(accountServiceDao.getDataBeans(anyInt(), anyInt())).thenReturn(Collections.emptyList());

        List<Rule> rules = putAccountServicesBL.buildRulesForService(serviceBean);

        assertNotNull(rules);
        assertEquals(1, rules.size());
        Rule rule = rules.get(0);
        assertEquals("Test Rule", rule.getName());
        assertEquals("RULE_TYPE", rule.getRuleType());
        assertEquals("PAYLOAD_TYPE", rule.getRequestTypeDetails().getPayloadTypeName());
        assertEquals("tcp_initial", rule.getRegexTypeDetails().getInitialPattern());
    }

    @NotNull
    private static RulesHelperPojo getRulesHelperPojo() {
        RulesHelperPojo ruleHelper = new RulesHelperPojo();
        ruleHelper.setId(1);
        ruleHelper.setName("Test Rule");
        ruleHelper.setEnabled(1);
        ruleHelper.setOrder(1);
        ruleHelper.setRuleTypeId(10);
        ruleHelper.setIsDefault(0);
        ruleHelper.setMaxTags(5);
        ruleHelper.setDiscoveryEnabled(1);
        ruleHelper.setTcpId(100);
        ruleHelper.setTcpInitialPattern("tcp_initial");
        ruleHelper.setTcpLastPattern("tcp_last");
        ruleHelper.setTcpLength(10);
        ruleHelper.setHttpId(200);
        ruleHelper.setHttpFirstUriSegments(1); // Corrected type
        ruleHelper.setHttpLastUriSegments(1);   // Corrected type
        ruleHelper.setHttpCompleteURI(1);
        ruleHelper.setHttpPayloadTypeId(30);
        return ruleHelper;
    }

    // updateServiceInList tests
    @Test
    @DisplayName("updateServiceInList: Should successfully update service in list")
    void updateServiceInList_Success() {
        List<BasicEntity> allServicesDetails = new ArrayList<>();
        allServicesDetails.add(BasicEntity.builder().id(101).name("Old Service").identifier("old-service-id").build());
        allServicesDetails.add(BasicEntity.builder().id(102).name("Another Service").identifier("another-service-id").build());

        when(serviceRepo.getAllServicesDetails(anyString())).thenReturn(allServicesDetails);
        doNothing().when(serviceRepo).updateServiceConfiguration(anyString(), anyList());

        BasicEntity updatedService = putAccountServicesBL.updateServiceInList(serviceRepo, serviceBean);

        assertNotNull(updatedService);
        assertEquals(101, updatedService.getId());
        assertEquals("Test Service_DC", updatedService.getName());
        assertEquals(2, allServicesDetails.size()); // Size should remain same as one is removed and one is added
        assertTrue(allServicesDetails.stream().anyMatch(s -> s.getId() == 101 && s.getName().equals("Test Service_DC")));
        verify(serviceRepo, times(1)).updateServiceConfiguration(eq("test-account"), eq(allServicesDetails));
    }

    // updateServiceMappedToApplications tests
    @Test
    @DisplayName("updateServiceMappedToApplications: Should successfully update service mapped to applications")
    void updateServiceMappedToApplications_Success() {
        serviceBean.setMappedServiceIdentifiers("mapped-app-id");
        List<BasicEntity> mappedServices = new ArrayList<>();
        mappedServices.add(BasicEntity.builder().id(201).name("Existing Mapped App").build());

        when(applicationRepo.getServicesMappedToApplication(anyString(), anyString())).thenReturn(mappedServices);
        doNothing().when(applicationRepo).updateServiceApplication(anyString(), anyString(), anyList());

        putAccountServicesBL.updateServiceMappedToApplications(serviceBean, BasicEntity.builder().id(101).build());

        verify(applicationRepo, times(1)).getServicesMappedToApplication(eq("test-account"), eq("mapped-app-id"));
        verify(applicationRepo, times(1)).updateServiceApplication(eq("test-account"), eq("mapped-app-id"), anyList());
        assertEquals(2, mappedServices.size());
        assertTrue(mappedServices.stream().anyMatch(s -> s.getId() == 101));
    }

    @Test
    @DisplayName("updateServiceMappedToApplications: Should return early if mappedServiceIdentifiers is null")
    void updateServiceMappedToApplications_NullMappedServiceIdentifiers_ReturnsEarly() {
        serviceBean.setMappedServiceIdentifiers(null);

        putAccountServicesBL.updateServiceMappedToApplications(serviceBean, BasicEntity.builder().build());

        verify(applicationRepo, never()).getServicesMappedToApplication(anyString(), anyString());
        verify(applicationRepo, never()).updateServiceApplication(anyString(), anyString(), anyList());
    }

    @Test
    @DisplayName("updateServiceMappedToApplications: Should return early if mappedServiceIdentifiers is empty")
    void updateServiceMappedToApplications_EmptyMappedServiceIdentifiers_ReturnsEarly() {
        serviceBean.setMappedServiceIdentifiers("");

        putAccountServicesBL.updateServiceMappedToApplications(serviceBean, BasicEntity.builder().build());

        verify(applicationRepo, never()).getServicesMappedToApplication(anyString(), anyString());
        verify(applicationRepo, never()).updateServiceApplication(anyString(), anyString(), anyList());
    }

    // updateApplicationsToService tests
    @Test
    @DisplayName("updateApplicationsToService: Should successfully update applications to service")
    void updateApplicationsToService_Success() {
        List<BasicEntity> allApplications = new ArrayList<>();
        allApplications.add(BasicEntity.builder().id(201).name("App1").build());
        allApplications.add(BasicEntity.builder().id(202).name("App2").build());
        serviceBean.setAppIds(Arrays.asList(201, 203)); // 201 exists, 203 does not

        when(accountRepo.getAllApplications(anyString())).thenReturn(allApplications);
        doNothing().when(serviceRepo).updateApplicationsByServiceIdentifier(anyString(), anyString(), anyList());

        putAccountServicesBL.updateApplicationsToService(serviceBean);

        ArgumentCaptor<List> capturedList = ArgumentCaptor.forClass(List.class);
        verify(serviceRepo, times(1)).updateApplicationsByServiceIdentifier(eq("test-account"), eq("test-service-id_DC"), capturedList.capture());
        List<BasicEntity> filteredApps = capturedList.getValue();
        assertNotNull(filteredApps);
        assertEquals(1, filteredApps.size());
        assertEquals(201, filteredApps.get(0).getId());
    }

    @Test
    @DisplayName("updateApplicationsToService: Should handle empty appIds in serviceBean")
    void updateApplicationsToService_EmptyAppIds_Success() {
        serviceBean.setAppIds(Collections.emptyList());

        putAccountServicesBL.updateApplicationsToService(serviceBean);

        verify(serviceRepo, times(1)).updateApplicationsByServiceIdentifier(eq("test-account"), eq("test-service-id_DC"), argThat(List::isEmpty));
    }

    @Test
    @DisplayName("handleServiceAliasesUpdate: Should update common name if name changed and alias exists")
    void handleServiceAliasesUpdate_NameChangedAliasExists_Success() throws HealControlCenterException {
        String oldName = "Old Service Name_DC";
        String newName = "New Service Name_DC";
        ServiceAliases existingAlias = new ServiceAliases();
        existingAlias.setCommonName(oldName);
        existingAlias.setDcServiceIdentifier("test-service-id_DC");
        existingAlias.setDrServiceIdentifier("mapped-service-id_DR");

        when(accountServiceDao.getLinkedServiceByIdentifier(eq("test-service-id_DC"))).thenReturn(existingAlias);
        when(accountServiceDao.updateServiceAliasCommonName(anyString(), anyString(), anyString(), anyString(), anyString(), anyInt(), anyString())).thenReturn(1);

        putAccountServicesBL.handleServiceAliasesUpdate(oldName, newName, serviceBean);

        verify(accountServiceDao, times(1)).updateServiceAliasCommonName(eq(newName), eq("test-service-id_DC"), eq("mapped-service-id_DR"), eq("test-user"), anyString(), eq(1), eq("test-service-id_DC"));
    }

    @Test
    @DisplayName("handleServiceAliasesUpdate: Should skip update if oldName is null")
    void handleServiceAliasesUpdate_NullOldName_SkipsUpdate() throws HealControlCenterException {
        String newName = "New Service Name_DC";

        putAccountServicesBL.handleServiceAliasesUpdate(null, newName, serviceBean);

        verify(accountServiceDao, never()).getLinkedServiceByIdentifier(anyString());
        verify(accountServiceDao, never()).updateServiceAliasCommonName(anyString(), anyString(), anyString(), anyString(), anyString(), anyInt(), anyString());
    }

    @Test
    @DisplayName("handleServiceAliasesUpdate: Should skip update if oldName is empty")
    void handleServiceAliasesUpdate_EmptyOldName_SkipsUpdate() throws HealControlCenterException {
        String oldName = "";
        String newName = "New Service Name_DC";

        putAccountServicesBL.handleServiceAliasesUpdate(oldName, newName, serviceBean);

        verify(accountServiceDao, never()).getLinkedServiceByIdentifier(anyString());
        verify(accountServiceDao, never()).updateServiceAliasCommonName(anyString(), anyString(), anyString(), anyString(), anyString(), anyInt(), anyString());
    }

    @Test
    @DisplayName("handleServiceAliasesUpdate: Should skip update if name has not changed")
    void handleServiceAliasesUpdate_NameNotChanged_SkipsUpdate() throws HealControlCenterException {
        String oldName = "Test Service_DC";
        String newName = "Test Service_DC"; // Same name

        putAccountServicesBL.handleServiceAliasesUpdate(oldName, newName, serviceBean);

        verify(accountServiceDao, never()).getLinkedServiceByIdentifier(anyString());
        verify(accountServiceDao, never()).updateServiceAliasCommonName(anyString(), anyString(), anyString(), anyString(), anyString(), anyInt(), anyString());
    }

    @Test
    @DisplayName("handleServiceAliasesUpdate: Should skip update if no existing alias found")
    void handleServiceAliasesUpdate_NoExistingAlias_SkipsUpdate() throws HealControlCenterException {
        String oldName = "Old Service Name_DC";
        String newName = "New Service Name_DC";

        when(accountServiceDao.getLinkedServiceByIdentifier(anyString())).thenReturn(null); // No existing alias

        putAccountServicesBL.handleServiceAliasesUpdate(oldName, newName, serviceBean);

        verify(accountServiceDao, times(1)).getLinkedServiceByIdentifier(eq("test-service-id_DC"));
        verify(accountServiceDao, never()).updateServiceAliasCommonName(anyString(), anyString(), anyString(), anyString(), anyString(), anyInt(), anyString());
    }

    @Test
    @DisplayName("handleServiceAliasesUpdate: Should throw HealControlCenterException if update fails")
    void handleServiceAliasesUpdate_UpdateFails_ThrowsControlCenterException() throws HealControlCenterException {
        String oldName = "Old Service Name_DC";
        String newName = "New Service Name_DC";
        ServiceAliases existingAlias = new ServiceAliases();
        existingAlias.setCommonName(oldName);
        existingAlias.setDcServiceIdentifier("test-service-id_DC");
        existingAlias.setDrServiceIdentifier("mapped-service-id_DR");

        when(accountServiceDao.getLinkedServiceByIdentifier(eq("test-service-id_DC"))).thenReturn(existingAlias);
        when(accountServiceDao.updateServiceAliasCommonName(anyString(), anyString(), anyString(), anyString(), anyString(), anyInt(), anyString())).thenReturn(0); // Simulate failure

        HealControlCenterException exception = assertThrows(HealControlCenterException.class, () -> putAccountServicesBL.handleServiceAliasesUpdate(oldName, newName, serviceBean));
        assertTrue(exception.getMessage().contains("Failed to update service alias for old common name [Old Service Name_DC] to new common name [New Service Name_DC]."));
    }
}