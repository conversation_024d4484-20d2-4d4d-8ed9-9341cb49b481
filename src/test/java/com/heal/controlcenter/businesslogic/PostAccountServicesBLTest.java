package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.*;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountServiceDao;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.redis.ApplicationRepo;
import com.heal.controlcenter.dao.redis.MasterDataRepo;
import com.heal.controlcenter.dao.redis.ServiceRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.LinkedEnvironmentPojo;
import com.heal.controlcenter.pojo.RulesHelperPojo;
import com.heal.controlcenter.pojo.ServiceGroupPojo;
import com.heal.controlcenter.pojo.ServicePojo;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.ServerValidationUtils;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PostAccountServicesBLTest {

    @Spy
    @InjectMocks
    private PostAccountServicesBL postAccountServicesBL;

    @Mock
    private ServiceRepo serviceRepo;
    @Mock
    private MasterDataDao masterDataDao;
    @Mock
    private ControllerDao controllerDao;
    @Mock
    private ClientValidationUtils clientValidationUtils;
    @Mock
    private ServerValidationUtils serverValidationUtils;
    @Mock
    private AccountServiceDao accountServiceDao;
    @Mock
    private ControllerBL controllerBL;
    @Mock
    private TagMappingBL tagMappingBL;
    @Mock
    private MasterDataRepo masterDataRepo;
    @Mock
    private ApplicationRepo applicationRepo;

    private ServicePojo servicePojo;
    private List<ServicePojo> servicePojoList;
    private Account account;
    private UtilityBean<List<ServicePojo>> clientValidationBean;
    private UtilityBean<List<ServiceBean>> serverValidationBean;
    private ServiceBean serviceBean;

    @BeforeEach
    void setUp() {
        // Common test data setup
        servicePojo = new ServicePojo();
        servicePojo.setName("Test Service");
        servicePojo.setIdentifier("test-service-id");
        servicePojo.setAppIdentifiers(Collections.singletonList("app-id"));
        servicePojo.setIsEntryPointService((short) 1);
        servicePojo.setEnvironment("DC");
        ServiceGroupPojo groupBean = new ServiceGroupPojo();
        groupBean.setIdentifier("group-1");
        servicePojo.setServiceGroup(groupBean);


        servicePojoList = Collections.singletonList(servicePojo);

        account = new Account();
        account.setId(1);
        account.setIdentifier("test-account");

        clientValidationBean = UtilityBean.<List<ServicePojo>>builder()
                .pojoObject(servicePojoList)
                .requestParams(CommonUtils.buildRequestParams(null, "test-account"))
                .metadata(new HashMap<>())
                .build();
        clientValidationBean.getMetadata().put(Constants.USER_ID_KEY, "test-user");

        serviceBean = ServiceBean.builder()
                .id(101)
                .name("Test Service_DC")
                .identifier("test-service-id_DC")
                .accountId(1)
                .accountIdentifier("test-account")
                .userId("test-user")
                .appIds(Collections.singletonList(201))
                .entryPointService(true)
                .serviceGroup(new ServiceGroupPojo("1", "test-group", "test-group-identifier"))
                .environment("DC")
                .build();

        serverValidationBean = UtilityBean.<List<ServiceBean>>builder()
                .pojoObject(Collections.singletonList(serviceBean))
                .requestParams(clientValidationBean.getRequestParams())
                .metadata(clientValidationBean.getMetadata())
                .build();
    }

    @Test
    @DisplayName("clientValidation_Success: Should validate client input successfully")
    void clientValidation_Success() throws ClientException {
        ServicePojo spyPojo = spy(servicePojo);
        doReturn(Collections.emptyMap()).when(spyPojo).validate();
        List<ServicePojo> spyList = Collections.singletonList(spyPojo);

        UtilityBean<List<ServicePojo>> result = postAccountServicesBL.clientValidation(spyList, "test-account");
        assertNotNull(result);
        assertEquals("test-account", result.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
        assertEquals(1, result.getPojoObject().size());
    }

    @Test
    @DisplayName("clientValidation_Failure_EmptyBody: Should throw exception for empty service list")
    void clientValidation_Failure_EmptyBody() {
        ClientException exception = assertThrows(ClientException.class,
                () -> postAccountServicesBL.clientValidation(Collections.emptyList(), "test-account"));
        assertTrue(exception.getMessage().contains("The list of services in the request body cannot be empty."));
    }

    @Test
    @DisplayName("clientValidation_Failure_DuplicateName: Should throw exception for duplicate service name")
    void clientValidation_Failure_DuplicateName() {
        ServicePojo duplicate = new ServicePojo();
        duplicate.setName("Test Service");
        List<ServicePojo> duplicatesList = Arrays.asList(servicePojo, duplicate);

        ClientException exception = assertThrows(ClientException.class,
                () -> postAccountServicesBL.clientValidation(duplicatesList, "test-account"));
        assertTrue(exception.getMessage().contains("Duplicate service name 'Test Service'"));
    }

    @Test
    @DisplayName("clientValidation_Failure_NullAccountIdentifier: Should throw exception for null account identifier")
    void clientValidation_Failure_NullAccountIdentifier() throws ClientException {
        doThrow(new ClientException("Account identifier cannot be null or empty")).when(clientValidationUtils).accountIdentifierValidation(isNull());
        ClientException exception = assertThrows(ClientException.class,
                () -> postAccountServicesBL.clientValidation(servicePojoList, (String) null));
        assertTrue(exception.getMessage().contains("Account identifier cannot be null or empty"));
    }

    @Test
    @DisplayName("clientValidation_Failure_EmptyAccountIdentifier: Should throw exception for empty account identifier")
    void clientValidation_Failure_EmptyAccountIdentifier() throws ClientException {
        doThrow(new ClientException("Account identifier cannot be null or empty")).when(clientValidationUtils).accountIdentifierValidation(eq(""));
        ClientException exception = assertThrows(ClientException.class,
                () -> postAccountServicesBL.clientValidation(servicePojoList, ""));
        assertTrue(exception.getMessage().contains("Account identifier cannot be null or empty"));
    }

    @Test
    @DisplayName("serverValidation_Success: Should validate server input successfully")
    void serverValidation_Success() throws ServerException, HealControlCenterException {
        ControllerBean appController = new ControllerBean();
        appController.setId(201);

        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);
        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(anyString(), anyString())).thenReturn(new ViewTypesBean());
        when(masterDataDao.findTagDetailsByName(anyString(), anyString())).thenReturn(new TagDetailsBean());
        when(controllerDao.getControllerByIdentifierOrName(eq("app-id"), isNull())).thenReturn(appController);
        when(controllerDao.getControllerByIdentifierOrName(eq("test-service-id_DC"), isNull())).thenReturn(null);
        when(controllerDao.getControllerByIdentifierOrName(isNull(), eq("Test Service_DC"))).thenReturn(null);


        UtilityBean<List<ServiceBean>> result = postAccountServicesBL.serverValidation(clientValidationBean);

        assertNotNull(result);
        assertEquals(1, result.getPojoObject().size());
        ServiceBean resultBean = result.getPojoObject().get(0);
        assertEquals("Test Service_DC", resultBean.getName());
        assertEquals(1, resultBean.getAccountId());
    }

    @Test
    @DisplayName("serverValidation_Failure_AccountNotFound: Should throw exception when account not found")
    void serverValidation_Failure_AccountNotFound() throws ServerException {
        when(serverValidationUtils.accountValidation(anyString())).thenThrow(new ServerException("Account not found"));
        assertThrows(ServerException.class, () -> postAccountServicesBL.serverValidation(clientValidationBean));
    }

    @Test
    @DisplayName("serverValidation_Failure_ServiceTypeNotFound: Should throw exception when service type not found")
    void serverValidation_Failure_ServiceTypeNotFound() throws ServerException, HealControlCenterException {
        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);
        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(anyString(), anyString())).thenReturn(null);
        assertThrows(ServerException.class, () -> postAccountServicesBL.serverValidation(clientValidationBean));
    }

    @Test
    @DisplayName("serverValidation_Failure_ControllerTagNotFound: Should throw exception when controller tag not found")
    void serverValidation_Failure_ControllerTagNotFound() throws ServerException, HealControlCenterException {
        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);
        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(anyString(), anyString())).thenReturn(new ViewTypesBean());
        when(masterDataDao.findTagDetailsByName(anyString(), anyString())).thenReturn(new TagDetailsBean());
        assertThrows(ServerException.class, () -> postAccountServicesBL.serverValidation(clientValidationBean));
    }

    @Test
    @DisplayName("serverValidation_Failure_AppIdentifierNotFound: Should throw exception when app identifier not found")
    void serverValidation_Failure_AppIdentifierNotFound() throws ServerException, HealControlCenterException {
        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);
        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(anyString(), anyString())).thenReturn(new ViewTypesBean());
        when(masterDataDao.findTagDetailsByName(anyString(), anyString())).thenReturn(new TagDetailsBean());
        when(controllerDao.getControllerByIdentifierOrName(eq("app-id"), isNull())).thenReturn(null);
        assertThrows(ServerException.class, () -> postAccountServicesBL.serverValidation(clientValidationBean));
    }

    @Test
    @DisplayName("serverValidation_Failure_ServiceIdentifierExists: Should throw exception when service identifier already exists")
    void serverValidation_Failure_ServiceIdentifierExists() throws ServerException, HealControlCenterException {
        ControllerBean appController = new ControllerBean();
        appController.setId(201);
        ControllerBean existingService = new ControllerBean();
        existingService.setId(101);
        existingService.setStatus(1);
        existingService.setAccountId(1);

        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);
        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(anyString(), anyString())).thenReturn(new ViewTypesBean());
        when(masterDataDao.findTagDetailsByName(anyString(), anyString())).thenReturn(new TagDetailsBean());
        when(controllerDao.getControllerByIdentifierOrName(eq("app-id"), isNull())).thenReturn(appController);
        when(controllerDao.getControllerByIdentifierOrName(eq("test-service-id_DC"), isNull())).thenReturn(existingService);
        when(controllerDao.getControllerApplicationId(anyInt(), anyInt(), anyInt())).thenReturn(201);


        assertThrows(ServerException.class, () -> postAccountServicesBL.serverValidation(clientValidationBean));
    }

    @Test
    @DisplayName("serverValidation_Success_InactiveServiceReactivated: Should reactivate inactive service")
    void serverValidation_Success_InactiveServiceReactivated() throws ServerException, HealControlCenterException {
        ControllerBean appController = new ControllerBean();
        appController.setId(201);
        ControllerBean inactiveService = new ControllerBean();
        inactiveService.setId(101);
        inactiveService.setStatus(0); // Inactive status
        inactiveService.setAccountId(1);
        inactiveService.setControllerTypeId(1); // Assuming a valid type ID

        ViewTypesBean serviceControllerType = new ViewTypesBean();
        serviceControllerType.setSubTypeId(1); // Set a valid subType ID

        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);
        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(anyString(), anyString())).thenReturn(serviceControllerType);
        when(masterDataDao.findTagDetailsByName(anyString(), anyString())).thenReturn(new TagDetailsBean());
        when(controllerDao.getControllerByIdentifierOrName(eq("app-id"), isNull())).thenReturn(appController);
        when(controllerDao.getControllerByIdentifierOrName(eq("test-service-id_DC"), isNull())).thenReturn(inactiveService);
        when(controllerDao.getControllerApplicationId(anyInt(), anyInt(), anyInt())).thenReturn(201);
//        when(controllerDao.getControllerByIdentifierOrName(isNull(), eq("Test Service_DC"))).thenReturn(null); // Ensure no name conflict

        UtilityBean<List<ServiceBean>> result = postAccountServicesBL.serverValidation(clientValidationBean);

        assertNotNull(result);
        assertEquals(1, result.getPojoObject().size());
        ServiceBean resultBean = result.getPojoObject().get(0);
        assertEquals("Test Service_DC", resultBean.getName());
        assertEquals(1, resultBean.getAccountId());
        // Verify that the service is marked for reactivation (status will be 1 by default in ServiceBean builder)
    }

    @Test
    @DisplayName("serverValidation_Success_LinkedEnvironmentExists: Should validate linked environment successfully")
    void serverValidation_Success_LinkedEnvironmentExists() throws ServerException, HealControlCenterException {
        ServicePojo linkedServicePojo = new ServicePojo();
        linkedServicePojo.setName("Test Service");
        linkedServicePojo.setIdentifier("test-service-id");
        linkedServicePojo.setAppIdentifiers(Collections.singletonList("app-id"));
        linkedServicePojo.setIsEntryPointService((short) 0);
        linkedServicePojo.setEnvironment("DC");
        linkedServicePojo.setLinkedEnvironment(new LinkedEnvironmentPojo("DR", "demo", "mapped-service-id"));

        List<ServicePojo> linkedServicePojoList = Collections.singletonList(linkedServicePojo);

        UtilityBean<List<ServicePojo>> linkedClientValidationBean = UtilityBean.<List<ServicePojo>>builder()
                .pojoObject(linkedServicePojoList)
                .requestParams(CommonUtils.buildRequestParams(null, "test-account"))
                .metadata(new HashMap<>())
                .build();
        linkedClientValidationBean.getMetadata().put(Constants.USER_ID_KEY, "test-user");

        ControllerBean appController = new ControllerBean();
        appController.setId(201);
        ControllerBean existingService = new ControllerBean();
        existingService.setId(101);
        existingService.setStatus(1); // Active status
        existingService.setAccountId(1);
        existingService.setControllerTypeId(1); // Assuming a valid type ID

        ViewTypesBean serviceControllerType = new ViewTypesBean();
        serviceControllerType.setSubTypeId(1); // Set a valid subType ID

        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);
        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(anyString(), anyString())).thenReturn(serviceControllerType);
        when(masterDataDao.findTagDetailsByName(anyString(), anyString())).thenReturn(new TagDetailsBean());
        when(controllerDao.getControllerByIdentifierOrName(eq("app-id"), isNull())).thenReturn(appController);
        when(controllerDao.getControllerByIdentifierOrName(eq("test-service-id_DC"), isNull())).thenReturn(existingService);
        when(controllerDao.getControllerApplicationId(anyInt(), anyInt(), anyInt())).thenReturn(201);
//        when(controllerDao.getControllerByIdentifierOrName(isNull(), eq("Test Service_DC"))).thenReturn(null); // Ensure no name conflict

        UtilityBean<List<ServiceBean>> result = postAccountServicesBL.serverValidation(linkedClientValidationBean);

        assertNotNull(result);
        assertEquals(1, result.getPojoObject().size());
        ServiceBean resultBean = result.getPojoObject().get(0);
        assertEquals("Test Service_DC", resultBean.getName());
        assertEquals("mapped-service-id", resultBean.getMappedServiceIdentifier());
        assertEquals("demo", resultBean.getLinkedIdentifier()); // This is actually the account in LinkedEnvironmentPojo
    }

    @Test
    @DisplayName("serverValidation_Success_NoLayer: Should validate when no layer is present")
    void serverValidation_Success_NoLayer() throws ServerException, HealControlCenterException {
        servicePojo.setLayer(null); // No layer

        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);
        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(anyString(), anyString())).thenReturn(new ViewTypesBean());
        when(masterDataDao.findTagDetailsByName(anyString(), anyString())).thenReturn(new TagDetailsBean());
        when(controllerDao.getControllerByIdentifierOrName(eq("app-id"), isNull())).thenReturn(new ControllerBean() {{ setId(201); }});
        when(controllerDao.getControllerByIdentifierOrName(eq("test-service-id_DC"), isNull())).thenReturn(null);
        when(controllerDao.getControllerByIdentifierOrName(isNull(), eq("Test Service_DC"))).thenReturn(null);

        UtilityBean<List<ServiceBean>> result = postAccountServicesBL.serverValidation(clientValidationBean);

        assertNotNull(result);
        assertEquals(1, result.getPojoObject().size());
        ServiceBean resultBean = result.getPojoObject().get(0);
        assertNull(resultBean.getLayer());
    }

    @Test
    @DisplayName("serverValidation_Success_NoType: Should validate when no type is present")
    void serverValidation_Success_NoType() throws ServerException, HealControlCenterException {
        servicePojo.setType(null); // No type

        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);
        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(anyString(), anyString())).thenReturn(new ViewTypesBean());
        when(masterDataDao.findTagDetailsByName(anyString(), anyString())).thenReturn(new TagDetailsBean());
        when(controllerDao.getControllerByIdentifierOrName(eq("app-id"), isNull())).thenReturn(new ControllerBean() {{ setId(201); }});
        when(controllerDao.getControllerByIdentifierOrName(eq("test-service-id_DC"), isNull())).thenReturn(null);
        when(controllerDao.getControllerByIdentifierOrName(isNull(), eq("Test Service_DC"))).thenReturn(null);

        UtilityBean<List<ServiceBean>> result = postAccountServicesBL.serverValidation(clientValidationBean);

        assertNotNull(result);
        assertEquals(1, result.getPojoObject().size());
        ServiceBean resultBean = result.getPojoObject().get(0);
        assertEquals(Constants.NON_KUBERNETES, resultBean.getType()); // Default type
    }

    @Test
    @DisplayName("serverValidation_Failure_ExistingServiceIdentifierDifferentAccount: Should not throw for different account")
    void serverValidation_Failure_ExistingServiceIdentifierDifferentAccount() throws ServerException, HealControlCenterException {
        ControllerBean appController = new ControllerBean();
        appController.setId(201);
        ControllerBean existingService = new ControllerBean();
        existingService.setId(101);
        existingService.setStatus(1);
        existingService.setAccountId(999); // Different account ID

        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);
        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(anyString(), anyString())).thenReturn(new ViewTypesBean());
        when(masterDataDao.findTagDetailsByName(anyString(), anyString())).thenReturn(new TagDetailsBean());
        when(controllerDao.getControllerByIdentifierOrName(eq("app-id"), isNull())).thenReturn(appController);
        when(controllerDao.getControllerByIdentifierOrName(eq("test-service-id_DC"), isNull())).thenReturn(existingService);

        ServerException exception = assertThrows(ServerException.class, () -> postAccountServicesBL.serverValidation(clientValidationBean));
        assertFalse(exception.getMessage().contains("Service identifier 'test-service-id_DC' already exists."));
    }

    @Test
    @DisplayName("serverValidation_Failure_ExistingServiceIdentifierNotMappedToApp: Should not throw for not mapped app")
    void serverValidation_Failure_ExistingServiceIdentifierNotMappedToApp() throws ServerException, HealControlCenterException {
        ControllerBean appController = new ControllerBean();
        appController.setId(201);
        ControllerBean existingService = new ControllerBean();
        existingService.setId(101);
        existingService.setStatus(1);
        existingService.setAccountId(1);

        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);
        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(anyString(), anyString())).thenReturn(new ViewTypesBean());
        when(masterDataDao.findTagDetailsByName(anyString(), anyString())).thenReturn(new TagDetailsBean());
        when(controllerDao.getControllerByIdentifierOrName(eq("app-id"), isNull())).thenReturn(appController);
        when(controllerDao.getControllerByIdentifierOrName(eq("test-service-id_DC"), isNull())).thenReturn(existingService);
        when(controllerDao.getControllerApplicationId(anyInt(), anyInt(), anyInt())).thenReturn(999); // Mapped to different app

        ServerException exception = assertThrows(ServerException.class, () -> postAccountServicesBL.serverValidation(clientValidationBean));
        assertFalse(exception.getMessage().contains("Service identifier 'test-service-id_DC' already exists."));
    }

    @Test
    @DisplayName("process_Failure_AddServiceThrowsException: Should throw exception when addService fails")
    void process_Failure_AddServiceThrowsException() throws HealControlCenterException, DataProcessingException {
        doThrow(new HealControlCenterException("DB error")).when(postAccountServicesBL).addService(any(ServiceBean.class));

        assertThrows(DataProcessingException.class, () -> postAccountServicesBL.process(serverValidationBean));
    }

    @Test
    @DisplayName("process_Failure_AddServiceToRedisThrowsException: Should throw exception when Redis update fails")
    void process_Failure_AddServiceToRedisThrowsException() throws HealControlCenterException, DataProcessingException {
        doReturn(new IdPojo(101, "Test Service_DC")).when(postAccountServicesBL).addService(any(ServiceBean.class));
        // Removed direct mocking of private method, its dependencies will be mocked instead.
        doThrow(new RuntimeException("Redis error")).when(serviceRepo).updateServiceRules(anyString(), anyString(), anyList());

        DataProcessingException exception = assertThrows(DataProcessingException.class,
                () -> postAccountServicesBL.process(serverValidationBean));
        assertTrue(exception.getMessage().contains("Failed to process adding account services."));
    }

    @Test
    @DisplayName("addService_Success_WithGroupAndEntryPoint: Should add service with group and entry point")
    void addService_Success_WithGroupAndEntryPoint() throws HealControlCenterException, DataProcessingException {
        ControllerBean controller = new ControllerBean();
        controller.setId(101);

        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(anyString(), anyString())).thenReturn(new ViewTypesBean());
        when(controllerBL.addOrUpdateController(any(ServiceBean.class), anyInt())).thenReturn(controller);
        when(accountServiceDao.findServiceGroupByIdentifier(anyString(), anyInt())).thenReturn(new ServiceGroupBean());
        when(accountServiceDao.createServiceGroupMapping(anyInt(), anyInt(), anyString())).thenReturn(1);
        when(accountServiceDao.getTagDetails(anyString(), anyInt())).thenReturn(new TagDetailsBean());
        when(accountServiceDao.isTagMappingPresent(anyInt(), anyInt(), anyString(), anyString(), anyString(), anyInt())).thenReturn(false);
        when(tagMappingBL.addTagMapping(anyInt(), anyInt(), anyString(), anyString(), anyString(), anyString(), anyInt())).thenReturn(1);

        IdPojo result = postAccountServicesBL.addService(serviceBean);

        assertNotNull(result);
        assertEquals(101, result.getId());
        verify(accountServiceDao, times(1)).createServiceGroupMapping(anyInt(), anyInt(), anyString());
        verify(tagMappingBL, times(1)).addTagMapping(anyInt(), anyInt(), anyString(), anyString(), anyString(), anyString(), anyInt());
    }

    @Test
    @DisplayName("addService_Failure_ControllerCreationFails: Should throw when controller creation fails")
    void addService_Failure_ControllerCreationFails() throws HealControlCenterException, DataProcessingException {
        ControllerBean controller = new ControllerBean();
        controller.setId(-1);

        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(anyString(), anyString())).thenReturn(new ViewTypesBean());
        when(controllerBL.addOrUpdateController(any(ServiceBean.class), anyInt())).thenReturn(controller);

        assertThrows(HealControlCenterException.class, () -> postAccountServicesBL.addService(serviceBean));
    }

    @Test
    @DisplayName("addService_Failure_ServiceGroupMappingFails: Should throw when service group mapping fails")
    void addService_Failure_ServiceGroupMappingFails() throws HealControlCenterException, DataProcessingException {
        ControllerBean controller = new ControllerBean();
        controller.setId(101);

        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(anyString(), anyString())).thenReturn(new ViewTypesBean());
        when(controllerBL.addOrUpdateController(any(ServiceBean.class), anyInt())).thenReturn(controller);
        when(accountServiceDao.findServiceGroupByIdentifier(anyString(), anyInt())).thenReturn(new ServiceGroupBean());
        when(accountServiceDao.createServiceGroupMapping(anyInt(), anyInt(), anyString())).thenReturn(0); // Simulate failure

        HealControlCenterException exception = assertThrows(HealControlCenterException.class, () -> postAccountServicesBL.addService(serviceBean));
        assertTrue(exception.getMessage().contains("Failed to map service [id: 101] to service group [id: 0]"));
    }

    @Test
    @DisplayName("addService_Failure_EntryPointTagDetailsNotFound: Should throw when entry point tag details not found")
    void addService_Failure_EntryPointTagDetailsNotFound() throws HealControlCenterException, DataProcessingException {
        ControllerBean controller = new ControllerBean();
        controller.setId(101);

        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(anyString(), anyString())).thenReturn(new ViewTypesBean());
        when(controllerBL.addOrUpdateController(any(ServiceBean.class), anyInt())).thenReturn(controller);
        when(accountServiceDao.findServiceGroupByIdentifier(anyString(), anyInt())).thenReturn(new ServiceGroupBean());
        when(accountServiceDao.createServiceGroupMapping(anyInt(), anyInt(), anyString())).thenReturn(1);
        when(accountServiceDao.getTagDetails(anyString(), anyInt())).thenReturn(null); // Simulate not found

        HealControlCenterException exception = assertThrows(HealControlCenterException.class, () -> postAccountServicesBL.addService(serviceBean));
        assertTrue(exception.getMessage().contains("Unable to fetch tag details for Entry-Point."));
    }

    @Test
    @DisplayName("addService_Success_EntryPointTagAlreadyMapped: Should not add entry point tag if already mapped")
    void addService_Success_EntryPointTagAlreadyMapped() throws HealControlCenterException, DataProcessingException {
        ControllerBean controller = new ControllerBean();
        controller.setId(101);

        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(anyString(), anyString())).thenReturn(new ViewTypesBean());
        when(controllerBL.addOrUpdateController(any(ServiceBean.class), anyInt())).thenReturn(controller);
        when(accountServiceDao.findServiceGroupByIdentifier(anyString(), anyInt())).thenReturn(new ServiceGroupBean());
        when(accountServiceDao.createServiceGroupMapping(anyInt(), anyInt(), anyString())).thenReturn(1);
        when(accountServiceDao.getTagDetails(anyString(), anyInt())).thenReturn(new TagDetailsBean());
        when(accountServiceDao.isTagMappingPresent(anyInt(), anyInt(), anyString(), anyString(), anyString(), anyInt())).thenReturn(true); // Already mapped

        IdPojo result = postAccountServicesBL.addService(serviceBean);

        assertNotNull(result);
        assertEquals(101, result.getId());
        verify(tagMappingBL, never()).addTagMapping(anyInt(), anyInt(), anyString(), anyString(), anyString(), anyString(), anyInt()); // Should not be called
    }

    @Test
    @DisplayName("addService_Failure_EntryPointTagMappingFails: Should throw when entry point tag mapping fails")
    void addService_Failure_EntryPointTagMappingFails() throws HealControlCenterException, DataProcessingException {
        ControllerBean controller = new ControllerBean();
        controller.setId(101);

        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(anyString(), anyString())).thenReturn(new ViewTypesBean());
        when(controllerBL.addOrUpdateController(any(ServiceBean.class), anyInt())).thenReturn(controller);
        when(accountServiceDao.findServiceGroupByIdentifier(anyString(), anyInt())).thenReturn(new ServiceGroupBean());
        when(accountServiceDao.createServiceGroupMapping(anyInt(), anyInt(), anyString())).thenReturn(1);
        when(accountServiceDao.getTagDetails(anyString(), anyInt())).thenReturn(new TagDetailsBean());
        when(accountServiceDao.isTagMappingPresent(anyInt(), anyInt(), anyString(), anyString(), anyString(), anyInt())).thenReturn(false);
        when(tagMappingBL.addTagMapping(anyInt(), anyInt(), anyString(), anyString(), anyString(), anyString(), anyInt())).thenReturn(-1); // Simulate failure

        HealControlCenterException exception = assertThrows(HealControlCenterException.class, () -> postAccountServicesBL.addService(serviceBean));
        assertTrue(exception.getMessage().contains("Unable to save entry point tag mapping details."));
    }

    @Test
    @DisplayName("addService_Success_NoServiceGroupAndNoEntryPoint: Should add service without group and entry point")
    void addService_Success_NoServiceGroupAndNoEntryPoint() throws HealControlCenterException, DataProcessingException {
        serviceBean.setServiceGroup(null); // No service group
        serviceBean.setEntryPointService(false); // No entry point

        ControllerBean controller = new ControllerBean();
        controller.setId(101);

        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(anyString(), anyString())).thenReturn(new ViewTypesBean());
        when(controllerBL.addOrUpdateController(any(ServiceBean.class), anyInt())).thenReturn(controller);

        IdPojo result = postAccountServicesBL.addService(serviceBean);

        assertNotNull(result);
        assertEquals(101, result.getId());
        verify(accountServiceDao, never()).findServiceGroupByIdentifier(anyString(), anyInt());
        verify(accountServiceDao, never()).createServiceGroupMapping(anyInt(), anyInt(), anyString());
        verify(accountServiceDao, never()).getTagDetails(anyString(), anyInt());
        verify(tagMappingBL, never()).addTagMapping(anyInt(), anyInt(), anyString(), anyString(), anyString(), anyString(), anyInt());
    }

    @Test
    @DisplayName("buildRulesForService_Success: Should build rules for service successfully")
    void buildRulesForService_Success() {
        when(accountServiceDao.getRulesHelperPojo(anyInt(), anyInt())).thenReturn(new ArrayList<>());
        List<Rule> rules = postAccountServicesBL.buildRulesForService(serviceBean);
        assertNotNull(rules);
        assertTrue(rules.isEmpty());
    }

    @Test
    @DisplayName("buildRulesForService_Success_WithRules: Should build rules for service with rules")
    void buildRulesForService_Success_WithRules() {
        List<RulesHelperPojo> rulesHelperList = getRulesHelperPojos();

        List<ViewTypes> viewTypesList = new ArrayList<>();
        ViewTypes vt1 = new ViewTypes();
        vt1.setSubTypeId(10);
        vt1.setSubTypeName("RuleType1");
        viewTypesList.add(vt1);
        ViewTypes vt2 = new ViewTypes();
        vt2.setSubTypeId(30);
        vt2.setSubTypeName("PayloadType1");
        viewTypesList.add(vt2);

        when(accountServiceDao.getRulesHelperPojo(anyInt(), anyInt())).thenReturn(rulesHelperList);
        when(masterDataRepo.getViewTypes()).thenReturn(viewTypesList);
        when(accountServiceDao.getDataBeans(anyInt(), anyInt())).thenReturn(Collections.emptyList()); // Mock empty pair data

        List<Rule> rules = postAccountServicesBL.buildRulesForService(serviceBean);

        assertNotNull(rules);
        assertFalse(rules.isEmpty());
        assertEquals(1, rules.size());
        assertEquals("Rule 1", rules.get(0).getName());
        assertEquals("RuleType1", rules.get(0).getRuleType());
        assertEquals("PayloadType1", rules.get(0).getRequestTypeDetails().getPayloadTypeName());
    }

    @NotNull
    private static List<RulesHelperPojo> getRulesHelperPojos() {
        List<RulesHelperPojo> rulesHelperList = new ArrayList<>();
        RulesHelperPojo rule1 = new RulesHelperPojo();
        rule1.setId(1);
        rule1.setName("Rule 1");
        rule1.setEnabled(1);
        rule1.setOrder(1);
        rule1.setRuleTypeId(10);
        rule1.setIsDefault(0);
        rule1.setMaxTags(5);
        rule1.setDiscoveryEnabled(1);
        rule1.setTcpId(100);
        rule1.setTcpInitialPattern("initial");
        rule1.setTcpLastPattern("last");
        rule1.setTcpLength(10);
        rule1.setHttpId(200);
        rule1.setHttpFirstUriSegments(1);
        rule1.setHttpLastUriSegments(1);
        rule1.setHttpCompleteURI(1);
        rule1.setHttpPayloadTypeId(30);
        rulesHelperList.add(rule1);
        return rulesHelperList;
    }

    @Test
    @DisplayName("addServiceMappedToApplications_Success: Should add service mapped to applications successfully")
    void addServiceMappedToApplications_Success() {
        serviceBean.setMappedServiceIdentifier("mapped-app-id");
        List<BasicEntity> mappedServices = new ArrayList<>();
        mappedServices.add(BasicEntity.builder().id(301).name("Existing_Mapped_Service").build());

        when(applicationRepo.getServicesMappedToApplication(anyString(), anyString())).thenReturn(mappedServices);
        doNothing().when(applicationRepo).updateServiceApplication(anyString(), anyString(), anyList());

        postAccountServicesBL.addServiceMappedToApplications(serviceBean, BasicEntity.builder().id(101).build());

        verify(applicationRepo, times(1)).getServicesMappedToApplication(eq("test-account"), eq("mapped-app-id"));
        verify(applicationRepo, times(1)).updateServiceApplication(eq("test-account"), eq("mapped-app-id"), anyList());
        assertEquals(2, mappedServices.size()); // Original + added
    }

    @Test
    @DisplayName("addServiceMappedToApplications_NoMappedServiceIdentifier: Should not add if mapped service identifier is null")
    void addServiceMappedToApplications_NoMappedServiceIdentifier() {
        serviceBean.setMappedServiceIdentifier(null); // No mapped service identifier

        postAccountServicesBL.addServiceMappedToApplications(serviceBean, BasicEntity.builder().build());

        verify(applicationRepo, never()).getServicesMappedToApplication(anyString(), anyString());
        verify(applicationRepo, never()).updateServiceApplication(anyString(), anyString(), anyList());
    }

    @Test
    @DisplayName("addServiceMappedToApplications_EmptyMappedServiceIdentifier: Should not add if mapped service identifier is empty")
    void addServiceMappedToApplications_EmptyMappedServiceIdentifier() {
        serviceBean.setMappedServiceIdentifier(""); // Empty mapped service identifier

        postAccountServicesBL.addServiceMappedToApplications(serviceBean, BasicEntity.builder().build());

        verify(applicationRepo, never()).getServicesMappedToApplication(anyString(), anyString());
        verify(applicationRepo, never()).updateServiceApplication(anyString(), anyString(), anyList());
    }

    @Test
    @DisplayName("addNewServiceToList_Success: Should add new service to list successfully")
    void addNewServiceToList_Success() {
        Map<String, IdPojo> serviceDetailMap = new HashMap<>();
        serviceDetailMap.put("Test Service_DC", new IdPojo(101, "Test Service_DC"));

        List<BasicEntity> allServicesDetails = new ArrayList<>();
        allServicesDetails.add(BasicEntity.builder().id(1).name("Existing Service").build());

        when(serviceRepo.getAllServicesDetails(anyString())).thenReturn(allServicesDetails);
        doNothing().when(serviceRepo).updateServiceConfiguration(anyString(), anyList());

        BasicEntity result = postAccountServicesBL.addNewServiceToList(serviceRepo, serviceBean, serviceDetailMap);

        assertNotNull(result);
        assertEquals(101, result.getId());
        assertEquals("Test Service_DC", result.getName());
        assertEquals(2, allServicesDetails.size()); // Original + added
        verify(serviceRepo, times(1)).updateServiceConfiguration(eq("test-account"), anyList());
    }

    @Test
    @DisplayName("addNewServiceToList_Failure_IdPojoNotFound: Should throw if IdPojo not found")
    void addNewServiceToList_Failure_IdPojoNotFound() {
        Map<String, IdPojo> serviceDetailMap = new HashMap<>(); // Empty map

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> postAccountServicesBL.addNewServiceToList(serviceRepo, serviceBean, serviceDetailMap));
        assertTrue(exception.getMessage().contains("Service name [Test Service_DC] not found in serviceDetailMap."));
    }

    @Test
    @DisplayName("buildServiceConfigurations_Success: Should build service configurations successfully")
    void buildServiceConfigurations_Success() {
        ServiceConfiguration config = postAccountServicesBL.buildServiceConfigurations(serviceBean);
        assertNotNull(config);
        assertEquals("test-user", config.getLastModifiedBy());
        assertTrue(config.isLowEnable());
    }

    @Test
    @DisplayName("buildTags_Success_NoLayer: Should build tags successfully when no layer")
    void buildTags_Success_NoLayer() {
        serviceBean.setLayer(null);
        List<Tags> tags = postAccountServicesBL.buildTags(serviceBean);
        assertNotNull(tags);
        assertEquals(1, tags.size()); // Type, EntryPoint
        assertFalse(tags.stream().anyMatch(t -> t.getType().equals(Constants.LAYER_TAG)));
    }

    @Test
    @DisplayName("buildTags_Success_EmptyLayer: Should build tags successfully when layer is empty")
    void buildTags_Success_EmptyLayer() {
        serviceBean.setLayer("");
        List<Tags> tags = postAccountServicesBL.buildTags(serviceBean);
        assertNotNull(tags);
        assertEquals(1, tags.size()); // Type, EntryPoint
        assertFalse(tags.stream().anyMatch(t -> t.getType().equals(Constants.LAYER_TAG)));
    }

    @Test
    @DisplayName("buildTags_Success_NoType: Should build tags successfully when no type")
    void buildTags_Success_NoType() {
        serviceBean.setType(null);
        List<Tags> tags = postAccountServicesBL.buildTags(serviceBean);
        assertNotNull(tags);
        assertEquals(1, tags.size()); // Layer, EntryPoint
        assertFalse(tags.stream().anyMatch(t -> t.getType().equals(Constants.SERVICE_TYPE_TAG)));
    }

    @Test
    @DisplayName("buildTags_Success_EmptyType: Should build tags successfully when type is empty")
    void buildTags_Success_EmptyType() {
        serviceBean.setType("");
        List<Tags> tags = postAccountServicesBL.buildTags(serviceBean);
        assertNotNull(tags);
        assertEquals(1, tags.size()); // Layer, EntryPoint
        assertFalse(tags.stream().anyMatch(t -> t.getType().equals(Constants.SERVICE_TYPE_TAG)));
    }

    @Test
    @DisplayName("buildTags_Success_NoEntryPoint: Should build tags successfully when no entry point")
    void buildTags_Success_NoEntryPoint() {
        serviceBean.setEntryPointService(false);
        List<Tags> tags = postAccountServicesBL.buildTags(serviceBean);
        assertNotNull(tags);
        assertEquals(0, tags.size()); // Layer, Type
        assertFalse(tags.stream().anyMatch(t -> t.getType().equals(Constants.ENTRY_POINT)));
    }

    @Test
    @DisplayName("handleLinkedServices_Success_ExistingLinkSkipped: Should skip existing link")
    void handleLinkedServices_Success_ExistingLinkSkipped() throws HealControlCenterException {
        serviceBean.setLinkedIdentifier("demo");
        serviceBean.setMappedServiceIdentifier("p01_DR");
        serviceBean.setName("a1_DC");

        ControllerBean linkedController = new ControllerBean();
        linkedController.setId(999);

        ServiceAliases existingAlias = new ServiceAliases();
        existingAlias.setCommonName("a1_DC");

        when(controllerDao.getControllerByIdentifierOrName(eq("p01_DR"), isNull())).thenReturn(linkedController);
        when(accountServiceDao.getLinkedServiceByCommonName(eq("a1_DC"))).thenReturn(existingAlias); // Existing alias found

        postAccountServicesBL.handleLinkedServices(serviceBean);

        verify(accountServiceDao, never()).insertLinkedService(anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyInt()); // Should not insert
    }

    @Test
    @DisplayName("handleLinkedServices_Failure_LinkedControllerNotFound: Should throw when linked controller not found")
    void handleLinkedServices_Failure_LinkedControllerNotFound() throws HealControlCenterException {
        serviceBean.setLinkedIdentifier("demo");
        serviceBean.setMappedServiceIdentifier("non-existent-id");

        when(controllerDao.getControllerByIdentifierOrName(eq("non-existent-id"), isNull())).thenReturn(null);

        HealControlCenterException exception = assertThrows(HealControlCenterException.class,
                () -> postAccountServicesBL.handleLinkedServices(serviceBean));
        assertTrue(exception.getMessage().contains("The specified service to link with identifier [non-existent-id] does not exist."));
    }

    @Test
    @DisplayName("handleLinkedServices_Failure_CommonNameMissing: Should throw when common name is missing")
    void handleLinkedServices_Failure_CommonNameMissing() {
        serviceBean.setLinkedIdentifier("demo");
        serviceBean.setMappedServiceIdentifier("p01_DR");
        serviceBean.setName(null); // Common name is null

        HealControlCenterException exception = assertThrows(HealControlCenterException.class,
                () -> postAccountServicesBL.handleLinkedServices(serviceBean));
        assertTrue(exception.getMessage().contains("Cannot process service link: commonName is missing from the ServiceBean."));
    }

    @Test
    @DisplayName("handleLinkedServices_Failure_EmptyCommonName: Should throw when common name is empty")
    void handleLinkedServices_Failure_EmptyCommonName() {
        serviceBean.setLinkedIdentifier("demo");
        serviceBean.setMappedServiceIdentifier("p01_DR");
        serviceBean.setName(""); // Common name is empty

        HealControlCenterException exception = assertThrows(HealControlCenterException.class,
                () -> postAccountServicesBL.handleLinkedServices(serviceBean));
        assertTrue(exception.getMessage().contains("Cannot process service link: commonName is missing from the ServiceBean."));
    }

    @Test
    @DisplayName("handleLinkedServices_Failure_UnknownEnvironment: Should handle unknown environment")
    void handleLinkedServices_Failure_UnknownEnvironment() throws HealControlCenterException {
        serviceBean.setLinkedIdentifier("demo");
        serviceBean.setMappedServiceIdentifier("p01_DR");
        serviceBean.setEnvironment("UNKNOWN"); // Unknown environment

        ControllerBean linkedController = new ControllerBean();
        linkedController.setId(999);

        when(controllerDao.getControllerByIdentifierOrName(eq("p01_DR"), isNull())).thenReturn(linkedController);
        when(accountServiceDao.getLinkedServiceByCommonName(anyString())).thenReturn(null);

        postAccountServicesBL.handleLinkedServices(serviceBean); // Should log warning and return

        verify(accountServiceDao, never()).insertLinkedService(anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyInt());
    }

    @Test
    @DisplayName("handleLinkedServices_Failure_InsertFails: Should throw when insert fails")
    void handleLinkedServices_Failure_InsertFails() throws HealControlCenterException {
        serviceBean.setLinkedIdentifier("demo");
        serviceBean.setMappedServiceIdentifier("p01_DR");
        serviceBean.setName("a1_DC");

        ControllerBean linkedController = new ControllerBean();
        linkedController.setId(999);

        when(controllerDao.getControllerByIdentifierOrName(eq("p01_DR"), isNull())).thenReturn(linkedController);
        when(accountServiceDao.getLinkedServiceByCommonName(eq("a1_DC"))).thenReturn(null);
        when(accountServiceDao.insertLinkedService(anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyInt())).thenReturn(0); // Simulate failure

        HealControlCenterException exception = assertThrows(HealControlCenterException.class,
                () -> postAccountServicesBL.handleLinkedServices(serviceBean));
        assertTrue(exception.getMessage().contains("Failed to insert service_alias for common name [a1_DC]"));
    }
}

