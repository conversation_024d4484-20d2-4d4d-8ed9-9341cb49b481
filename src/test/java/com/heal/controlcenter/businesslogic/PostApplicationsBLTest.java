package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.*;
import com.heal.controlcenter.dao.redis.ApplicationRepo;
import com.heal.controlcenter.dao.redis.MasterDataRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.pojo.Application;
import com.heal.controlcenter.pojo.ApplicationTags;
import com.heal.controlcenter.pojo.IdPojo;
import com.heal.controlcenter.pojo.SeverityLevel;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.DateTimeUtil;
import com.heal.controlcenter.util.ServerValidationUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.sql.Timestamp;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class PostApplicationsBLTest {
    @Mock
    UserDao userDao;
    @Mock
    AccountsDao accountsDao;
    @Mock
    ControllerDao controllerDao;
    @Mock
    MasterDataDao masterDataDao;
    @Mock
    ApplicationNotifAndPercentileDao applicationNotifAndPercentileDao;
    @Mock
    TagsDao tagsDao;
    @Mock
    ClientValidationUtils clientValidationUtils;
    @Mock
    ServerValidationUtils serverValidationUtils;
    @Mock
    ApplicationRepo applicationRepo;
    @Mock
    MasterDataRepo masterDataRepo;

    @InjectMocks
    PostApplicationsBL postApplicationsBL;

    private Application validApp;
    private String accountIdentifier = "acc-1";
    private MockedStatic<DateTimeUtil> dateTimeUtilMockedStatic;

    @BeforeEach
    void setup() {
        validApp = Application.builder()
                .name("TestApp")
                .identifier("test-app")
                .environment("dev")
                .timezoneId("Asia/Kolkata")
                .severity(new ArrayList<SeverityLevel>())
                .tags(new ArrayList<ApplicationTags>())
                .build();
        
        dateTimeUtilMockedStatic = Mockito.mockStatic(DateTimeUtil.class);
    }

    @AfterEach
    void tearDown() {
        dateTimeUtilMockedStatic.close();
    }

    @Test
    void testClientValidation_success() throws ClientException {
        List<Application> apps = Collections.singletonList(validApp);
        doNothing().when(clientValidationUtils).accountIdentifierValidation(accountIdentifier);
        UtilityBean<List<Application>> result = postApplicationsBL.clientValidation(apps, accountIdentifier);
        assertNotNull(result);
        assertEquals(apps, result.getPojoObject());
        assertEquals(accountIdentifier, result.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
    }

    @Test
    void testClientValidation_emptyList_throwsException() {
        Exception ex = assertThrows(ClientException.class, () ->
                postApplicationsBL.clientValidation(Collections.emptyList(), accountIdentifier));
        assertTrue(ex.getMessage().contains("Request body cannot be empty"));
    }

    @Test
    void testClientValidation_nullApplications_throwsException() {
        Exception ex = assertThrows(ClientException.class, () ->
                postApplicationsBL.clientValidation(null, accountIdentifier));
        assertTrue(ex.getMessage().contains("Request body cannot be empty"));
    }

    @Test
    void testClientValidation_invalidAccountIdentifier_throwsException() throws ClientException {
        List<Application> apps = Collections.singletonList(validApp);
        doThrow(new ClientException("Invalid account identifier")).when(clientValidationUtils).accountIdentifierValidation(anyString());
        Exception ex = assertThrows(ClientException.class, () ->
                postApplicationsBL.clientValidation(apps, ""));
        assertTrue(ex.getMessage().contains("Invalid account identifier"));
    }

    @Test
    void testClientValidation_applicationValidationError() throws ClientException {
        Application invalidApp = Application.builder().name("").identifier("").environment("").timezoneId("").severity(new ArrayList<>()).tags(new ArrayList<>()).build();
        List<Application> apps = Collections.singletonList(invalidApp);
        doNothing().when(clientValidationUtils).accountIdentifierValidation(accountIdentifier);
        Exception ex = assertThrows(ClientException.class, () ->
                postApplicationsBL.clientValidation(apps, accountIdentifier));
        assertTrue(ex.getMessage().contains("accountName"));
    }

    @Test
    void testServerValidation_success() throws Exception {
        // Arrange
        List<Application> apps = Collections.singletonList(validApp);
        Map<String, String> reqParams = new HashMap<>();
        reqParams.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID_KEY, "user-1");
        UtilityBean<List<Application>> input = UtilityBean.<List<Application>>builder()
                .pojoObject(apps)
                .requestParams(reqParams)
                .metadata(metadata)
                .build();
        Account mockAccount = new Account();
        mockAccount.setId(10);
        when(serverValidationUtils.accountValidation(accountIdentifier)).thenReturn(mockAccount);
        UserInfoBean userInfoBean = new UserInfoBean();
        userInfoBean.setProfileId(1);
        when(userDao.getUserDetails(anyString())).thenReturn(userInfoBean);
        UserProfileBean userProfileBean = new UserProfileBean();
        userProfileBean.setUserProfileName("Super Admin"); // Use correct role name as per Constants.SUPER_ADMIN
        when(userDao.getUserProfile(anyInt())).thenReturn(userProfileBean);
        when(controllerDao.existsByIdentifier(anyString())).thenReturn(false);
        when(controllerDao.existsByName(anyString())).thenReturn(false);
        when(masterDataDao.getTimeZoneWithId(anyString())).thenReturn(new TimezoneBean());
        // Act
        UtilityBean<List<ApplicationBean>> result = postApplicationsBL.serverValidation(input);
        // Assert
        assertNotNull(result);
        assertEquals(1, result.getPojoObject().size());
        assertEquals("TestApp", result.getPojoObject().get(0).getName());
    }

    @Test
    void testServerValidation_accountValidationFails() throws Exception {
        List<Application> apps = Collections.singletonList(validApp);
        Map<String, String> reqParams = new HashMap<>();
        reqParams.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID_KEY, "user-1");
        UtilityBean<List<Application>> input = UtilityBean.<List<Application>>builder()
                .pojoObject(apps)
                .requestParams(reqParams)
                .metadata(metadata)
                .build();
        when(serverValidationUtils.accountValidation(accountIdentifier)).thenThrow(new ServerException("Account not found"));
        assertThrows(ServerException.class, () -> postApplicationsBL.serverValidation(input));
    }

    @Test
    void testServerValidation_userDetailsFails() throws Exception {
        List<Application> apps = Collections.singletonList(validApp);
        Map<String, String> reqParams = new HashMap<>();
        reqParams.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID_KEY, "user-1");
        UtilityBean<List<Application>> input = UtilityBean.<List<Application>>builder()
                .pojoObject(apps)
                .requestParams(reqParams)
                .metadata(metadata)
                .build();
        Account mockAccount = new Account();
        mockAccount.setId(10);
        when(serverValidationUtils.accountValidation(accountIdentifier)).thenReturn(mockAccount);
        when(userDao.getUserDetails(anyString())).thenThrow(new HealControlCenterException("User not found"));
        assertThrows(ServerException.class, () -> postApplicationsBL.serverValidation(input));
    }

    @Test
    void testServerValidation_userProfileFails() throws Exception {
        List<Application> apps = Collections.singletonList(validApp);
        Map<String, String> reqParams = new HashMap<>();
        reqParams.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID_KEY, "user-1");
        UtilityBean<List<Application>> input = UtilityBean.<List<Application>>builder()
                .pojoObject(apps)
                .requestParams(reqParams)
                .metadata(metadata)
                .build();
        Account mockAccount = new Account();
        mockAccount.setId(10);
        when(serverValidationUtils.accountValidation(accountIdentifier)).thenReturn(mockAccount);
        UserInfoBean userInfoBean = new UserInfoBean();
        userInfoBean.setProfileId(1);
        when(userDao.getUserDetails(anyString())).thenReturn(userInfoBean);
        when(userDao.getUserProfile(anyInt())).thenThrow(new HealControlCenterException("Profile not found"));
        assertThrows(ServerException.class, () -> postApplicationsBL.serverValidation(input));
    }

    @Test
    void testServerValidation_userNotAllowed() throws Exception {
        List<Application> apps = Collections.singletonList(validApp);
        Map<String, String> reqParams = new HashMap<>();
        reqParams.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID_KEY, "user-1");
        UtilityBean<List<Application>> input = UtilityBean.<List<Application>>builder()
                .pojoObject(apps)
                .requestParams(reqParams)
                .metadata(metadata)
                .build();
        Account mockAccount = new Account();
        mockAccount.setId(10);
        when(serverValidationUtils.accountValidation(accountIdentifier)).thenReturn(mockAccount);
        UserInfoBean userInfoBean = new UserInfoBean();
        userInfoBean.setProfileId(1);
        when(userDao.getUserDetails(anyString())).thenReturn(userInfoBean);
        UserProfileBean userProfileBean = new UserProfileBean();
        userProfileBean.setUserProfileName("USER");
        when(userDao.getUserProfile(anyInt())).thenReturn(userProfileBean);
        assertThrows(ServerException.class, () -> postApplicationsBL.serverValidation(input));
    }

    @Test
    void testServerValidation_duplicateIdentifier() throws Exception {
        List<Application> apps = Collections.singletonList(validApp);
        Map<String, String> reqParams = new HashMap<>();
        reqParams.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID_KEY, "user-1");
        UtilityBean<List<Application>> input = UtilityBean.<List<Application>>builder()
                .pojoObject(apps)
                .requestParams(reqParams)
                .metadata(metadata)
                .build();
        Account mockAccount = new Account();
        mockAccount.setId(10);
        when(serverValidationUtils.accountValidation(accountIdentifier)).thenReturn(mockAccount);
        UserInfoBean userInfoBean = new UserInfoBean();
        userInfoBean.setProfileId(1);
        when(userDao.getUserDetails(anyString())).thenReturn(userInfoBean);
        UserProfileBean userProfileBean = new UserProfileBean();
        userProfileBean.setUserProfileName("SUPER_ADMIN");
        when(userDao.getUserProfile(anyInt())).thenReturn(userProfileBean);
        assertThrows(ServerException.class, () -> postApplicationsBL.serverValidation(input));
    }

    @Test
    void testServerValidation_duplicateName() throws Exception {
        List<Application> apps = Collections.singletonList(validApp);
        Map<String, String> reqParams = new HashMap<>();
        reqParams.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID_KEY, "user-1");
        UtilityBean<List<Application>> input = UtilityBean.<List<Application>>builder()
                .pojoObject(apps)
                .requestParams(reqParams)
                .metadata(metadata)
                .build();
        Account mockAccount = new Account();
        mockAccount.setId(10);
        when(serverValidationUtils.accountValidation(accountIdentifier)).thenReturn(mockAccount);
        UserInfoBean userInfoBean = new UserInfoBean();
        userInfoBean.setProfileId(1);
        when(userDao.getUserDetails(anyString())).thenReturn(userInfoBean);
        UserProfileBean userProfileBean = new UserProfileBean();
        userProfileBean.setUserProfileName("SUPER_ADMIN");
        when(userDao.getUserProfile(anyInt())).thenReturn(userProfileBean);
        assertThrows(ServerException.class, () -> postApplicationsBL.serverValidation(input));
    }

    @Test
    void testServerValidation_invalidTimezone() throws Exception {
        List<Application> apps = Collections.singletonList(validApp);
        Map<String, String> reqParams = new HashMap<>();
        reqParams.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID_KEY, "user-1");
        UtilityBean<List<Application>> input = UtilityBean.<List<Application>>builder()
                .pojoObject(apps)
                .requestParams(reqParams)
                .metadata(metadata)
                .build();
        Account mockAccount = new Account();
        mockAccount.setId(10);
        when(serverValidationUtils.accountValidation(accountIdentifier)).thenReturn(mockAccount);
        UserInfoBean userInfoBean = new UserInfoBean();
        userInfoBean.setProfileId(1);
        when(userDao.getUserDetails(anyString())).thenReturn(userInfoBean);
        UserProfileBean userProfileBean = new UserProfileBean();
        userProfileBean.setUserProfileName("SUPER_ADMIN");
        when(userDao.getUserProfile(anyInt())).thenReturn(userProfileBean);
        assertThrows(ServerException.class, () -> postApplicationsBL.serverValidation(input));
    }

    @Test
    void testServerValidation_userNotAllowed_superAdmin() throws Exception {
        List<Application> apps = Collections.singletonList(validApp);
        Map<String, String> reqParams = new HashMap<>();
        reqParams.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID_KEY, "user-1");
        UtilityBean<List<Application>> input = UtilityBean.<List<Application>>builder()
                .pojoObject(apps)
                .requestParams(reqParams)
                .metadata(metadata)
                .build();
        Account mockAccount = new Account();
        mockAccount.setId(10);
        when(serverValidationUtils.accountValidation(accountIdentifier)).thenReturn(mockAccount);
        UserInfoBean userInfoBean = new UserInfoBean();
        userInfoBean.setProfileId(1);
        when(userDao.getUserDetails(anyString())).thenReturn(userInfoBean);
        UserProfileBean userProfileBean = new UserProfileBean();
        userProfileBean.setUserProfileName(Constants.SUPER_ADMIN);
        when(userDao.getUserProfile(anyInt())).thenReturn(userProfileBean);
        when(controllerDao.existsByIdentifier(anyString())).thenReturn(false);
        when(controllerDao.existsByName(anyString())).thenReturn(false);
        when(masterDataDao.getTimeZoneWithId(anyString())).thenReturn(new TimezoneBean());
        UtilityBean<List<ApplicationBean>> result = postApplicationsBL.serverValidation(input);
        assertNotNull(result);
        assertEquals(1, result.getPojoObject().size());
    }

    @Test
    void testServerValidation_userNotAllowed_healAdmin() throws Exception {
        List<Application> apps = Collections.singletonList(validApp);
        Map<String, String> reqParams = new HashMap<>();
        reqParams.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID_KEY, "user-1");
        UtilityBean<List<Application>> input = UtilityBean.<List<Application>>builder()
                .pojoObject(apps)
                .requestParams(reqParams)
                .metadata(metadata)
                .build();
        Account mockAccount = new Account();
        mockAccount.setId(10);
        when(serverValidationUtils.accountValidation(accountIdentifier)).thenReturn(mockAccount);
        UserInfoBean userInfoBean = new UserInfoBean();
        userInfoBean.setProfileId(1);
        when(userDao.getUserDetails(anyString())).thenReturn(userInfoBean);
        UserProfileBean userProfileBean = new UserProfileBean();
        userProfileBean.setUserProfileName(Constants.HEAL_ADMIN);
        when(userDao.getUserProfile(anyInt())).thenReturn(userProfileBean);
        when(controllerDao.existsByIdentifier(anyString())).thenReturn(false);
        when(controllerDao.existsByName(anyString())).thenReturn(false);
        when(masterDataDao.getTimeZoneWithId(anyString())).thenReturn(new TimezoneBean());
        UtilityBean<List<ApplicationBean>> result = postApplicationsBL.serverValidation(input);
        assertNotNull(result);
        assertEquals(1, result.getPojoObject().size());
    }

    @Test
    void testServerValidation_userNotAllowed_otherRole() throws Exception {
        List<Application> apps = Collections.singletonList(validApp);
        Map<String, String> reqParams = new HashMap<>();
        reqParams.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID_KEY, "user-1");
        UtilityBean<List<Application>> input = UtilityBean.<List<Application>>builder()
                .pojoObject(apps)
                .requestParams(reqParams)
                .metadata(metadata)
                .build();
        Account mockAccount = new Account();
        mockAccount.setId(10);
        when(serverValidationUtils.accountValidation(accountIdentifier)).thenReturn(mockAccount);
        UserInfoBean userInfoBean = new UserInfoBean();
        userInfoBean.setProfileId(1);
        when(userDao.getUserDetails(anyString())).thenReturn(userInfoBean);
        UserProfileBean userProfileBean = new UserProfileBean();
        userProfileBean.setUserProfileName("USER");
        when(userDao.getUserProfile(anyInt())).thenReturn(userProfileBean);
        // This should throw ServerException for not allowed user
        ServerException ex = assertThrows(ServerException.class, () -> postApplicationsBL.serverValidation(input));
        assertTrue(ex.getMessage().contains("User is not allowed to create an application"));
    }

    @Test
    void testProcess_success() throws Exception {
        ApplicationBean appBean = ApplicationBean.builder()
                .name("TestApp")
                .identifier("test-app")
                .accountId(10)
                .environment("dev")
                .userId("user-1")
                .severity(new ArrayList<>())
                .tags(new ArrayList<>())
                .status(1)
                .build();
        UtilityBean<List<ApplicationBean>> input = UtilityBean.<List<ApplicationBean>>builder()
                .pojoObject(List.of(appBean))
                .metadata(Map.of(Constants.USER_ID_KEY, "user-1", Constants.TIME_ZONE_TAG, new TimezoneBean()))
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .build();
        dateTimeUtilMockedStatic.when(DateTimeUtil::getCurrentTimestampInGMT).thenReturn(Timestamp.valueOf("2025-07-17 10:00:00"));
        when(controllerDao.insertController(any())).thenReturn(1);
        when(tagsDao.getTagDetailsByName(anyString())).thenReturn(createTagDetailsBean(1));
        when(tagsDao.addTagMappingDetails(any())).thenReturn(1);
        when(accountsDao.getAccountAnomalyConfiguration(anyInt())).thenReturn(null);
        doNothing().when(controllerDao).insertApplicationAnomalyConfigurations(any());
        when(applicationNotifAndPercentileDao.addDefaultNotificationPreferences(any())).thenReturn(new int[]{1});
        when(applicationNotifAndPercentileDao.addApplicationPercentiles(any())).thenReturn(new int[]{1});
        doNothing().when(applicationRepo).updateApplication(anyString(), any());
        doNothing().when(applicationRepo).updateApplicationDetailsForAccount(anyString(), any());
        // Fix: Mock masterDataRepo.getTypes() to return required ViewTypes
        List<com.heal.configuration.pojos.ViewTypes> viewTypesList = new ArrayList<>();
        com.heal.configuration.pojos.ViewTypes appType = new com.heal.configuration.pojos.ViewTypes();
        appType.setTypeName(Constants.CONTROLLER_TYPE_NAME_DEFAULT); // "ControllerType"
        appType.setSubTypeName(Constants.APPLICATION_CONTROLLER_TYPE); // "Application"
        appType.setSubTypeId(191); // Example type id
        viewTypesList.add(appType);
        when(masterDataRepo.getTypes()).thenReturn(viewTypesList);
        // Mock all required masterDataDao.getViewTypesFromMstTypeAndSubTypeName calls
        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(eq(Constants.NOTIFICATION_TYPE_LITERAL), eq(Constants.IMMEDIATELY)))
            .thenReturn(ViewTypesBean.builder().subTypeId(101).build());
        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(eq(Constants.SIGNAL_TYPE_LITERAL), eq(Constants.PROBLEM)))
            .thenReturn(ViewTypesBean.builder().subTypeId(201).build());
        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(eq(Constants.SIGNAL_TYPE_LITERAL), eq(Constants.EARLY_WARNING)))
            .thenReturn(ViewTypesBean.builder().subTypeId(202).build());
        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(eq(Constants.SIGNAL_TYPE_LITERAL), eq(Constants.INFO)))
            .thenReturn(ViewTypesBean.builder().subTypeId(203).build());
        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(eq(Constants.SIGNAL_TYPE_LITERAL), eq(Constants.BATCH)))
            .thenReturn(ViewTypesBean.builder().subTypeId(204).build());
        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(eq(Constants.SIGNAL_SEVERITY_TYPE_LITERAL), eq(Constants.SEVERE)))
            .thenReturn(ViewTypesBean.builder().subTypeId(301).build());
        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(eq(Constants.SIGNAL_SEVERITY_TYPE_LITERAL), eq(Constants.DEFAULT)))
            .thenReturn(ViewTypesBean.builder().subTypeId(302).build());
        List<IdPojo> result = postApplicationsBL.process(input);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("TestApp", result.get(0).getName());
    }

    @Test
    void testProcess_tagMappingException() throws Exception {
        ApplicationBean appBean = ApplicationBean.builder()
                .name("TestApp")
                .identifier("test-app")
                .accountId(10)
                .environment("dev")
                .userId("user-1")
                .severity(new ArrayList<>())
                .tags(new ArrayList<>())
                .status(1)
                .build();
        UtilityBean<List<ApplicationBean>> input = UtilityBean.<List<ApplicationBean>>builder()
                .pojoObject(List.of(appBean))
                .metadata(Map.of(Constants.USER_ID_KEY, "user-1", Constants.TIME_ZONE_TAG, new TimezoneBean()))
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .build();
        dateTimeUtilMockedStatic.when(DateTimeUtil::getCurrentTimestampInGMT).thenReturn(Timestamp.valueOf("2025-07-17 10:00:00"));
        when(controllerDao.insertController(any())).thenReturn(1);
        when(tagsDao.getTagDetailsByName(anyString())).thenReturn(createTagDetailsBean(1));
        doThrow(new HealControlCenterException("Tag mapping failed")).when(tagsDao).addTagMappingDetails(any());
        assertThrows(DataProcessingException.class, () -> postApplicationsBL.process(input));
    }

    @Test
    void testProcess_anomalyConfigException() throws Exception {
        ApplicationBean appBean = ApplicationBean.builder()
                .name("TestApp")
                .identifier("test-app")
                .accountId(10)
                .environment("dev")
                .userId("user-1")
                .severity(List.of(createSeverityLevel("low", 1)))
                .tags(new ArrayList<>())
                .status(1)
                .build();
        UtilityBean<List<ApplicationBean>> input = UtilityBean.<List<ApplicationBean>>builder()
                .pojoObject(List.of(appBean))
                .metadata(Map.of(Constants.USER_ID_KEY, "user-1", Constants.TIME_ZONE_TAG, new TimezoneBean()))
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .build();
        dateTimeUtilMockedStatic.when(DateTimeUtil::getCurrentTimestampInGMT).thenReturn(Timestamp.valueOf("2025-07-17 10:00:00"));
        when(controllerDao.insertController(any())).thenReturn(1);
        when(tagsDao.getTagDetailsByName(anyString())).thenReturn(createTagDetailsBean(1));
        when(tagsDao.addTagMappingDetails(any())).thenReturn(1);
        when(accountsDao.getAccountAnomalyConfiguration(anyInt())).thenReturn(null);
        doThrow(new HealControlCenterException("Anomaly config failed")).when(controllerDao).insertApplicationAnomalyConfigurations(any());
        assertThrows(DataProcessingException.class, () -> postApplicationsBL.process(input));
    }

    @Test
    void testProcess_notificationPrefException() throws Exception {
        ApplicationBean appBean = ApplicationBean.builder()
                .name("TestApp")
                .identifier("test-app")
                .accountId(10)
                .environment("dev")
                .userId("user-1")
                .severity(new ArrayList<>())
                .tags(new ArrayList<>())
                .status(1)
                .build();
        UtilityBean<List<ApplicationBean>> input = UtilityBean.<List<ApplicationBean>>builder()
                .pojoObject(List.of(appBean))
                .metadata(Map.of(Constants.USER_ID_KEY, "user-1", Constants.TIME_ZONE_TAG, new TimezoneBean()))
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .build();
        dateTimeUtilMockedStatic.when(DateTimeUtil::getCurrentTimestampInGMT).thenReturn(Timestamp.valueOf("2025-07-17 10:00:00"));
        when(controllerDao.insertController(any())).thenReturn(1);
        when(tagsDao.getTagDetailsByName(anyString())).thenReturn(createTagDetailsBean(1));
        when(tagsDao.addTagMappingDetails(any())).thenReturn(1);
        when(accountsDao.getAccountAnomalyConfiguration(anyInt())).thenReturn(null);
        doNothing().when(controllerDao).insertApplicationAnomalyConfigurations(any());
        assertThrows(DataProcessingException.class, () -> postApplicationsBL.process(input));
    }

    @Test
    void testProcess_percentileException() throws Exception {
        ApplicationBean appBean = ApplicationBean.builder()
                .name("TestApp")
                .identifier("test-app")
                .accountId(10)
                .environment("dev")
                .userId("user-1")
                .severity(new ArrayList<>())
                .tags(new ArrayList<>())
                .status(1)
                .build();
        UtilityBean<List<ApplicationBean>> input = UtilityBean.<List<ApplicationBean>>builder()
                .pojoObject(List.of(appBean))
                .metadata(Map.of(Constants.USER_ID_KEY, "user-1", Constants.TIME_ZONE_TAG, new TimezoneBean()))
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .build();
        dateTimeUtilMockedStatic.when(DateTimeUtil::getCurrentTimestampInGMT).thenReturn(Timestamp.valueOf("2025-07-17 10:00:00"));
        when(controllerDao.insertController(any())).thenReturn(1);
        when(tagsDao.getTagDetailsByName(anyString())).thenReturn(createTagDetailsBean(1));
        when(tagsDao.addTagMappingDetails(any())).thenReturn(1);
        when(accountsDao.getAccountAnomalyConfiguration(anyInt())).thenReturn(null);
        doNothing().when(controllerDao).insertApplicationAnomalyConfigurations(any());
        assertThrows(DataProcessingException.class, () -> postApplicationsBL.process(input));
    }

    @Test
    void testProcess_redisUpdateException() throws Exception {
        ApplicationBean appBean = ApplicationBean.builder()
                .name("TestApp")
                .identifier("test-app")
                .accountId(10)
                .environment("dev")
                .userId("user-1")
                .severity(new ArrayList<>())
                .tags(new ArrayList<>())
                .status(1)
                .build();
        UtilityBean<List<ApplicationBean>> input = UtilityBean.<List<ApplicationBean>>builder()
                .pojoObject(List.of(appBean))
                .metadata(Map.of(Constants.USER_ID_KEY, "user-1", Constants.TIME_ZONE_TAG, new TimezoneBean()))
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .build();
        dateTimeUtilMockedStatic.when(DateTimeUtil::getCurrentTimestampInGMT).thenReturn(Timestamp.valueOf("2025-07-17 10:00:00"));
        when(controllerDao.insertController(any())).thenReturn(1);
        when(tagsDao.getTagDetailsByName(anyString())).thenReturn(createTagDetailsBean(1));
        when(tagsDao.addTagMappingDetails(any())).thenReturn(1);
        when(accountsDao.getAccountAnomalyConfiguration(anyInt())).thenReturn(null);
        doNothing().when(controllerDao).insertApplicationAnomalyConfigurations(any());
        assertThrows(DataProcessingException.class, () -> postApplicationsBL.process(input));
    }

    @Test
    void testInsertController_success() throws Exception {
        ApplicationBean appBean = ApplicationBean.builder()
                .name("TestApp")
                .identifier("test-app")
                .accountId(10)
                .environment("dev")
                .status(1)
                .build();
        when(controllerDao.insertController(any())).thenReturn(123);
        int id = postApplicationsBL.insertController(appBean, "user-1", "2025-07-17 10:00:00");
        assertEquals(123, id);
    }

    @Test
    void testInsertController_exception() throws Exception {
        ApplicationBean appBean = ApplicationBean.builder()
                .name("TestApp")
                .identifier("test-app")
                .accountId(10)
                .environment("dev")
                .status(1)
                .build();
        when(controllerDao.insertController(any())).thenThrow(new HealControlCenterException("Insert failed"));
        assertThrows(HealControlCenterException.class, () -> postApplicationsBL.insertController(appBean, "user-1", "2025-07-17 10:00:00"));
    }

    @Test
    void testBuildPercentileMap_variousCases() throws DataProcessingException {
        String[] kpis = {"response_time_95_percentile", "throughput_99_percentile", "bad_format", "latency_abc_percentile"};
        Map<String, Set<Double>> result = invokeBuildPercentileMap(kpis, "percentile");
        assertTrue(result.containsKey("response_time"));
        assertTrue(result.containsKey("throughput"));
        assertEquals(Set.of(95.0), result.get("response_time"));
        assertEquals(Set.of(99.0), result.get("throughput"));
        assertFalse(result.containsKey("bad_format"));
        assertFalse(result.containsKey("latency"));
    }

    // Helper to access private buildPercentileMap
    private Map<String, Set<Double>> invokeBuildPercentileMap(String[] kpis, String suffix) throws DataProcessingException {
        try {
            var method = PostApplicationsBL.class.getDeclaredMethod("buildPercentileMap", String[].class, String.class);
            method.setAccessible(true);
            return (Map<String, Set<Double>>) method.invoke(postApplicationsBL, kpis, suffix);
        } catch (Exception e) {
            throw new DataProcessingException(e.getMessage());
        }
    }

    @Test
    void testHandleLinkedApplications_validAndInvalid() throws Exception {
        ApplicationBean appBean = ApplicationBean.builder()
                .name("TestApp")
                .identifier("test-app")
                .accountId(10)
                .environment("dr")
                .userId("user-1")
                .linkedEnvironment(createLinkedEnvironment("dr", null, "AppDC"))
                .status(1)
                .build();
        when(controllerDao.getApplicationIdByName("AppDC")).thenReturn(createControllerBean("appdc_1"));
        when(controllerDao.insertApplicationAlias(any())).thenReturn(1);
        // Use reflection to call private method
        var method = PostApplicationsBL.class.getDeclaredMethod("handleLinkedApplications", ApplicationBean.class, String.class, String.class);
        method.setAccessible(true);
        method.invoke(postApplicationsBL, appBean, "user-1", "2025-07-17 10:00:00");
        // Now test with invalid environment
        appBean.setEnvironment("invalid");
        method.invoke(postApplicationsBL, appBean, "user-1", "2025-07-17 10:00:00");
    }

    // Helper to create TagDetailsBean
    private TagDetailsBean createTagDetailsBean(int id) {
        TagDetailsBean bean = new TagDetailsBean();
        bean.setId(id);
        return bean;
    }

    // Helper to create SeverityLevel
    private SeverityLevel createSeverityLevel(String type, int status) {
        SeverityLevel level = new SeverityLevel();
        level.setType(type);
        level.setStatus(status);
        return level;
    }

    // Helper to create LinkedEnvironment
    private com.heal.controlcenter.pojo.LinkedEnvironment createLinkedEnvironment(String env, String account, String mappedToApp) {
        com.heal.controlcenter.pojo.LinkedEnvironment le = new com.heal.controlcenter.pojo.LinkedEnvironment();
        le.setEnvironment(env);
        le.setAccount(account);
        le.setMappedToApplication(mappedToApp);
        return le;
    }

    // Helper to create ControllerBean
    private com.heal.controlcenter.beans.ControllerBean createControllerBean(String identifier) {
        com.heal.controlcenter.beans.ControllerBean bean = new com.heal.controlcenter.beans.ControllerBean();
        bean.setIdentifier(identifier);
        return bean;
    }
}
