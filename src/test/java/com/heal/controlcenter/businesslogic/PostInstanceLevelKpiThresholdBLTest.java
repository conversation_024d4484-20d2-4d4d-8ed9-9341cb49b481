package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.ThresholdDao;
import com.heal.controlcenter.dao.mysql.entity.InstanceKpiAttributeThresholdBean;
import com.heal.controlcenter.dao.opensearch.InstanceKpiThresholdRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.InstanceKpiThresholdDetails;
import com.heal.controlcenter.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class PostInstanceLevelKpiThresholdBLTest {

    @InjectMocks
    private PostInstanceLevelKpiThresholdBL postInstanceLevelKpiThresholdBL;

    @Mock
    private ClientValidationUtils clientValidationUtils;

    @Mock
    private InstanceLevelKpiThresholdUtil instanceLevelKpiThresholdUtil;

    @Mock
    private ServerValidationUtils serverValidationUtils;

    @Mock
    private ThresholdDao thresholdDao;

    @Mock
    private InstanceKpiThresholdRepo instanceKpiThresholdRepo;


    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testClientValidation_ValidInputs() throws ClientException, HealControlCenterException {
        InstanceKpiThresholdDetails details = new InstanceKpiThresholdDetails();
        String[] params = {"auth", "acc", "extra"};
        doNothing().when(clientValidationUtils).nullRequestBodyCheck(details, UIMessages.REQUEST_BODY_NULL);
        doNothing().when(clientValidationUtils).nullOrEmptyCheck("auth", UIMessages.AUTH_KEY_INVALID);
        doNothing().when(clientValidationUtils).nullOrEmptyCheck("acc", UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        doNothing().when(instanceLevelKpiThresholdUtil).validateInstanceKpiThresholdDetails(details, true);

        UtilityBean<InstanceKpiThresholdDetails> result = postInstanceLevelKpiThresholdBL.clientValidation(details, params);

        assertNotNull(result);
        assertEquals(details, result.getPojoObject());
        assertEquals("auth", result.getRequestParams().get(Constants.AUTH_KEY));
        assertEquals("acc", result.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
    }

    @Test
    void testClientValidation_NullRequestBody() throws ClientException {
        String[] params = {"auth", "acc", "extra"};
        doThrow(new ClientException(UIMessages.REQUEST_BODY_NULL))
                .when(clientValidationUtils).nullRequestBodyCheck(null, UIMessages.REQUEST_BODY_NULL);

        ClientException ex = assertThrows(ClientException.class,
                () -> postInstanceLevelKpiThresholdBL.clientValidation(null, params));
        assertEquals("ClientException : " + UIMessages.REQUEST_BODY_NULL, ex.getMessage());
    }

    @Test
    void testClientValidation_NullOrEmptyAuthKey() throws ClientException {
        InstanceKpiThresholdDetails details = new InstanceKpiThresholdDetails();
        String[] params = {null, "acc", "extra"};
        doNothing().when(clientValidationUtils).nullRequestBodyCheck(details, UIMessages.REQUEST_BODY_NULL);
        doThrow(new ClientException(UIMessages.AUTH_KEY_INVALID))
                .when(clientValidationUtils).nullOrEmptyCheck(null, UIMessages.AUTH_KEY_INVALID);

        ClientException ex = assertThrows(ClientException.class,
                () -> postInstanceLevelKpiThresholdBL.clientValidation(details, params));
        assertEquals("ClientException : " + UIMessages.AUTH_KEY_INVALID, ex.getMessage());
    }

    @Test
    void testClientValidation_NullOrEmptyAccountIdentifier() throws ClientException {
        InstanceKpiThresholdDetails details = new InstanceKpiThresholdDetails();
        String[] params = {"auth", "", "extra"};
        doNothing().when(clientValidationUtils).nullRequestBodyCheck(details, UIMessages.REQUEST_BODY_NULL);
        doNothing().when(clientValidationUtils).nullOrEmptyCheck("auth", UIMessages.AUTH_KEY_INVALID);
        doThrow(new ClientException(UIMessages.ACCOUNT_IDENTIFIER_INVALID))
                .when(clientValidationUtils).nullOrEmptyCheck("", UIMessages.ACCOUNT_IDENTIFIER_INVALID);

        ClientException ex = assertThrows(ClientException.class,
                () -> postInstanceLevelKpiThresholdBL.clientValidation(details, params));
        assertEquals("ClientException : " + UIMessages.ACCOUNT_IDENTIFIER_INVALID, ex.getMessage());
    }

    @Test
    void testClientValidation_ValidationUtilThrows() throws ClientException, HealControlCenterException {
        InstanceKpiThresholdDetails details = new InstanceKpiThresholdDetails();
        String[] params = {"auth", "acc", "extra"};
        doNothing().when(clientValidationUtils).nullRequestBodyCheck(details, UIMessages.REQUEST_BODY_NULL);
        doNothing().when(clientValidationUtils).nullOrEmptyCheck("auth", UIMessages.AUTH_KEY_INVALID);
        doNothing().when(clientValidationUtils).nullOrEmptyCheck("acc", UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        doThrow(new HealControlCenterException("validation failed"))
                .when(instanceLevelKpiThresholdUtil).validateInstanceKpiThresholdDetails(details, true);

        ClientException ex = assertThrows(ClientException.class,
                () -> postInstanceLevelKpiThresholdBL.clientValidation(details, params));
        assertEquals("ClientException : validation failed", ex.getMessage());
    }

    // server validation test cases
    @Test
    void testServerValidation_HappyPath() throws Exception {
        InstanceKpiThresholdDetails details = new InstanceKpiThresholdDetails();
        Map<Integer, String> compInstIdToIdentifierMap = Map.of(1, "i1");
        Account account = Account.builder().id(100).identifier("acc").build();
        UtilityBean<InstanceKpiThresholdDetails> utilityBean = UtilityBean.<InstanceKpiThresholdDetails>builder()
                .pojoObject(details)
                .requestParams(Map.of(Constants.AUTH_KEY, "user1", Constants.ACCOUNT_IDENTIFIER, "acc"))
                .build();

        when(serverValidationUtils.authKeyValidation("user1")).thenReturn("user1");
        when(serverValidationUtils.accountValidation("acc")).thenReturn(account);
        when(instanceLevelKpiThresholdUtil.verifyInstanceIdKpiAndGroupKpi(details, 100)).thenReturn(compInstIdToIdentifierMap);
        when(instanceLevelKpiThresholdUtil.kpiAndCompInstanceValidation(details, compInstIdToIdentifierMap, "user1", 100, "acc", true))
                .thenReturn(List.of(new InstanceKpiAttributeThresholdBean()));

        List<InstanceKpiAttributeThresholdBean> result = postInstanceLevelKpiThresholdBL.serverValidation(utilityBean);

        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    void testServerValidation_AuthKeyValidationThrows() throws Exception {
        InstanceKpiThresholdDetails details = new InstanceKpiThresholdDetails();
        UtilityBean<InstanceKpiThresholdDetails> utilityBean = UtilityBean.<InstanceKpiThresholdDetails>builder()
                .pojoObject(details)
                .requestParams(Map.of(Constants.AUTH_KEY, "user1", Constants.ACCOUNT_IDENTIFIER, "acc"))
                .build();

        when(serverValidationUtils.authKeyValidation("user1")).thenThrow(new ServerException("auth error"));

        ServerException ex = assertThrows(ServerException.class, () ->
                postInstanceLevelKpiThresholdBL.serverValidation(utilityBean));
        assertTrue(ex.getMessage().contains("auth error"));
    }

    @Test
    void testServerValidation_AccountValidationThrows() throws Exception {
        InstanceKpiThresholdDetails details = new InstanceKpiThresholdDetails();
        UtilityBean<InstanceKpiThresholdDetails> utilityBean = UtilityBean.<InstanceKpiThresholdDetails>builder()
                .pojoObject(details)
                .requestParams(Map.of(Constants.AUTH_KEY, "user1", Constants.ACCOUNT_IDENTIFIER, "acc"))
                .build();

        when(serverValidationUtils.authKeyValidation("user1")).thenReturn("user1");
        when(serverValidationUtils.accountValidation("acc")).thenThrow(new ServerException("account error"));

        ServerException ex = assertThrows(ServerException.class, () ->
                postInstanceLevelKpiThresholdBL.serverValidation(utilityBean));
        assertTrue(ex.getMessage().contains("account error"));
    }

    @Test
    void testServerValidation_VerifyInstanceIdKpiAndGroupKpiThrows() throws Exception {
        InstanceKpiThresholdDetails details = new InstanceKpiThresholdDetails();
        Account account = Account.builder().id(100).identifier("acc").build();
        UtilityBean<InstanceKpiThresholdDetails> utilityBean = UtilityBean.<InstanceKpiThresholdDetails>builder()
                .pojoObject(details)
                .requestParams(Map.of(Constants.AUTH_KEY, "user1", Constants.ACCOUNT_IDENTIFIER, "acc"))
                .build();

        when(serverValidationUtils.authKeyValidation("user1")).thenReturn("user1");
        when(serverValidationUtils.accountValidation("acc")).thenReturn(account);
        when(instanceLevelKpiThresholdUtil.verifyInstanceIdKpiAndGroupKpi(details, 100))
                .thenThrow(new HealControlCenterException("verify error"));

        ServerException ex = assertThrows(ServerException.class, () ->
                postInstanceLevelKpiThresholdBL.serverValidation(utilityBean));
        assertTrue(ex.getMessage().contains("verify error"));
    }

    @Test
    void testServerValidation_KpiAndCompInstanceValidationThrows() throws Exception {
        InstanceKpiThresholdDetails details = new InstanceKpiThresholdDetails();
        Account account = Account.builder().id(100).identifier("acc").build();
        Map<Integer, String> compInstIdToIdentifierMap = Map.of(1, "i1");
        UtilityBean<InstanceKpiThresholdDetails> utilityBean = UtilityBean.<InstanceKpiThresholdDetails>builder()
                .pojoObject(details)
                .requestParams(Map.of(Constants.AUTH_KEY, "user1", Constants.ACCOUNT_IDENTIFIER, "acc"))
                .build();

        when(serverValidationUtils.authKeyValidation("user1")).thenReturn("user1");
        when(serverValidationUtils.accountValidation("acc")).thenReturn(account);
        when(instanceLevelKpiThresholdUtil.verifyInstanceIdKpiAndGroupKpi(details, 100)).thenReturn(compInstIdToIdentifierMap);
        when(instanceLevelKpiThresholdUtil.kpiAndCompInstanceValidation(details, compInstIdToIdentifierMap, "user1", 100, "acc", true))
                .thenThrow(new HealControlCenterException("validation error"));

        ServerException ex = assertThrows(ServerException.class, () ->
                postInstanceLevelKpiThresholdBL.serverValidation(utilityBean));
        assertTrue(ex.getMessage().contains("validation error"));
    }

    // process test cases
    @Test
    void testProcess_Success() throws Exception {
        InstanceKpiAttributeThresholdBean bean = new InstanceKpiAttributeThresholdBean();
        List<InstanceKpiAttributeThresholdBean> beans = List.of(bean);

        when(thresholdDao.addInstanceKpiAttributeLevelThresholds(beans)).thenReturn(new int[]{1});
        doNothing().when(instanceKpiThresholdRepo).addInstanceKpiThresholdInBulk(beans);
        doNothing().when(instanceLevelKpiThresholdUtil).addInstanceKpiAttributeLevelThresholdsInRedis(beans);

        String result = postInstanceLevelKpiThresholdBL.process(beans);

        assertEquals("Instance Level KPI Attribute thresholds added successfully", result);
        verify(thresholdDao).addInstanceKpiAttributeLevelThresholds(beans);
        verify(instanceKpiThresholdRepo).addInstanceKpiThresholdInBulk(beans);
        verify(instanceLevelKpiThresholdUtil).addInstanceKpiAttributeLevelThresholdsInRedis(beans);
    }

    @Test
    void testProcess_NullInput_ThrowsException() {
        DataProcessingException ex = assertThrows(DataProcessingException.class,
                () -> postInstanceLevelKpiThresholdBL.process(null));
        assertTrue(ex.getMessage().contains("Threshold beans are null or empty"));
    }

    @Test
    void testProcess_EmptyInput_ThrowsException() {
        DataProcessingException ex = assertThrows(DataProcessingException.class,
                () -> postInstanceLevelKpiThresholdBL.process(Collections.emptyList()));
        assertTrue(ex.getMessage().contains("Threshold beans are null or empty"));
    }

    @Test
    void testProcess_PerconaInsertFails_ThrowsException() {
        InstanceKpiAttributeThresholdBean bean = new InstanceKpiAttributeThresholdBean();
        List<InstanceKpiAttributeThresholdBean> beans = List.of(bean);

        when(thresholdDao.addInstanceKpiAttributeLevelThresholds(beans)).thenReturn(new int[0]);

        DataProcessingException ex = assertThrows(DataProcessingException.class,
                () -> postInstanceLevelKpiThresholdBL.process(beans));
        assertTrue(ex.getMessage().contains("Error while adding attribute level thresholds"));
    }

    @Test
    void testProcess_OpenSearchFails_ThrowsException() throws Exception {
        InstanceKpiAttributeThresholdBean bean = new InstanceKpiAttributeThresholdBean();
        List<InstanceKpiAttributeThresholdBean> beans = List.of(bean);

        when(thresholdDao.addInstanceKpiAttributeLevelThresholds(beans)).thenReturn(new int[]{1});
        doThrow(new HealControlCenterException("OpenSearch error"))
                .when(instanceKpiThresholdRepo).addInstanceKpiThresholdInBulk(beans);

        DataProcessingException ex = assertThrows(DataProcessingException.class,
                () -> postInstanceLevelKpiThresholdBL.process(beans));
        assertTrue(ex.getMessage().contains("Error while adding the thresholds to OpenSearch"));
    }
}
