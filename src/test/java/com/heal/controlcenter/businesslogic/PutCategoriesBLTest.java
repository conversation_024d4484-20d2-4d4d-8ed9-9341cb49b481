package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.beans.CategoryDetailBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.CategoryDao;
import com.heal.controlcenter.dao.redis.CategoryRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.CategoryDetails;
import com.heal.controlcenter.pojo.IdPojo;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.DateTimeUtil;
import com.heal.controlcenter.util.ServerValidationUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class PutCategoriesBLTest {
    @Mock
    CategoryDao categoryDao;
    @Mock
    ClientValidationUtils clientValidationUtils;
    @Mock
    ServerValidationUtils serverValidationUtils;
    @Mock
    CategoryRepo categoryRepo;

    @InjectMocks
    PutCategoriesBL putCategoriesBL;

    private CategoryDetails validCategory;
    private String accountIdentifier = "acc-1";
    private CategoryDetailBean categoryDetailBean;
    private MockedStatic<DateTimeUtil> dateTimeUtilMockedStatic;

    @BeforeEach
    void setup() {
        validCategory = CategoryDetails.builder()
                .name("TestCategory")
                .identifier("test-category")
                .description("desc")
                .type("CUSTOM")
                .subType("Workload")
                .status(1)
                .build();
        categoryDetailBean = CategoryDetailBean.builder()
                .id(10)
                .name("TestCategory")
                .identifier("test-category")
                .accountId(10)
                .description("desc")
                .userDetailsId("user-1")
                .createdTime("2025-07-23 10:00:00")
                .updatedTime("2025-07-23 10:00:00")
                .status(1)
                .isCustom(1)
                .isInformative(0)
                .isWorkLoad(1)
                .accountIdentifier(accountIdentifier)
                .build();
        
        dateTimeUtilMockedStatic = Mockito.mockStatic(DateTimeUtil.class);
    }

    @AfterEach
    void tearDown() {
        dateTimeUtilMockedStatic.close();
    }

    /**
     * Tests client-side validation for updating categories.
     */
    @Test
    void testClientValidation_success() throws ClientException {
        List<CategoryDetails> categories = Collections.singletonList(validCategory);
        doNothing().when(clientValidationUtils).accountIdentifierValidation(accountIdentifier);
        
        UtilityBean<List<CategoryDetails>> result = putCategoriesBL.clientValidation(categories, accountIdentifier);
        
        assertNotNull(result);
        assertEquals(categories, result.getPojoObject());
        assertEquals(accountIdentifier, result.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
    }

    /**
     * Tests server-side validation for updating categories.
     */
    @Test
    void testServerValidation_success() throws Exception {
        List<CategoryDetails> categories = Collections.singletonList(validCategory);
        Map<String, String> reqParams = new HashMap<>();
        reqParams.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID_KEY, "user-1");
        UtilityBean<List<CategoryDetails>> input = UtilityBean.<List<CategoryDetails>>builder()
                .pojoObject(categories)
                .requestParams(reqParams)
                .metadata(metadata)
                .build();
        Account mockAccount = new Account();
        mockAccount.setId(10);
        
        when(serverValidationUtils.accountValidation(accountIdentifier)).thenReturn(mockAccount);
        when(categoryDao.getCategoryByAccountIdAndIdentifier(anyInt(), anyString())).thenReturn(categoryDetailBean);
        when(categoryDao.getCategoriesForAccount(anyInt())).thenReturn(Collections.singletonList(categoryDetailBean));
        when(categoryDao.getKpiCountForCategory(anyInt())).thenReturn(0);
        Timestamp mockTimestamp = Timestamp.valueOf("2025-07-23 10:00:00");
        dateTimeUtilMockedStatic.when(DateTimeUtil::getCurrentTimestampInGMT).thenReturn(mockTimestamp);
        
        UtilityBean<List<CategoryDetailBean>> result = putCategoriesBL.serverValidation(input);
        
        assertNotNull(result);
        assertEquals(1, result.getPojoObject().size());
        assertEquals("TestCategory", result.getPojoObject().get(0).getName());
    }

    /**
     * Tests process logic for successful category update and Redis update.
     */
    @Test
    void testProcess_success() throws Exception {
        UtilityBean<List<CategoryDetailBean>> input = UtilityBean.<List<CategoryDetailBean>>builder()
                .pojoObject(List.of(categoryDetailBean))
                .metadata(Map.of(Constants.USER_ID_KEY, "user-1"))
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .build();
        
        when(categoryDao.updateCategory(any())).thenReturn(10);
        when(categoryRepo.getCategoryDetails(anyString())).thenReturn(new ArrayList<>());
        doNothing().when(categoryRepo).updateCategoryDetails(anyString(), any());
        doNothing().when(categoryRepo).updateCategory(anyString(), any());
        
        List<IdPojo> result = putCategoriesBL.process(input);
        
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("TestCategory", result.get(0).getName());
    }

    /**
     * Tests process logic for category update failure.
     */
    @Test
    void testProcess_updateCategoryException() throws Exception {
        UtilityBean<List<CategoryDetailBean>> input = UtilityBean.<List<CategoryDetailBean>>builder()
                .pojoObject(List.of(categoryDetailBean))
                .metadata(Map.of(Constants.USER_ID_KEY, "user-1"))
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .build();
        
        when(categoryDao.updateCategory(any())).thenThrow(new RuntimeException("Update failed"));
        
        assertThrows(DataProcessingException.class, () -> putCategoriesBL.process(input));
    }

    /**
     * Tests process logic for Redis update failure.
     */
    @Test
    void testProcess_redisUpdateException() throws Exception {
        UtilityBean<List<CategoryDetailBean>> input = UtilityBean.<List<CategoryDetailBean>>builder()
                .pojoObject(List.of(categoryDetailBean))
                .metadata(Map.of(Constants.USER_ID_KEY, "user-1"))
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .build();
        
        when(categoryDao.updateCategory(any())).thenReturn(10);
        when(categoryRepo.getCategoryDetails(anyString())).thenReturn(new ArrayList<>());
        doThrow(new RuntimeException("Redis update failed")).when(categoryRepo).updateCategoryDetails(anyString(), any());
        
        assertThrows(DataProcessingException.class, () -> putCategoriesBL.process(input));
    }

    /**
     * Tests updateCategoryInRedis for exception when fetching categories from Redis.
     */
    @Test
    void testUpdateCategoryInRedis_fetchCategoriesThrowsException() throws Exception {
        when(categoryRepo.getCategoryDetails(anyString())).thenThrow(new RuntimeException("Redis fetch failed"));
        doNothing().when(categoryRepo).updateCategoryDetails(anyString(), any());
        doNothing().when(categoryRepo).updateCategory(anyString(), any());
        
        assertDoesNotThrow(() -> putCategoriesBL.updateCategoryInRedis(categoryDetailBean, 10));
    }

    /**
     * Tests server-side validation for standard category update (valid case).
     */
    @Test
    void testServerValidation_standardCategory_success() throws Exception {
        CategoryDetails standardCategory = CategoryDetails.builder()
                .name("StandardCategory")
                .identifier("standard-category")
                .description("desc")
                .type("STANDARD")
                .subType("Info")
                .status(1)
                .build();
        
        CategoryDetailBean standardBean = CategoryDetailBean.builder()
                .id(20)
                .name("StandardCategory")
                .identifier("standard-category")
                .accountId(10)
                .description("desc")
                .userDetailsId("user-2")
                .createdTime("2025-07-23 10:00:00")
                .updatedTime("2025-07-23 10:00:00")
                .status(1)
                .isCustom(0)
                .isInformative(1)
                .isWorkLoad(0)
                .accountIdentifier(accountIdentifier)
                .build();

        List<CategoryDetails> categories = Collections.singletonList(standardCategory);
        Map<String, String> reqParams = new HashMap<>();
        reqParams.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID_KEY, "user-2");
        UtilityBean<List<CategoryDetails>> input = UtilityBean.<List<CategoryDetails>>builder()
                .pojoObject(categories)
                .requestParams(reqParams)
                .metadata(metadata)
                .build();
        
        Account mockAccount = new Account();
        mockAccount.setId(10);
        
        when(serverValidationUtils.accountValidation(accountIdentifier)).thenReturn(mockAccount);
        when(categoryDao.getCategoryByAccountIdAndIdentifier(anyInt(), anyString())).thenReturn(standardBean);
        when(categoryDao.getCategoriesForAccount(anyInt())).thenReturn(Collections.singletonList(standardBean));
        when(categoryDao.getKpiCountForCategory(anyInt())).thenReturn(0);
        Timestamp mockTimestamp = Timestamp.valueOf("2025-07-23 10:00:00");
        dateTimeUtilMockedStatic.when(DateTimeUtil::getCurrentTimestampInGMT).thenReturn(mockTimestamp);
        
        UtilityBean<List<CategoryDetailBean>> result = putCategoriesBL.serverValidation(input);
        
        assertNotNull(result);
        assertEquals(2, result.getPojoObject().size());
        assertEquals("StandardCategory", result.getPojoObject().get(0).getName());
        assertEquals(0, result.getPojoObject().get(0).getIsCustom());
        assertEquals(1, result.getPojoObject().get(0).getIsInformative());
        assertEquals(0, result.getPojoObject().get(0).getIsWorkLoad());
    }

    /**
     * Tests exception when modifying fields other than description for standard category.
     */
    @Test
    void testServerValidation_standardCategory_invalidModification() throws Exception {
        CategoryDetailBean standardCategory = CategoryDetailBean.builder()
                .id(21)
                .name("StandardCategory")
                .identifier("standard-category")
                .accountId(10)
                .description("desc")
                .userDetailsId("user-2")
                .createdTime("2025-07-23 10:00:00")
                .updatedTime("2025-07-23 10:00:00")
                .status(1)
                .isCustom(0)
                .isInformative(0)
                .isWorkLoad(1)
                .accountIdentifier(accountIdentifier)
                .build();
        // Try to change name and status
        CategoryDetails standardCategoryDetails = CategoryDetails.builder()
                .name("ChangedName")
                .identifier("standard-category")
                .description("desc")
                .type("STANDARD")
                .subType("Workload")
                .status(0)
                .build();
        List<CategoryDetails> categories = Collections.singletonList(standardCategoryDetails);
        Map<String, String> reqParams = new HashMap<>();
        reqParams.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        UtilityBean<List<CategoryDetails>> input = UtilityBean.<List<CategoryDetails>>builder()
                .pojoObject(categories)
                .requestParams(reqParams)
                .metadata(new HashMap<>())
                .build();
        Account mockAccount = new Account();
        mockAccount.setId(10);
        when(serverValidationUtils.accountValidation(accountIdentifier)).thenReturn(mockAccount);
        when(categoryDao.getCategoryByAccountIdAndIdentifier(anyInt(), anyString())).thenReturn(standardCategory);
        when(categoryDao.getCategoriesForAccount(anyInt())).thenReturn(Collections.singletonList(standardCategory));
        when(categoryDao.getKpiCountForCategory(anyInt())).thenReturn(0);
        assertThrows(ServerException.class, () -> putCategoriesBL.serverValidation(input));
    }

    /**
     * Tests exception when description is null/empty for standard category.
     */
    @Test
    void testServerValidation_standardCategory_emptyDescription() throws Exception {
        CategoryDetailBean standardCategory = CategoryDetailBean.builder()
                .id(22)
                .name("StandardCategory")
                .identifier("standard-category")
                .accountId(10)
                .description("desc")
                .userDetailsId("user-2")
                .createdTime("2025-07-23 10:00:00")
                .updatedTime("2025-07-23 10:00:00")
                .status(1)
                .isCustom(0)
                .isInformative(0)
                .isWorkLoad(1)
                .accountIdentifier(accountIdentifier)
                .build();
        CategoryDetails standardCategoryDetails = CategoryDetails.builder()
                .name("StandardCategory")
                .identifier("standard-category")
                .description("") // empty description
                .type("STANDARD")
                .subType("Workload")
                .status(1)
                .build();
        List<CategoryDetails> categories = Collections.singletonList(standardCategoryDetails);
        Map<String, String> reqParams = new HashMap<>();
        reqParams.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        UtilityBean<List<CategoryDetails>> input = UtilityBean.<List<CategoryDetails>>builder()
                .pojoObject(categories)
                .requestParams(reqParams)
                .metadata(new HashMap<>())
                .build();
        Account mockAccount = new Account();
        mockAccount.setId(10);
        when(serverValidationUtils.accountValidation(accountIdentifier)).thenReturn(mockAccount);
        when(categoryDao.getCategoryByAccountIdAndIdentifier(anyInt(), anyString())).thenReturn(standardCategory);
        when(categoryDao.getCategoriesForAccount(anyInt())).thenReturn(Collections.singletonList(standardCategory));
        when(categoryDao.getKpiCountForCategory(anyInt())).thenReturn(0);
        assertThrows(ServerException.class, () -> putCategoriesBL.serverValidation(input));
    }

    /**
     * Tests exception when fetching KPI count fails.
     */
    @Test
    void testServerValidation_kpiCountFetchException() throws Exception {
        CategoryDetailBean customCategory = CategoryDetailBean.builder()
                .id(23)
                .name("CustomCategory")
                .identifier("custom-category")
                .accountId(10)
                .description("desc")
                .userDetailsId("user-3")
                .createdTime("2025-07-23 10:00:00")
                .updatedTime("2025-07-23 10:00:00")
                .status(1)
                .isCustom(1)
                .isInformative(0)
                .isWorkLoad(1)
                .accountIdentifier(accountIdentifier)
                .build();
        CategoryDetails customCategoryDetails = CategoryDetails.builder()
                .name("CustomCategory")
                .identifier("custom-category")
                .description("desc")
                .type("CUSTOM")
                .subType("Workload")
                .status(1)
                .build();
        List<CategoryDetails> categories = Collections.singletonList(customCategoryDetails);
        Map<String, String> reqParams = new HashMap<>();
        reqParams.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        UtilityBean<List<CategoryDetails>> input = UtilityBean.<List<CategoryDetails>>builder()
                .pojoObject(categories)
                .requestParams(reqParams)
                .metadata(new HashMap<>())
                .build();
        Account mockAccount = new Account();
        mockAccount.setId(10);
        when(serverValidationUtils.accountValidation(accountIdentifier)).thenReturn(mockAccount);
        when(categoryDao.getCategoryByAccountIdAndIdentifier(anyInt(), anyString())).thenReturn(customCategory);
        when(categoryDao.getCategoriesForAccount(anyInt())).thenReturn(Collections.singletonList(customCategory));
        when(categoryDao.getKpiCountForCategory(anyInt())).thenThrow(new RuntimeException("DB error"));
        assertThrows(ServerException.class, () -> putCategoriesBL.serverValidation(input));
    }

    /**
     * Tests exception when category is not present for the specified account.
     */
    @Test
    void testServerValidation_categoryNotPresent() throws Exception {
        CategoryDetails details = CategoryDetails.builder()
                .name("MissingCategory")
                .identifier("missing-category")
                .description("desc")
                .type("CUSTOM")
                .subType("Workload")
                .status(1)
                .build();
        List<CategoryDetails> categories = Collections.singletonList(details);
        Map<String, String> reqParams = new HashMap<>();
        reqParams.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        UtilityBean<List<CategoryDetails>> input = UtilityBean.<List<CategoryDetails>>builder()
                .pojoObject(categories)
                .requestParams(reqParams)
                .metadata(new HashMap<>())
                .build();
        Account mockAccount = new Account();
        mockAccount.setId(10);
        when(serverValidationUtils.accountValidation(accountIdentifier)).thenReturn(mockAccount);
        when(categoryDao.getCategoryByAccountIdAndIdentifier(anyInt(), anyString())).thenReturn(null);
        assertThrows(ServerException.class, () -> putCategoriesBL.serverValidation(input));
    }
}
