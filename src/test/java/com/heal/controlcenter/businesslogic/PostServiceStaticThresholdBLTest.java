package com.heal.controlcenter.businesslogic;

import com.heal.configuration.entities.UserAccessDetails;
import com.heal.configuration.pojos.*;
import com.heal.controlcenter.beans.CompInstClusterDetailsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.utility.ThresholdSyncService;
import com.heal.controlcenter.dao.mysql.KPIDao;
import com.heal.controlcenter.dao.mysql.ServiceDao;
import com.heal.controlcenter.dao.mysql.entity.KpiDetailsBean;
import com.heal.controlcenter.dao.mysql.entity.ServiceKpiThreshold;
import com.heal.controlcenter.dao.opensearch.ServiceOpensearchRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.Applicable;
import com.heal.controlcenter.pojo.OperationTypeEnum;
import com.heal.controlcenter.pojo.StaticThresholdRules;
import com.heal.controlcenter.service.CommonDataService;
import com.heal.controlcenter.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PostServiceStaticThresholdBLTest {

    @InjectMocks
    private PostServiceStaticThresholdBL postServiceStaticThresholdBL;

    @Mock
    private CommonUtils commonUtils;
    @Mock
    private ServiceDao serviceDao;
    @Mock
    private DateTimeUtil dateTimeUtil;
    @Mock
    private ServiceOpensearchRepo serviceOpensearchRepo; // CORRECTED: Renamed mock to match field name
    @Mock
    private CacheWrapper cacheWrapper;
    @Mock
    private ClientValidationUtils clientValidationUtils;
    @Mock
    private ServerValidationUtils serverValidationUtils;
    @Mock
    private CommonDataService commonDataService;
    @Mock
    private KPIDao kpiDao;
    @Mock
    private ThresholdSyncService thresholdSyncService;

    private List<StaticThresholdRules> sorRuleList;
    private UtilityBean<List<StaticThresholdRules>> utilityBean;
    private Account account;
    private Service service;

    @BeforeEach
    void setUp() {
        // Common setup for test data
        sorRuleList = new ArrayList<>();
        StaticThresholdRules rule = new StaticThresholdRules();
        rule.setKpiId("101");
        rule.setKpiLevel("instances");
        rule.setKpiAttribute("cpu");
        ThresholdConfig thresholdConfig = new ThresholdConfig();
        thresholdConfig.setMin(90.0);
        thresholdConfig.setOperationType(OperationTypeEnum.LESS_THAN.getType());
        rule.setErrorThreshold(thresholdConfig);
        sorRuleList.add(rule);

        account = new Account();
        account.setId(1);
        account.setIdentifier("test-account");

        service = new Service();
        service.setId(1);
        service.setIdentifier("test-service");

        Map<String, String> requestParams = new HashMap<>();
        requestParams.put(Constants.ACCOUNT_IDENTIFIER, "test-account");
        requestParams.put(Constants.SERVICE_IDENTIFIER, "test-service");
        requestParams.put(Constants.KPI_TYPE, "Core");
        requestParams.put(Constants.THRESHOLD_TYPE, "Static");
        requestParams.put(Constants.AUTH_KEY, "dummy-auth-key");

        utilityBean = UtilityBean.<List<StaticThresholdRules>>builder()
                .pojoObject(sorRuleList)
                .requestParams(requestParams)
                .build();
    }

    //region clientValidation Tests
    @Test
    void clientValidation_success() throws ClientException {
        UtilityBean<List<StaticThresholdRules>> result = postServiceStaticThresholdBL.clientValidation(sorRuleList, "authKey", "account", "service", "Core", "Static");
        assertNotNull(result);
        assertEquals(sorRuleList, result.getPojoObject());
    }

    @Test
    void clientValidation_throwsException_onInvalidThresholdType() {
        assertThrows(ClientException.class, () -> postServiceStaticThresholdBL.clientValidation(sorRuleList, "authKey", "account", "service", "Core", "Dynamic"));
    }

    @Test
    void clientValidation_throwsException_onNullRuleList() {
        assertThrows(ClientException.class, () -> postServiceStaticThresholdBL.clientValidation(null, "authKey", "account", "service", "Core", "Static"));
    }

    @Test
    void clientValidation_throwsException_onDuplicateRules() {
        sorRuleList.add(sorRuleList.get(0)); // Add a duplicate
        assertThrows(ClientException.class, () -> postServiceStaticThresholdBL.clientValidation(sorRuleList, "authKey", "account", "service", "Core", "Static"));
    }
    //endregion

    //region serverValidation Tests
    @Test
    void serverValidation_success() throws ServerException, HealControlCenterException {
        // Mock dependencies for a successful validation
        when(serverValidationUtils.authKeyValidation(anyString())).thenReturn("test-user");
        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);
        when(serverValidationUtils.userAccessDetailsValidation(anyString(), anyString())).thenReturn(new UserAccessDetails());
        when(serverValidationUtils.serviceValidation(anyString(), anyString(), anyString(), any())).thenReturn(service);
        mockViewTypes();
        when(commonDataService.getComponentClusterList(anyInt(), anyInt()))
                .thenReturn(List.of(CompInstClusterDetailsBean.builder().commonVersionId(1).compId(1).build()));
        when(kpiDao.getAllKpiDetailsKpiList()).thenReturn(List.of(createKpiDetailsBean(101, "Core")));

        List<ServiceKpiThreshold> rules = List.of(ServiceKpiThreshold.builder().kpiId(101).applicableTo(Applicable.instances.toString()).kpiAttribute("ALL").build());
        when(serviceDao.getAllKpiThresholds(anyInt(), anyInt())).thenReturn(rules);

        UtilityBean<List<StaticThresholdRules>> result = postServiceStaticThresholdBL.serverValidation(utilityBean);

        assertNotNull(result);
        assertEquals("test-user", result.getMetadata().get(Constants.USER_ID));
    }

    @Test
    void serverValidation_throwsException_onInvalidKpi() throws ServerException, HealControlCenterException {
        when(serverValidationUtils.authKeyValidation(anyString())).thenReturn("test-user");
        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);
        when(serverValidationUtils.userAccessDetailsValidation(anyString(), anyString())).thenReturn(new UserAccessDetails());
        when(serverValidationUtils.serviceValidation(anyString(), anyString(), anyString(), any())).thenReturn(service);
        mockViewTypes();
        when(commonDataService.getComponentClusterList(anyInt(), anyInt())).thenReturn(List.of(new CompInstClusterDetailsBean()));
        // Return a KPI list that does NOT contain the one in the request (101)
        when(kpiDao.getAllKpiDetailsKpiList()).thenReturn(List.of(createKpiDetailsBean(202, "Core")));

        assertThrows(ServerException.class, () -> postServiceStaticThresholdBL.serverValidation(utilityBean));
    }

    @Test
    void serverValidation_throwsException_whenRuleItselfIsInvalid() throws ServerException {
        // Make the rule invalid by setting a field that its internal validate() would catch
        sorRuleList.get(0).setKpiId(null);

        when(serverValidationUtils.authKeyValidation(anyString())).thenReturn("test-user");
        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);
        when(serverValidationUtils.userAccessDetailsValidation(anyString(), anyString())).thenReturn(new UserAccessDetails());
        when(serverValidationUtils.serviceValidation(anyString(), anyString(), anyString(), any())).thenReturn(service);
        mockViewTypes();

        assertThrows(ServerException.class, () -> postServiceStaticThresholdBL.serverValidation(utilityBean));
    }
    //endregion

    //region Helper Methods for Mocks
    private void mockViewTypes() {
        Map<String, List<ViewTypes>> viewTypesMap = new HashMap<>();
        viewTypesMap.put(Constants.KPI_TYPE, List.of(createViewType("Core", 1)));
        viewTypesMap.put(Constants.THRESHOLD_SEVERITY_TYPE, List.of(
                createViewType("Low", 1),
                createViewType("Medium", 2),
                createViewType("High", 3)
        ));
        viewTypesMap.put(Constants.OPERATIONS_TYPE, List.of(createViewType("GREATER_THAN", 1)));

        when(cacheWrapper.getAllViewTypesIdMap()).thenReturn(viewTypesMap);
        when(commonUtils.getViewTypeByNameAndSubType(any(), eq(Constants.KPI_TYPE), anyString())).thenReturn(createViewType("Core", 1));
        when(commonUtils.getViewTypeByNameAndSubType(any(), eq(Constants.THRESHOLD_SEVERITY_TYPE), eq("Low"))).thenReturn(createViewType("Low", 1));
        when(commonUtils.getViewTypeByNameAndSubType(any(), eq(Constants.THRESHOLD_SEVERITY_TYPE), eq("Medium"))).thenReturn(createViewType("Medium", 2));
        when(commonUtils.getViewTypeByNameAndSubType(any(), eq(Constants.THRESHOLD_SEVERITY_TYPE), eq("High"))).thenReturn(createViewType("High", 3));
        when(commonUtils.getViewTypeByNameAndSubType(any(), eq(Constants.OPERATIONS_TYPE), anyString())).thenReturn(createViewType("GREATER_THAN", 1));
    }

    private ViewTypes createViewType(String subTypeName, int subTypeId) {
        ViewTypes vt = new ViewTypes();
        vt.setSubTypeName(subTypeName);
        vt.setSubTypeId(subTypeId);
        return vt;
    }

    private KpiDetailsBean createKpiDetailsBean(int kpiId, String kpiType) {
        KpiDetailsBean bean = new KpiDetailsBean();
        bean.setId(kpiId);
        bean.setCommonVersionId(1);
        bean.setComponentId(1);
        bean.setTypeId(kpiType.equals("Core") ? 1 : 2); // Assuming 1 is Core
        return bean;
    }

    private Map<String, Object> createMetadataMap() {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.ACCOUNT, account);
        metadata.put(Constants.SERVICE, service);
        metadata.put("Core", createViewType("Core", 1));
        metadata.put(Constants.THRESHOLD_SEVERITY_TYPE_LOW, createViewType("Low", 1));
        metadata.put(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM, createViewType("Medium", 2));
        metadata.put(Constants.THRESHOLD_SEVERITY_TYPE_HIGH, createViewType("High", 3));
        return metadata;
    }
    //endregion
}