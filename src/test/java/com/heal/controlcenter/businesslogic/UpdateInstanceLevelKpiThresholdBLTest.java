package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.CompInstKpiEntity;
import com.heal.configuration.pojos.KpiViolationConfig;
import com.heal.controlcenter.dao.mysql.CompInstanceDao;
import com.heal.controlcenter.dao.mysql.ThresholdDao;
import com.heal.controlcenter.dao.mysql.entity.InstanceKpiAttributeThresholdBean;
import com.heal.controlcenter.dao.opensearch.InstanceKpiThresholdRepo;
import com.heal.controlcenter.dao.redis.InstanceRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.InstanceKpiThresholdDetails;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class UpdateInstanceLevelKpiThresholdBLTest {

    @InjectMocks
    private UpdateInstanceLevelKpiThresholdBL updateInstanceLevelKpiThresholdBL;

    @Mock
    private ClientValidationUtils clientValidationUtils;

    @Mock
    private InstanceLevelKpiThresholdUtil instanceLevelKpiThresholdUtil;

    @Mock
    private ServerValidationUtils serverValidationUtils;

    @Mock
    private ThresholdDao thresholdDao;

    @Mock
    private InstanceKpiThresholdRepo instanceKpiThresholdRepo;

    @Mock
    private InstanceRepo instanceRepo;

    @Mock
    private CompInstanceDao compInstanceDao;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testClientValidation_ValidInputs() throws ClientException, HealControlCenterException {
        InstanceKpiThresholdDetails details = new InstanceKpiThresholdDetails();
        String[] params = {"auth", "acc"};
        doNothing().when(clientValidationUtils).nullRequestBodyCheck(details, UIMessages.REQUEST_BODY_NULL);
        doNothing().when(clientValidationUtils).nullOrEmptyCheck("auth", UIMessages.AUTH_KEY_INVALID);
        doNothing().when(clientValidationUtils).nullOrEmptyCheck("acc", UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        doNothing().when(instanceLevelKpiThresholdUtil).validateInstanceKpiThresholdDetails(details, false);

        UtilityBean<InstanceKpiThresholdDetails> result = updateInstanceLevelKpiThresholdBL.clientValidation(details, params);

        assertNotNull(result);
        assertEquals(details, result.getPojoObject());
        assertEquals("auth", result.getRequestParams().get(Constants.AUTH_KEY));
        assertEquals("acc", result.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
    }

    @Test
    void testClientValidation_NullRequestBody() throws ClientException {
        String[] params = {"auth", "acc"};
        doThrow(new ClientException(UIMessages.REQUEST_BODY_NULL))
                .when(clientValidationUtils).nullRequestBodyCheck(null, UIMessages.REQUEST_BODY_NULL);

        ClientException ex = assertThrows(ClientException.class,
                () -> updateInstanceLevelKpiThresholdBL.clientValidation(null, params));
        assertEquals("ClientException : " + UIMessages.REQUEST_BODY_NULL, ex.getMessage());
    }

    @Test
    void testClientValidation_NullOrEmptyAuthKey() throws ClientException {
        InstanceKpiThresholdDetails details = new InstanceKpiThresholdDetails();
        String[] params = {null, "acc"};
        doNothing().when(clientValidationUtils).nullRequestBodyCheck(details, UIMessages.REQUEST_BODY_NULL);
        doThrow(new ClientException(UIMessages.AUTH_KEY_INVALID))
                .when(clientValidationUtils).nullOrEmptyCheck(null, UIMessages.AUTH_KEY_INVALID);

        ClientException ex = assertThrows(ClientException.class,
                () -> updateInstanceLevelKpiThresholdBL.clientValidation(details, params));
        assertEquals("ClientException : " + UIMessages.AUTH_KEY_INVALID, ex.getMessage());
    }

    @Test
    void testClientValidation_NullOrEmptyAccountIdentifier() throws ClientException {
        InstanceKpiThresholdDetails details = new InstanceKpiThresholdDetails();
        String[] params = {"auth", ""};
        doNothing().when(clientValidationUtils).nullRequestBodyCheck(details, UIMessages.REQUEST_BODY_NULL);
        doNothing().when(clientValidationUtils).nullOrEmptyCheck("auth", UIMessages.AUTH_KEY_INVALID);
        doThrow(new ClientException(UIMessages.ACCOUNT_IDENTIFIER_INVALID))
                .when(clientValidationUtils).nullOrEmptyCheck("", UIMessages.ACCOUNT_IDENTIFIER_INVALID);

        ClientException ex = assertThrows(ClientException.class,
                () -> updateInstanceLevelKpiThresholdBL.clientValidation(details, params));
        assertEquals("ClientException : " + UIMessages.ACCOUNT_IDENTIFIER_INVALID, ex.getMessage());
    }

    @Test
    void testClientValidation_ValidationUtilThrows() throws ClientException, HealControlCenterException {
        InstanceKpiThresholdDetails details = new InstanceKpiThresholdDetails();
        String[] params = {"auth", "acc"};
        doNothing().when(clientValidationUtils).nullRequestBodyCheck(details, UIMessages.REQUEST_BODY_NULL);
        doNothing().when(clientValidationUtils).nullOrEmptyCheck("auth", UIMessages.AUTH_KEY_INVALID);
        doNothing().when(clientValidationUtils).nullOrEmptyCheck("acc", UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        doThrow(new HealControlCenterException("validation failed"))
                .when(instanceLevelKpiThresholdUtil).validateInstanceKpiThresholdDetails(details, false);

        ClientException ex = assertThrows(ClientException.class,
                () -> updateInstanceLevelKpiThresholdBL.clientValidation(details, params));
        assertEquals("ClientException : validation failed", ex.getMessage());
    }

    // server validation test cases
    @Test
    void testServerValidation_HappyPath() throws Exception {
        InstanceKpiThresholdDetails details = new InstanceKpiThresholdDetails();
        Map<Integer, String> compInstIdToIdentifierMap = Map.of(1, "i1");
        Map<String, Object> metadata = new HashMap<>();
        Account account = Account.builder().id(100).identifier("acc").build();
        metadata.put(Constants.ACCOUNT, account);
        UtilityBean<InstanceKpiThresholdDetails> utilityBean = UtilityBean.<InstanceKpiThresholdDetails>builder()
                .pojoObject(details).metadata(metadata)
                .requestParams(Map.of(Constants.AUTH_KEY, "user1", Constants.ACCOUNT_IDENTIFIER, "acc"))
                .build();

        when(serverValidationUtils.authKeyValidation("user1")).thenReturn("user1");
        when(serverValidationUtils.accountValidation("acc")).thenReturn(account);
        when(instanceLevelKpiThresholdUtil.verifyInstanceIdKpiAndGroupKpi(details, 100)).thenReturn(compInstIdToIdentifierMap);
        when(instanceLevelKpiThresholdUtil.kpiAndCompInstanceValidation(details, compInstIdToIdentifierMap, "user1", 100, "acc", false))
                .thenReturn(List.of(new InstanceKpiAttributeThresholdBean()));

        UtilityBean<List<InstanceKpiAttributeThresholdBean>> result = updateInstanceLevelKpiThresholdBL.serverValidation(utilityBean);

        assertNotNull(result);
        assertNotNull(result.getPojoObject());
        assertFalse(result.getPojoObject().isEmpty());
    }

    @Test
    void testServerValidation_AuthKeyValidationThrows() throws Exception {
        InstanceKpiThresholdDetails details = new InstanceKpiThresholdDetails();
        UtilityBean<InstanceKpiThresholdDetails> utilityBean = UtilityBean.<InstanceKpiThresholdDetails>builder()
                .pojoObject(details)
                .requestParams(Map.of(Constants.AUTH_KEY, "user1", Constants.ACCOUNT_IDENTIFIER, "acc"))
                .build();

        when(serverValidationUtils.authKeyValidation("user1")).thenThrow(new ServerException("auth error"));

        ServerException ex = assertThrows(ServerException.class, () ->
                updateInstanceLevelKpiThresholdBL.serverValidation(utilityBean));
        assertTrue(ex.getMessage().contains("auth error"));
    }

    @Test
    void testServerValidation_AccountValidationThrows() throws Exception {
        InstanceKpiThresholdDetails details = new InstanceKpiThresholdDetails();
        UtilityBean<InstanceKpiThresholdDetails> utilityBean = UtilityBean.<InstanceKpiThresholdDetails>builder()
                .pojoObject(details)
                .requestParams(Map.of(Constants.AUTH_KEY, "user1", Constants.ACCOUNT_IDENTIFIER, "acc"))
                .build();

        when(serverValidationUtils.authKeyValidation("user1")).thenReturn("user1");
        when(serverValidationUtils.accountValidation("acc")).thenThrow(new ServerException("account error"));

        ServerException ex = assertThrows(ServerException.class, () ->
                updateInstanceLevelKpiThresholdBL.serverValidation(utilityBean));
        assertTrue(ex.getMessage().contains("account error"));
    }

    @Test
    void testServerValidation_VerifyInstanceIdKpiAndGroupKpiThrows() throws Exception {
        InstanceKpiThresholdDetails details = new InstanceKpiThresholdDetails();
        Map<String, Object> metadata = new HashMap<>();
        Account account = Account.builder().id(100).identifier("acc").build();
        metadata.put(Constants.ACCOUNT, account);
        UtilityBean<InstanceKpiThresholdDetails> utilityBean = UtilityBean.<InstanceKpiThresholdDetails>builder()
                .pojoObject(details).metadata(metadata)
                .requestParams(Map.of(Constants.AUTH_KEY, "user1", Constants.ACCOUNT_IDENTIFIER, "acc"))
                .build();

        when(serverValidationUtils.authKeyValidation("user1")).thenReturn("user1");
        when(serverValidationUtils.accountValidation("acc")).thenReturn(account);
        when(instanceLevelKpiThresholdUtil.verifyInstanceIdKpiAndGroupKpi(details, 100))
                .thenThrow(new HealControlCenterException("verify error"));

        ServerException ex = assertThrows(ServerException.class, () ->
                updateInstanceLevelKpiThresholdBL.serverValidation(utilityBean));
        assertTrue(ex.getMessage().contains("verify error"));
    }

    @Test
    void testServerValidation_KpiAndCompInstanceValidationThrows() throws Exception {
        InstanceKpiThresholdDetails details = new InstanceKpiThresholdDetails();
        Map<String, Object> metadata = new HashMap<>();
        Account account = Account.builder().id(100).identifier("acc").build();
        metadata.put(Constants.ACCOUNT, account);
        Map<Integer, String> compInstIdToIdentifierMap = Map.of(1, "i1");
        UtilityBean<InstanceKpiThresholdDetails> utilityBean = UtilityBean.<InstanceKpiThresholdDetails>builder()
                .pojoObject(details).metadata(metadata)
                .requestParams(Map.of(Constants.AUTH_KEY, "user1", Constants.ACCOUNT_IDENTIFIER, "acc"))
                .build();

        when(serverValidationUtils.authKeyValidation("user1")).thenReturn("user1");
        when(serverValidationUtils.accountValidation("acc")).thenReturn(account);
        when(instanceLevelKpiThresholdUtil.verifyInstanceIdKpiAndGroupKpi(details, 100)).thenReturn(compInstIdToIdentifierMap);
        when(instanceLevelKpiThresholdUtil.kpiAndCompInstanceValidation(details, compInstIdToIdentifierMap, "user1", 100, "acc", false))
                .thenThrow(new HealControlCenterException("validation error"));

        ServerException ex = assertThrows(ServerException.class, () ->
                updateInstanceLevelKpiThresholdBL.serverValidation(utilityBean));
        assertTrue(ex.getMessage().contains("validation error"));
    }

    // process method unit test cases
    @Test
    void testProcess_Success() throws Exception {
        InstanceKpiAttributeThresholdBean addBean = mock(InstanceKpiAttributeThresholdBean.class);
        InstanceKpiAttributeThresholdBean updateBean = mock(InstanceKpiAttributeThresholdBean.class);
        InstanceKpiAttributeThresholdBean deleteBean = mock(InstanceKpiAttributeThresholdBean.class);

        when(addBean.getActionForUpdate()).thenReturn(com.heal.controlcenter.pojo.ActionsEnum.ADD);
        when(updateBean.getActionForUpdate()).thenReturn(com.heal.controlcenter.pojo.ActionsEnum.MODIFY);
        when(deleteBean.getActionForUpdate()).thenReturn(com.heal.controlcenter.pojo.ActionsEnum.DELETE);

        Map<String, Object> metadata = new HashMap<>();
        Account account = Account.builder().id(100).identifier("acc").build();
        metadata.put(Constants.ACCOUNT, account);
        List<InstanceKpiAttributeThresholdBean> beans = Arrays.asList(addBean, updateBean, deleteBean);
        UtilityBean<List<InstanceKpiAttributeThresholdBean>> utilityBean = UtilityBean.<List<InstanceKpiAttributeThresholdBean>>builder()
                .pojoObject(beans).metadata(metadata).build();

        // Mock add
        when(thresholdDao.addInstanceKpiAttributeLevelThresholds(anyList())).thenReturn(new int[]{1});
        doNothing().when(instanceKpiThresholdRepo).addInstanceKpiThresholdInBulk(anyList());

        // Mock update
        when(updateBean.getCompInstanceId()).thenReturn(1);
        when(thresholdDao.fetchCompInstanceKpiAttrThresholds(anyList())).thenReturn(Collections.emptyList());
        when(thresholdDao.updateInstanceKpiAttributeLevelThresholds(anyList())).thenReturn(new int[]{1});
        doNothing().when(instanceKpiThresholdRepo).updateInstanceKpiThresholdsInBulk(anyString(), anyList());

        // Mock delete
        when(thresholdDao.deleteInstanceKpiAttributeLevelThresholds(anyList())).thenReturn(new int[]{1});
        doNothing().when(instanceKpiThresholdRepo).closeExistingThresholds(anyList());

        // Mock Redis
        doNothing().when(instanceLevelKpiThresholdUtil).addInstanceKpiAttributeLevelThresholdsInRedis(anyList());
        doNothing().when(instanceLevelKpiThresholdUtil).deleteThresholdsForInstanceKpiInRedis(anyList());
        doNothing().when(instanceRepo).updateKpiDetailsForKpiId(anyString(), anyString(), any());
        doNothing().when(instanceRepo).updateKpiDetailsForKpiIdentifier(anyString(), anyString(), any());
        doNothing().when(instanceRepo).updateKpiDetails(anyString(), anyString(), anyList());
        doNothing().when(compInstanceDao).updateNonGroupInstanceKpiAnomaly(any());
        doNothing().when(compInstanceDao).updateGroupInstanceKpiAnomaly(any());
        when(instanceRepo.getInstanceWiseKpis(anyString(), anyString())).thenReturn(Collections.emptyList());
        doNothing().when(instanceKpiThresholdRepo).updateInstanceKpiThresholdsInBulk(anyString(), any());

        String result = updateInstanceLevelKpiThresholdBL.process(utilityBean);

        assertEquals("Instance Level Thresholds for attributes updated successfully", result);

        // Verifications
        verify(thresholdDao).addInstanceKpiAttributeLevelThresholds(anyList());
        verify(instanceKpiThresholdRepo).addInstanceKpiThresholdInBulk(anyList());
        verify(thresholdDao).updateInstanceKpiAttributeLevelThresholds(anyList());
        verify(instanceKpiThresholdRepo, atLeastOnce()).updateInstanceKpiThresholdsInBulk(anyString(), anyList());
        verify(thresholdDao).deleteInstanceKpiAttributeLevelThresholds(anyList());
        verify(instanceKpiThresholdRepo).closeExistingThresholds(anyList());
        verify(instanceLevelKpiThresholdUtil).addInstanceKpiAttributeLevelThresholdsInRedis(anyList());
        verify(instanceLevelKpiThresholdUtil).deleteThresholdsForInstanceKpiInRedis(anyList());
        verify(compInstanceDao).updateNonGroupInstanceKpiAnomaly(any());
        verify(compInstanceDao).updateGroupInstanceKpiAnomaly(any());
    }

    @Test
    void testProcess_AddThresholds_PerconaFails() {
        Map<String, Object> metadata = new HashMap<>();
        Account account = Account.builder().id(100).identifier("acc").build();
        metadata.put(Constants.ACCOUNT, account);
        InstanceKpiAttributeThresholdBean addBean = mock(InstanceKpiAttributeThresholdBean.class);
        when(addBean.getActionForUpdate()).thenReturn(com.heal.controlcenter.pojo.ActionsEnum.ADD);
        List<InstanceKpiAttributeThresholdBean> beans = List.of(addBean);
        UtilityBean<List<InstanceKpiAttributeThresholdBean>> utilityBean = UtilityBean.<List<InstanceKpiAttributeThresholdBean>>builder()
                .pojoObject(beans).metadata(metadata).build();

        when(thresholdDao.addInstanceKpiAttributeLevelThresholds(anyList())).thenReturn(new int[0]);

        DataProcessingException ex = assertThrows(DataProcessingException.class,
                () -> updateInstanceLevelKpiThresholdBL.process(utilityBean));
        assertTrue(ex.getMessage().contains("Error while adding attribute level thresholds"));
    }

    @Test
    void testProcess_AddThresholds_OpenSearchFails() throws Exception {
        Map<String, Object> metadata = new HashMap<>();
        Account account = Account.builder().id(100).identifier("acc").build();
        metadata.put(Constants.ACCOUNT, account);
        InstanceKpiAttributeThresholdBean addBean = mock(InstanceKpiAttributeThresholdBean.class);
        when(addBean.getActionForUpdate()).thenReturn(com.heal.controlcenter.pojo.ActionsEnum.ADD);
        List<InstanceKpiAttributeThresholdBean> beans = List.of(addBean);
        UtilityBean<List<InstanceKpiAttributeThresholdBean>> utilityBean = UtilityBean.<List<InstanceKpiAttributeThresholdBean>>builder()
                .pojoObject(beans).metadata(metadata).build();

        when(thresholdDao.addInstanceKpiAttributeLevelThresholds(anyList())).thenReturn(new int[]{1});
        doThrow(new HealControlCenterException("OpenSearch error")).when(instanceKpiThresholdRepo).addInstanceKpiThresholdInBulk(anyList());

        DataProcessingException ex = assertThrows(DataProcessingException.class,
                () -> updateInstanceLevelKpiThresholdBL.process(utilityBean));
        assertTrue(ex.getMessage().contains("Error while adding the thresholds to OpenSearch"));
    }

    @Test
    void testProcess_DeleteThresholds_PerconaFails() {
        Map<String, Object> metadata = new HashMap<>();
        Account account = Account.builder().id(100).identifier("acc").build();
        metadata.put(Constants.ACCOUNT, account);
        InstanceKpiAttributeThresholdBean deleteBean = mock(InstanceKpiAttributeThresholdBean.class);
        when(deleteBean.getActionForUpdate()).thenReturn(com.heal.controlcenter.pojo.ActionsEnum.DELETE);
        List<InstanceKpiAttributeThresholdBean> beans = List.of(deleteBean);
        UtilityBean<List<InstanceKpiAttributeThresholdBean>> utilityBean = UtilityBean.<List<InstanceKpiAttributeThresholdBean>>builder()
                .pojoObject(beans).metadata(metadata).build();

        when(thresholdDao.deleteInstanceKpiAttributeLevelThresholds(anyList())).thenReturn(new int[0]);

        DataProcessingException ex = assertThrows(DataProcessingException.class,
                () -> updateInstanceLevelKpiThresholdBL.process(utilityBean));
        assertTrue(ex.getMessage().contains("Error while deleting attribute level thresholds"));
    }

    @Test
    void testProcess_DeleteThresholds_OpenSearchFails() throws Exception {
        Map<String, Object> metadata = new HashMap<>();
        Account account = Account.builder().id(100).identifier("acc").build();
        metadata.put(Constants.ACCOUNT, account);
        InstanceKpiAttributeThresholdBean deleteBean = mock(InstanceKpiAttributeThresholdBean.class);
        when(deleteBean.getActionForUpdate()).thenReturn(com.heal.controlcenter.pojo.ActionsEnum.DELETE);
        List<InstanceKpiAttributeThresholdBean> beans = List.of(deleteBean);
        UtilityBean<List<InstanceKpiAttributeThresholdBean>> utilityBean = UtilityBean.<List<InstanceKpiAttributeThresholdBean>>builder()
                .pojoObject(beans).metadata(metadata).build();

        when(thresholdDao.deleteInstanceKpiAttributeLevelThresholds(anyList())).thenReturn(new int[]{1});
        doThrow(new HealControlCenterException("OpenSearch error")).when(instanceKpiThresholdRepo).closeExistingThresholds(anyList());

        DataProcessingException ex = assertThrows(DataProcessingException.class,
                () -> updateInstanceLevelKpiThresholdBL.process(utilityBean));
        assertTrue(ex.getMessage().contains("Error while updating endTime for the thresholds to OpenSearch"));
    }

    @Test
    void testUpdateThresholdsForInstanceKpiInRedis_CoversAllBranches() {
        // Prepare threshold bean
        InstanceKpiAttributeThresholdBean thresholdBean = mock(InstanceKpiAttributeThresholdBean.class);
        when(thresholdBean.getCompInstanceId()).thenReturn(1);
        when(thresholdBean.getKpiId()).thenReturn(2);
        when(thresholdBean.getKpiGroupId()).thenReturn(0);
        when(thresholdBean.getStatus()).thenReturn(1);
        when(thresholdBean.getAccountIdentifier()).thenReturn("acc");
        when(thresholdBean.getCompInstanceIdentifier()).thenReturn("ci1");
        when(thresholdBean.getAttributeValue()).thenReturn("attr");

        // Prepare kpi entity with empty violation config
        CompInstKpiEntity kpiEntity = mock(CompInstKpiEntity.class);
        when(kpiEntity.getId()).thenReturn(2);
        when(kpiEntity.getGroupId()).thenReturn(0);
        when(kpiEntity.getKpiViolationConfig()).thenReturn(new HashMap<>());

        // Prepare instanceWiseKpis
        List<CompInstKpiEntity> instanceWiseKpis = List.of(kpiEntity);
        when(instanceRepo.getInstanceWiseKpis("acc", "ci1")).thenReturn(instanceWiseKpis);

        // Mock buildKpiViolationConfig
        KpiViolationConfig violationConfig = mock(KpiViolationConfig.class);
        when(instanceLevelKpiThresholdUtil.buildKpiViolationConfig(any(), any())).thenReturn(violationConfig);

        // Call method
        updateInstanceLevelKpiThresholdBL.updateThresholdsForInstanceKpiInRedis(List.of(thresholdBean), "acc", 1);

        // Verify updates
        verify(instanceRepo).updateKpiDetailsForKpiId(eq("acc"), eq("ci1"), eq(kpiEntity));
        verify(instanceRepo).updateKpiDetailsForKpiIdentifier(eq("acc"), eq("ci1"), eq(kpiEntity));
        verify(instanceRepo).updateKpiDetails(eq("acc"), eq("ci1"), eq(instanceWiseKpis));
        verify(kpiEntity).setKpiViolationConfig(anyMap());
    }

    @Test
    void testProcess_UpdateThresholds_PerconaFails() {
        // Prepare a MODIFY bean to trigger update logic
        Map<String, Object> metadata = new HashMap<>();
        Account account = Account.builder().id(100).identifier("acc").build();
        metadata.put(Constants.ACCOUNT, account);
        InstanceKpiAttributeThresholdBean updateBean = mock(InstanceKpiAttributeThresholdBean.class);
        when(updateBean.getActionForUpdate()).thenReturn(com.heal.controlcenter.pojo.ActionsEnum.MODIFY);
        when(updateBean.getCompInstanceId()).thenReturn(1);
        when(updateBean.getKpiId()).thenReturn(2);

        List<InstanceKpiAttributeThresholdBean> beans = List.of(updateBean);
        UtilityBean<List<InstanceKpiAttributeThresholdBean>> utilityBean = UtilityBean.<List<InstanceKpiAttributeThresholdBean>>builder()
                .pojoObject(beans).metadata(metadata).build();

        // Mock fetch to return a map with the kpiId
        InstanceKpiAttributeThresholdBean existingBean = mock(InstanceKpiAttributeThresholdBean.class);
        when(existingBean.getKpiId()).thenReturn(2);
        when(thresholdDao.fetchCompInstanceKpiAttrThresholds(anyList())).thenReturn(List.of(existingBean));

        // Mock update to return null (or new int[0]) to trigger the exception
        when(thresholdDao.updateInstanceKpiAttributeLevelThresholds(anyList())).thenReturn(null);

        DataProcessingException ex = assertThrows(DataProcessingException.class,
                () -> updateInstanceLevelKpiThresholdBL.process(utilityBean));
        assertTrue(ex.getMessage().contains("Error while updating attribute level thresholds"));
    }
}