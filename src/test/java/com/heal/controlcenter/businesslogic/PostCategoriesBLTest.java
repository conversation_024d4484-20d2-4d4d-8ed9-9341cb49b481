package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.beans.CategoryDetailBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.CategoryDao;
import com.heal.controlcenter.dao.redis.CategoryRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.CategoryDetails;
import com.heal.controlcenter.pojo.IdPojo;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.DateTimeUtil;
import com.heal.controlcenter.util.ServerValidationUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;

@ExtendWith(MockitoExtension.class)
public class PostCategoriesBLTest {
    @Mock
    CategoryDao categoryDao;
    @Mock
    ClientValidationUtils clientValidationUtils;
    @Mock
    ServerValidationUtils serverValidationUtils;
    @Mock
    CategoryRepo categoryRepo;

    @InjectMocks
    PostCategoriesBL postCategoriesBL;

    private CategoryDetails validCategory;
    private String accountIdentifier = "acc-1";
    private MockedStatic<DateTimeUtil> dateTimeUtilMockedStatic;

    @BeforeEach
    void setup() {
        validCategory = CategoryDetails.builder()
                .name("TestCategory")
                .identifier("test-category")
                .description("desc")
                .type("CUSTOM")
                .subType("Workload")
                .build();

        dateTimeUtilMockedStatic = Mockito.mockStatic(DateTimeUtil.class);
        Timestamp mockTimestamp = Timestamp.valueOf("2025-07-23 10:00:00");
        dateTimeUtilMockedStatic.when(DateTimeUtil::getCurrentTimestampInGMT).thenReturn(mockTimestamp);
    }

    @AfterEach
    void tearDown() {
        dateTimeUtilMockedStatic.close();
    }

    /**
     * Tests client-side validation for adding categories.
     * Ensures valid input passes and invalid input throws ClientException.
     */
    @Test
    void testClientValidation_success() throws ClientException {
        List<CategoryDetails> categories = Collections.singletonList(validCategory);
        doNothing().when(clientValidationUtils).accountIdentifierValidation(accountIdentifier);
        UtilityBean<List<CategoryDetails>> result = postCategoriesBL.clientValidation(categories, accountIdentifier);
        assertNotNull(result);
        assertEquals(categories, result.getPojoObject());
        assertEquals(accountIdentifier, result.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
    }

    /**
     * Tests client-side validation for empty category list.
     * Should throw ClientException for empty request body.
     */
    @Test
    void testClientValidation_emptyList_throwsException() {
        Exception ex = assertThrows(ClientException.class, () ->
                postCategoriesBL.clientValidation(Collections.emptyList(), accountIdentifier));
        assertTrue(ex.getMessage().contains("Request body cannot be empty"));
    }

    /**
     * Tests client-side validation for null category list.
     * Should throw ClientException for null request body.
     */
    @Test
    void testClientValidation_nullCategories_throwsException() {
        Exception ex = assertThrows(ClientException.class, () ->
                postCategoriesBL.clientValidation(null, accountIdentifier));
        assertTrue(ex.getMessage().contains("Request body cannot be empty"));
    }

    /**
     * Tests client-side validation for invalid account identifier.
     * Should throw ClientException for invalid account.
     */
    @Test
    void testClientValidation_invalidAccountIdentifier_throwsException() throws ClientException {
        List<CategoryDetails> categories = Collections.singletonList(validCategory);
        doThrow(new ClientException("Invalid account identifier")).when(clientValidationUtils).accountIdentifierValidation(anyString());
        Exception ex = assertThrows(ClientException.class, () ->
                postCategoriesBL.clientValidation(categories, ""));
        assertTrue(ex.getMessage().contains("Invalid account identifier"));
    }

    /**
     * Tests client-side validation for invalid category fields.
     * Should throw ClientException for invalid category name.
     */
    @Test
    void testClientValidation_categoryValidationError() throws ClientException {
        CategoryDetails invalidCategory = CategoryDetails.builder().name("").identifier("").description("").type("").subType("").build();
        List<CategoryDetails> categories = Collections.singletonList(invalidCategory);
        doNothing().when(clientValidationUtils).accountIdentifierValidation(accountIdentifier);
        Exception ex = assertThrows(ClientException.class, () ->
                postCategoriesBL.clientValidation(categories, accountIdentifier));
        assertTrue(ex.getMessage().contains("categoryName"));
    }

    /**
     * Tests server-side validation for adding categories.
     * Ensures valid input passes and beans are prepared.
     */
    @Test
    void testServerValidation_success() throws Exception {
        List<CategoryDetails> categories = Collections.singletonList(validCategory);
        Map<String, String> reqParams = new HashMap<>();
        reqParams.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID_KEY, "user-1");
        UtilityBean<List<CategoryDetails>> input = UtilityBean.<List<CategoryDetails>>builder()
                .pojoObject(categories)
                .requestParams(reqParams)
                .metadata(metadata)
                .build();
        Account mockAccount = new Account();
        mockAccount.setId(10);
        Mockito.when(serverValidationUtils.accountValidation(accountIdentifier)).thenReturn(mockAccount);
        Mockito.when(categoryDao.existsByIdentifier(anyInt(), anyString())).thenReturn(false);
        Mockito.when(categoryDao.existsByName(anyInt(), anyString())).thenReturn(false);
        UtilityBean<List<CategoryDetailBean>> result = postCategoriesBL.serverValidation(input);
        assertNotNull(result);
        assertEquals(1, result.getPojoObject().size());
        assertEquals("TestCategory", result.getPojoObject().get(0).getName());
    }

    /**
     * Tests server-side validation for account validation failure.
     * Should throw ServerException if account is not found.
     */
    @Test
    void testServerValidation_accountValidationFails() throws Exception {
        List<CategoryDetails> categories = Collections.singletonList(validCategory);
        Map<String, String> reqParams = new HashMap<>();
        reqParams.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID_KEY, "user-1");
        UtilityBean<List<CategoryDetails>> input = UtilityBean.<List<CategoryDetails>>builder()
                .pojoObject(categories)
                .requestParams(reqParams)
                .metadata(metadata)
                .build();
        Mockito.when(serverValidationUtils.accountValidation(accountIdentifier)).thenThrow(new ServerException("Account not found"));
        assertThrows(ServerException.class, () -> postCategoriesBL.serverValidation(input));
    }

    /**
     * Tests server-side validation for duplicate category identifier.
     * Should throw ServerException if identifier already exists.
     */
    @Test
    void testServerValidation_duplicateIdentifier() throws Exception {
        List<CategoryDetails> categories = Collections.singletonList(validCategory);
        Map<String, String> reqParams = new HashMap<>();
        reqParams.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID_KEY, "user-1");
        UtilityBean<List<CategoryDetails>> input = UtilityBean.<List<CategoryDetails>>builder()
                .pojoObject(categories)
                .requestParams(reqParams)
                .metadata(metadata)
                .build();
        Account mockAccount = new Account();
        mockAccount.setId(10);
        Mockito.when(serverValidationUtils.accountValidation(accountIdentifier)).thenReturn(mockAccount);
        Mockito.when(categoryDao.existsByIdentifier(anyInt(), anyString())).thenReturn(true);
        assertThrows(ServerException.class, () -> postCategoriesBL.serverValidation(input));
    }

    /**
     * Tests server-side validation for duplicate category name.
     * Should throw ServerException if name already exists.
     */
    @Test
    void testServerValidation_duplicateName() throws Exception {
        List<CategoryDetails> categories = Collections.singletonList(validCategory);
        Map<String, String> reqParams = new HashMap<>();
        reqParams.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID_KEY, "user-1");
        UtilityBean<List<CategoryDetails>> input = UtilityBean.<List<CategoryDetails>>builder()
                .pojoObject(categories)
                .requestParams(reqParams)
                .metadata(metadata)
                .build();
        Account mockAccount = new Account();
        mockAccount.setId(10);
        Mockito.when(serverValidationUtils.accountValidation(accountIdentifier)).thenReturn(mockAccount);
        Mockito.when(categoryDao.existsByIdentifier(anyInt(), anyString())).thenReturn(false);
        Mockito.when(categoryDao.existsByName(anyInt(), anyString())).thenReturn(true);
        assertThrows(ServerException.class, () -> postCategoriesBL.serverValidation(input));
    }

    /**
     * Tests process logic for successful category insertion and Redis update.
     * Ensures categories are inserted and returned.
     */
    @Test
    void testProcess_success() throws Exception {
        CategoryDetailBean bean = CategoryDetailBean.builder()
                .name("TestCategory")
                .identifier("test-category")
                .accountId(10)
                .description("desc")
                .userDetailsId("user-1")
                .createdTime("2025-07-23 10:00:00")
                .updatedTime("2025-07-23 10:00:00")
                .status(1)
                .isCustom(1)
                .isInformative(0)
                .isWorkLoad(1)
                .accountIdentifier(accountIdentifier)
                .build();
        UtilityBean<List<CategoryDetailBean>> input = UtilityBean.<List<CategoryDetailBean>>builder()
                .pojoObject(List.of(bean))
                .metadata(Map.of(Constants.USER_ID_KEY, "user-1"))
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .build();
        Mockito.when(categoryDao.insertCategory(any())).thenReturn(123);
        Mockito.when(categoryRepo.getCategoryDetails(anyString())).thenReturn(new ArrayList<>());
        doNothing().when(categoryRepo).updateCategoryDetails(anyString(), any());
        doNothing().when(categoryRepo).updateCategory(anyString(), any());
        List<IdPojo> result = postCategoriesBL.process(input);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("TestCategory", result.get(0).getName());
    }

    /**
     * Tests process logic for category insertion failure.
     * Should throw DataProcessingException if DB insert fails.
     */
    @Test
    void testProcess_insertCategoryException() throws Exception {
        CategoryDetailBean bean = CategoryDetailBean.builder()
                .name("TestCategory")
                .identifier("test-category")
                .accountId(10)
                .description("desc")
                .userDetailsId("user-1")
                .createdTime("2025-07-23 10:00:00")
                .updatedTime("2025-07-23 10:00:00")
                .status(1)
                .isCustom(1)
                .isInformative(0)
                .isWorkLoad(1)
                .accountIdentifier(accountIdentifier)
                .build();
        UtilityBean<List<CategoryDetailBean>> input = UtilityBean.<List<CategoryDetailBean>>builder()
                .pojoObject(List.of(bean))
                .metadata(Map.of(Constants.USER_ID_KEY, "user-1"))
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .build();
        Mockito.when(categoryDao.insertCategory(any())).thenThrow(new RuntimeException("Insert failed"));
        assertThrows(DataProcessingException.class, () -> postCategoriesBL.process(input));
    }

    /**
     * Tests process logic for Redis update failure.
     * Should throw DataProcessingException if Redis update fails.
     */
    @Test
    void testProcess_redisUpdateException() throws Exception {
        CategoryDetailBean bean = CategoryDetailBean.builder()
                .name("TestCategory")
                .identifier("test-category")
                .accountId(10)
                .description("desc")
                .userDetailsId("user-1")
                .createdTime("2025-07-23 10:00:00")
                .updatedTime("2025-07-23 10:00:00")
                .status(1)
                .isCustom(1)
                .isInformative(0)
                .isWorkLoad(1)
                .accountIdentifier(accountIdentifier)
                .build();
        UtilityBean<List<CategoryDetailBean>> input = UtilityBean.<List<CategoryDetailBean>>builder()
                .pojoObject(List.of(bean))
                .metadata(Map.of(Constants.USER_ID_KEY, "user-1"))
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .build();
        Mockito.when(categoryDao.insertCategory(any())).thenReturn(123);
        Mockito.when(categoryRepo.getCategoryDetails(anyString())).thenReturn(new ArrayList<>());
        doThrow(new RuntimeException("Redis update failed")).when(categoryRepo).updateCategoryDetails(anyString(), any());
        assertThrows(DataProcessingException.class, () -> postCategoriesBL.process(input));
    }

    /**
     * Tests addCategoryInRedis for exception when fetching categories from Redis.
     * Should not throw, just log and continue with empty list.
     */
    @Test
    void testAddCategoryInRedis_fetchCategoriesThrowsException() throws Exception {
        CategoryDetailBean bean = CategoryDetailBean.builder()
                .name("TestCategory")
                .identifier("test-category")
                .accountId(10)
                .description("desc")
                .userDetailsId("user-1")
                .createdTime("2025-07-23 10:00:00")
                .updatedTime("2025-07-23 10:00:00")
                .status(1)
                .isCustom(1)
                .isInformative(0)
                .isWorkLoad(1)
                .accountIdentifier(accountIdentifier)
                .build();
        // Simulate exception when fetching categories from Redis
        Mockito.when(categoryRepo.getCategoryDetails(anyString())).thenThrow(new RuntimeException("Redis fetch failed"));
        doNothing().when(categoryRepo).updateCategoryDetails(anyString(), any());
        doNothing().when(categoryRepo).updateCategory(anyString(), any());
        assertDoesNotThrow(() -> postCategoriesBL.addCategoryInRedis(bean, 123));
    }
}
