package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.CategoryDao;
import com.heal.controlcenter.dao.redis.CategoryRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.DeleteCategoriesPojo;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.ServerValidationUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class DeleteCategoriesBLTest {
    @InjectMocks
    DeleteCategoriesBL deleteCategoriesBL;
    @Mock
    CategoryDao categoryDao;
    @Mock
    ClientValidationUtils clientValidationUtils;
    @Mock
    ServerValidationUtils serverValidationUtils;
    @Mock
    CategoryRepo categoryRepo;

    /**
     * Tests client validation with valid input, expecting successful validation.
     */
    @Test
    void testClientValidationSuccess() throws Exception {
        DeleteCategoriesPojo pojo = DeleteCategoriesPojo.builder().hardDelete(true).categoryIdentifiers(List.of("cat-1")).build();
        String accountIdentifier = "acc-1";
        doNothing().when(clientValidationUtils).accountIdentifierValidation(accountIdentifier);
        UtilityBean<DeleteCategoriesPojo> result = deleteCategoriesBL.clientValidation(pojo, accountIdentifier);
        assertNotNull(result);
        assertEquals(pojo, result.getPojoObject());
        assertEquals(accountIdentifier, result.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
        assertEquals(true, result.getMetadata().get(Constants.HARD_DELETE));
    }

    /**
     * Tests client validation with empty category identifiers, expecting ClientException.
     */
    @Test
    void testClientValidationEmptyIdentifiersThrows() {
        DeleteCategoriesPojo pojo = DeleteCategoriesPojo.builder().hardDelete(true).categoryIdentifiers(Collections.emptyList()).build();
        String accountIdentifier = "acc-1";
        assertThrows(ClientException.class, () -> deleteCategoriesBL.clientValidation(pojo, accountIdentifier));
    }

    /**
     * Tests server validation with valid category and hard delete flag, expecting successful validation.
     */
    @Test
    void testServerValidationWithValidCategoryAndHardDelete() throws Exception {
        String accountIdentifier = "acc-1";
        DeleteCategoriesPojo pojo = DeleteCategoriesPojo.builder().hardDelete(true).categoryIdentifiers(List.of("cat-1")).build();
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.HARD_DELETE, true);
        UtilityBean<DeleteCategoriesPojo> inputBean = UtilityBean.<DeleteCategoriesPojo>builder()
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .pojoObject(pojo)
                .metadata(metadata)
                .build();
        Account mockAccount = new Account();
        mockAccount.setId(1);
        when(serverValidationUtils.accountValidation(accountIdentifier)).thenReturn(mockAccount);
        when(categoryDao.getCategoryIdsByIdentifiers(anyList(), anyInt())).thenReturn(List.of(10));
        when(categoryDao.getKpiCountForCategory(anyInt())).thenReturn(0);
        UtilityBean<List<Integer>> result = deleteCategoriesBL.serverValidation(inputBean);
        assertNotNull(result);
        assertEquals(1, result.getPojoObject().size());
        assertEquals(10, result.getPojoObject().get(0));
    }

    /**
     * Tests server validation when account validation fails, expecting ServerException.
     */
    @Test
    void testServerValidation_accountValidationFails() throws Exception {
        String accountIdentifier = "acc-1";
        DeleteCategoriesPojo pojo = DeleteCategoriesPojo.builder().hardDelete(true).categoryIdentifiers(List.of("cat-1")).build();
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.HARD_DELETE, true);
        UtilityBean<DeleteCategoriesPojo> inputBean = UtilityBean.<DeleteCategoriesPojo>builder()
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .pojoObject(pojo)
                .metadata(metadata)
                .build();
        when(serverValidationUtils.accountValidation(accountIdentifier)).thenThrow(new ServerException("Account not found"));
        assertThrows(ServerException.class, () -> deleteCategoriesBL.serverValidation(inputBean));
    }

    /**
     * Tests server validation when category is mapped to KPI, expecting ServerException.
     */
    @Test
    void testServerValidationCategoryMappedToKpiThrows() throws Exception {
        String accountIdentifier = "acc-1";
        DeleteCategoriesPojo pojo = DeleteCategoriesPojo.builder().hardDelete(true).categoryIdentifiers(List.of("cat-1")).build();
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.HARD_DELETE, true);
        UtilityBean<DeleteCategoriesPojo> inputBean = UtilityBean.<DeleteCategoriesPojo>builder()
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .pojoObject(pojo)
                .metadata(metadata)
                .build();
        Account mockAccount = new Account();
        mockAccount.setId(1);
        when(serverValidationUtils.accountValidation(accountIdentifier)).thenReturn(mockAccount);
        when(categoryDao.getCategoryIdsByIdentifiers(anyList(), anyInt())).thenReturn(List.of(10));
        when(categoryDao.getKpiCountForCategory(anyInt())).thenReturn(2);
        assertThrows(ServerException.class, () -> deleteCategoriesBL.serverValidation(inputBean));
    }

    /**
     * Tests soft delete process for categories, expecting successful deletion message.
     */
    @Test
    void testProcessSoftDelete() throws Exception {
        String accountIdentifier = "acc-1";
        List<Integer> catIds = List.of(10);
        UtilityBean<List<Integer>> input = UtilityBean.<List<Integer>>builder()
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .pojoObject(catIds)
                .metadata(Map.of(Constants.HARD_DELETE, false))
                .build();
        com.heal.configuration.pojos.Category cat = new com.heal.configuration.pojos.Category();
        cat.setId(10);
        when(categoryRepo.getCategoryDetails(accountIdentifier)).thenReturn(List.of(cat));
        when(categoryDao.softDeleteCategoryById(10)).thenReturn(1);
        doNothing().when(categoryRepo).deleteCategoryInRedis(anyString(), any());
        doNothing().when(categoryRepo).updateCategoryDetails(anyString(), anyList());
        String result = (String) deleteCategoriesBL.process(input);
        assertEquals("Category(ies) deleted successfully.", result);
    }

    /**
     * Tests hard delete process failure, expecting DataProcessingException.
     */
    @Test
    void testProcessHardDeleteFailure() throws Exception {
        String accountIdentifier = "acc-1";
        List<Integer> catIds = List.of(10);
        UtilityBean<List<Integer>> input = UtilityBean.<List<Integer>>builder()
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .pojoObject(catIds)
                .metadata(Map.of(Constants.HARD_DELETE, true))
                .build();
        when(categoryDao.deleteCategoryById(10)).thenReturn(-1); // Simulate failure
        assertThrows(DataProcessingException.class, () -> deleteCategoriesBL.process(input));
    }
}
