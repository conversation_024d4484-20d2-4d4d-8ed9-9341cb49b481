package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.SMTPDetailsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.NotificationsDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.SMTPDetailsPojo;
import com.heal.controlcenter.util.AECSBouncyCastleUtil;
import com.heal.controlcenter.util.CommonUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@PrepareForTest({GetEmailConfigurationsBL.class})
class GetEmailConfigurationsBLTest {

    @InjectMocks
    GetEmailConfigurationsBL getEmailConfigurationsBL;

    @Mock
    CommonUtils commonUtils;
    @Mock
    AccountsDao accountsDao;
    @Mock
    NotificationsDao notificationsDao;
    @Mock
    AECSBouncyCastleUtil aecsBouncyCastleUtil;

    String[] requestParams = new String[2];
    UtilityBean<Object> mockUtilityBean = null;
    SMTPDetailsBean smtpDetailsBean = new SMTPDetailsBean();

    @BeforeEach
    void setUp() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "mockAccountIdentifier";

        mockUtilityBean = UtilityBean.builder()
                .requestParams(Map.of(
                        "Authorization", "mockAuthToken",
                        "accountIdentifier", "mockAccountIdentifier"
                ))
                .metadata(Map.of(
                        "userId", "mockUserId"
                ))
                .build();

        smtpDetailsBean.setId(4);
        smtpDetailsBean.setAddress("trial");
        smtpDetailsBean.setPassword("password");
    }

    @AfterEach
    void tearDown() {
        requestParams = null;
    }

    @Test
    void clientValidations_EmptyAuthorizationToken() {
        requestParams[0] = "";
        requestParams[1] = "mockAccountIdentifier";

        String expectedMessage = "ClientException : Invalid authorization token";
        ClientException requestException = assertThrows(ClientException.class, () ->
                getEmailConfigurationsBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_EmptyAccountIdentifier() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "";

        String expectedMessage = "ClientException : Invalid account identifier";
        ClientException requestException = assertThrows(ClientException.class, () ->
                getEmailConfigurationsBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_ErrorWhileFetchingUserId() throws Exception {
        String expectedMessage = "ClientException : Error occurred while fetching userId from authorization token";
        when(commonUtils.getUserId("mockAuthToken")).thenThrow(HealControlCenterException.class);
        ClientException requestException = assertThrows(ClientException.class, () ->
                getEmailConfigurationsBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_AuthKeyInvalid() throws Exception {
        String expectedMessage = "ClientException : Error occurred while fetching userId from authorization token";
        when(commonUtils.getUserId("mockAuthToken")).thenReturn(any());
        ClientException requestException = assertThrows(ClientException.class, () ->
                getEmailConfigurationsBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_Success() throws Exception {
        String userId = "mockUserId";
        when(commonUtils.getUserId("mockAuthToken")).thenReturn(userId);
        UtilityBean<Object> utilityBean = getEmailConfigurationsBL.clientValidation(null, requestParams);
        assertEquals(utilityBean.getMetadata().get("userId"), userId);
    }

    @Test
    void serverValidation_Success() throws Exception {
        AccountBean account = new AccountBean();
        account.setId(1);

        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(account);
        Integer accountId = getEmailConfigurationsBL.serverValidation(mockUtilityBean);
        assertEquals(accountId, account.getId());
    }

    @Test
    void serverValidation_InvalidAccountIdentifier() {
        String expectedMessage = "ServerException : Account with identifier [mockAccountIdentifier] is unavailable";
        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(any());
        ServerException requestException = assertThrows(ServerException.class, () ->
                getEmailConfigurationsBL.serverValidation(mockUtilityBean));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void process_Success() throws Exception {
        when(notificationsDao.getSMTPDetails(2)).thenReturn(smtpDetailsBean);
        when(commonUtils.decryptInBCEC("password")).thenReturn("password");
        when(aecsBouncyCastleUtil.encrypt("password")).thenReturn("encryptedPassword");
        SMTPDetailsPojo data = getEmailConfigurationsBL.process(2);
        assertEquals(data.getAddress(), "trial");
    }

    @Test
    void process_Failure() throws Exception {
        String expectedMessage = "DataProcessingException : Error occurred while decrypting the password from the database.";

        when(notificationsDao.getSMTPDetails(2)).thenReturn(smtpDetailsBean);
        ServerException serverException = new ServerException("Error occurred while decrypting the password from the database.");
        when(commonUtils.decryptInBCEC("password")).thenThrow(serverException);
        DataProcessingException requestException = assertThrows(DataProcessingException.class, () ->
                getEmailConfigurationsBL.process(2));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void process_Success_NoConfigurations() throws Exception {
        when(notificationsDao.getSMTPDetails(2)).thenReturn(null);
        SMTPDetailsPojo data = getEmailConfigurationsBL.process(2);
        assertNull(data);
    }
}
