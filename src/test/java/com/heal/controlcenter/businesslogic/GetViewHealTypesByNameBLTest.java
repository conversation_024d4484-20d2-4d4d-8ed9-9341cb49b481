package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.redis.MasterDataRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.pojo.ViewTypeResponse;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.UIMessages;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for GetViewHealTypesByNameBL class, covering client validation, server validation, and process logic.
 */
@ExtendWith(MockitoExtension.class)
class GetViewHealTypesByNameBLTest {

    @InjectMocks
    GetViewTypesByNameBL getViewHealTypesByNameBL;

    @Mock
    MasterDataRepo masterDataRepo;
    @Mock
    ClientValidationUtils clientValidationUtils;

    /**
     * Default type name used for positive and negative test cases.
     */
    private static final String TEST_TYPE_NAME = "Environment";
    private UtilityBean<String> validUtilityBean;

    @BeforeEach
    void setUp() {
        validUtilityBean = UtilityBean.<String>builder()
                .pojoObject(TEST_TYPE_NAME)
                .metadata(Collections.emptyMap())
                .build();
    }

    /**
     * Tests successful client validation when typeName is valid.
     */
    @Test
    void testClientValidation_success() throws ClientException {
        doNothing().when(clientValidationUtils).nullOrEmptyCheck(TEST_TYPE_NAME, UIMessages.TYPE_NAME_INVALID);
        UtilityBean<String> result = getViewHealTypesByNameBL.clientValidation(TEST_TYPE_NAME);
        assertNotNull(result);
        assertEquals(TEST_TYPE_NAME, result.getPojoObject());
        verify(clientValidationUtils, times(1)).nullOrEmptyCheck(TEST_TYPE_NAME, UIMessages.TYPE_NAME_INVALID);
    }

    /**
     * Tests client validation when typeName is invalid (null or empty).
     */
    @Test
    void testClientValidation_typeNameInvalid_throwsClientException() throws ClientException {
        String invalidTypeName = "";
        doThrow(new ClientException(UIMessages.TYPE_NAME_INVALID)).when(clientValidationUtils).nullOrEmptyCheck(invalidTypeName, UIMessages.TYPE_NAME_INVALID);
        ClientException exception = assertThrows(ClientException.class, () -> getViewHealTypesByNameBL.clientValidation(invalidTypeName));
        assertEquals("ClientException : " + UIMessages.TYPE_NAME_INVALID, exception.getMessage());
        verify(clientValidationUtils, times(1)).nullOrEmptyCheck(invalidTypeName, UIMessages.TYPE_NAME_INVALID);
    }

    /**
     * Tests server validation (no-op, always returns input bean).
     */
    @Test
    void testServerValidation_success() throws Exception {
        UtilityBean<String> result = getViewHealTypesByNameBL.serverValidation(validUtilityBean);
        assertNotNull(result);
        assertEquals(TEST_TYPE_NAME, result.getPojoObject());
    }

    /**
     * Tests process method when records are found for the given type name.
     */
    @Test
    void testProcess_success() throws DataProcessingException {
        List<com.heal.configuration.pojos.ViewTypes> mockViewTypes = List.of(
                new com.heal.configuration.pojos.ViewTypes(1, "Environ", 1, "SubType1"),
                new com.heal.configuration.pojos.ViewTypes(1, "Environ", 2, "SubType2")
        );
        when(masterDataRepo.getViewTypesByName(TEST_TYPE_NAME)).thenReturn(mockViewTypes);
        List<ViewTypeResponse> result = getViewHealTypesByNameBL.process(validUtilityBean);
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("SubType1", result.get(0).getName());
        assertEquals("SubType2", result.get(1).getName());
        verify(masterDataRepo, times(1)).getViewTypesByName(TEST_TYPE_NAME);
    }

    /**
     * Tests process method when no records are found for the given type name.
     */
    @Test
    void testProcess_noRecordsFound_throwsDataProcessingException() {
        when(masterDataRepo.getViewTypesByName(TEST_TYPE_NAME)).thenReturn(Collections.emptyList());
        DataProcessingException exception = assertThrows(DataProcessingException.class, () -> getViewHealTypesByNameBL.process(validUtilityBean));
        assertTrue(exception.getMessage().contains(UIMessages.NO_RECORDS_FOR_TYPE_NAME));
        verify(masterDataRepo, times(1)).getViewTypesByName(TEST_TYPE_NAME);
    }

    /**
     * Tests process method when an exception occurs during fetching.
     */
    @Test
    void testProcess_exceptionThrown_throwsDataProcessingException() {
        when(masterDataRepo.getViewTypesByName(TEST_TYPE_NAME)).thenThrow(new RuntimeException("DB error"));
        DataProcessingException exception = assertThrows(DataProcessingException.class, () -> getViewHealTypesByNameBL.process(validUtilityBean));
        assertTrue(exception.getMessage().contains("Error while fetching ViewTypeResponse"));
        verify(masterDataRepo, times(1)).getViewTypesByName(TEST_TYPE_NAME);
    }
}
