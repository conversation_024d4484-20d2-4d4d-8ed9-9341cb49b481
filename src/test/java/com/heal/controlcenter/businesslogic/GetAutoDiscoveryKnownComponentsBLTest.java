package com.heal.controlcenter.businesslogic;

import com.appnomic.appsone.common.beans.discovery.Component;
import com.heal.controlcenter.beans.AutoDiscoveryKnownComponentAttributesBean;
import com.heal.controlcenter.dao.mysql.AutoDiscoveryDao;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@PrepareForTest({GetAutoDiscoveryKnownComponentsBL.class})
public class GetAutoDiscoveryKnownComponentsBLTest {

    @InjectMocks
    GetAutoDiscoveryKnownComponentsBL getAutoDiscoveryKnownComponentsBL;
    @Mock
    AutoDiscoveryDao autoDiscoveryDao;

    List<AutoDiscoveryKnownComponentAttributesBean> knownCompAttrBeansList;

    @BeforeEach
    void setUp() {
        knownCompAttrBeansList = new ArrayList<>();
    }

    @AfterEach
    void tearDown() {
        knownCompAttrBeansList = null;
    }

    @Test
    void process_Success() throws Exception {
        AutoDiscoveryKnownComponentAttributesBean knownComponentAttributesBean = new AutoDiscoveryKnownComponentAttributesBean();
        knownComponentAttributesBean.setAttributeName("Description");
        knownComponentAttributesBean.setDiscoveryPattern("(?<process>([h][t][t][p][d]))");
        knownComponentAttributesBean.setId(1);
        knownComponentAttributesBean.setName("Apache httpd - Apache");
        knownComponentAttributesBean.setMstComponentTypeId(2);
        knownComponentAttributesBean.setRelativePath("conf/httpd.conf");
        knownComponentAttributesBean.setIsMandatory(0);
        knownComponentAttributesBean.setPriority(1);
        knownCompAttrBeansList.add(knownComponentAttributesBean);
        knownComponentAttributesBean = new AutoDiscoveryKnownComponentAttributesBean();
        knownComponentAttributesBean.setAttributeName("HostAddress");
        knownComponentAttributesBean.setDiscoveryPattern("(?<process>([h][t][t][p][d]))");
        knownComponentAttributesBean.setId(1);
        knownComponentAttributesBean.setName("Apache httpd - Apache");
        knownComponentAttributesBean.setMstComponentTypeId(2);
        knownComponentAttributesBean.setRelativePath("conf/httpd.conf");
        knownComponentAttributesBean.setIsMandatory(1);
        knownComponentAttributesBean.setPriority(1);
        knownCompAttrBeansList.add(knownComponentAttributesBean);
        knownComponentAttributesBean = new AutoDiscoveryKnownComponentAttributesBean();
        knownComponentAttributesBean.setAttributeName("HostAddress");
        knownComponentAttributesBean.setDiscoveryPattern("(?<process>([h][t][t][p][d]))");
        knownComponentAttributesBean.setId(1);
        knownComponentAttributesBean.setName("Apache httpd - Apache");
        knownComponentAttributesBean.setMstComponentTypeId(2);
        knownComponentAttributesBean.setRelativePath("conf/httpd.conf");
        knownComponentAttributesBean.setIsMandatory(1);
        knownComponentAttributesBean.setPriority(2);
        knownCompAttrBeansList.add(knownComponentAttributesBean);

        when(autoDiscoveryDao.getADComponentAttributeDetails()).thenReturn(knownCompAttrBeansList);
        List<Component> data = getAutoDiscoveryKnownComponentsBL.process(new Object());
        assertEquals(1, data.size());
    }

    @Test
    void process_Failure() throws HealControlCenterException {
        String expectedMessage = "DataProcessingException : Error while fetching known components and their attributes. List is empty.";

        when(autoDiscoveryDao.getADComponentAttributeDetails()).thenReturn(knownCompAttrBeansList);
        DataProcessingException requestException = assertThrows(DataProcessingException.class, () ->
                getAutoDiscoveryKnownComponentsBL.process(new Object()));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }
}
