package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.beans.ControllerBean;
import com.heal.controlcenter.beans.TimezoneBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.beans.ViewApplicationServiceMappingBean;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.dao.mysql.TimeZoneDao;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ApplicationPojo;
import com.heal.controlcenter.pojo.ClusterComponentDetails;
import com.heal.controlcenter.pojo.ServiceClusterDetails;
import com.heal.controlcenter.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class GetApplicationsBLTest {
    @Mock
    UserValidationUtil userValidationUtil;
    @Mock
    ControllerDao controllerDao;
    @Mock
    DateTimeUtil dateTimeUtil;
    @Mock
    UserDao userDao;
    @Mock
    TimeZoneDao timeZoneDao;
    @Mock
    ClientValidationUtils clientValidationUtils;
    @Mock
    ServerValidationUtils serverValidationUtils;

    @InjectMocks
    GetApplicationsBL getApplicationsBL;

    private Account account;
    private Pageable pageable;
    private ControllerBean controllerBean;
    private UtilityBean<Account> utilityBean;

    @BeforeEach
    void setup() {
        account = new Account();
        account.setId(1);
        account.setIdentifier("acc-1");
        pageable = PageRequest.of(0, 10);
        controllerBean = new ControllerBean();
        controllerBean.setId(1);
        controllerBean.setIdentifier("app-1");
        controllerBean.setName("App1");
        controllerBean.setEnvironment("dev");
        controllerBean.setLastModifiedBy("user-1");
        controllerBean.setUpdatedTime("2025-07-17 10:00:00");
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID_KEY, "user-1");
        Map<String, String> reqParams = new HashMap<>();
        reqParams.put(Constants.SEARCH_TERM_KEY, "");
        reqParams.put(Constants.ACCOUNT_IDENTIFIER, "acc-1");
        utilityBean = UtilityBean.<Account>builder()
                .pojoObject(account)
                .metadata(metadata)
                .requestParams(reqParams)
                .pageable(pageable)
                .build();
    }

    @Test
    void testClientValidation_success() throws ClientException {
        doNothing().when(clientValidationUtils).accountIdentifierValidation(anyString());
        UtilityBean<String> result = getApplicationsBL.clientValidation(null, "acc-1", "true", "search");
        assertNotNull(result);
        assertEquals("acc-1", result.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
        assertEquals("search", result.getRequestParams().get(Constants.SEARCH_TERM_KEY));
    }

    @Test
    void testClientValidation_searchTermTooLong_throwsException() {
        String longSearch = "a".repeat(101);
        Exception ex = assertThrows(ClientException.class, () ->
                getApplicationsBL.clientValidation(null, "acc-1", "true", longSearch));
        assertTrue(ex.getMessage().contains("maximum length"));
    }

    @Test
    void testClientValidation_nullSearchTerm_defaultsToEmptyString() throws Exception {
        doNothing().when(clientValidationUtils).accountIdentifierValidation(anyString());
        UtilityBean<String> result = getApplicationsBL.clientValidation(null, "acc-1", "true", null);
        assertNotNull(result);
        assertEquals("", result.getRequestParams().get(Constants.SEARCH_TERM_KEY));
    }

    @Test
    void testClientValidation_emptySearchTerm_defaultsToEmptyString() throws Exception {
        doNothing().when(clientValidationUtils).accountIdentifierValidation(anyString());
        UtilityBean<String> result = getApplicationsBL.clientValidation(null, "acc-1", "true", "   ");
        assertNotNull(result);
        assertEquals("", result.getRequestParams().get(Constants.SEARCH_TERM_KEY));
    }

    @Test
    void testClientValidation_noParams_defaultsToEmptyString() throws Exception {
        doNothing().when(clientValidationUtils).accountIdentifierValidation(anyString());
        UtilityBean<String> result = getApplicationsBL.clientValidation(null, "acc-1", "true");
        assertNotNull(result);
        assertEquals("", result.getRequestParams().get(Constants.SEARCH_TERM_KEY));
    }

    @Test
    void testServerValidation_success() throws ServerException {
        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);
        UtilityBean<String> input = UtilityBean.<String>builder()
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, "acc-1", Constants.SEARCH_TERM_KEY, ""))
                .metadata(Map.of(Constants.USER_ID_KEY, "user-1"))
                .pageable(pageable)
                .build();
        UtilityBean<Account> result = getApplicationsBL.serverValidation(input);
        assertNotNull(result);
        assertEquals(account, result.getPojoObject());
    }

    @Test
    void testServerValidation_accountValidationFails() throws ServerException {
        when(serverValidationUtils.accountValidation(anyString())).thenThrow(new ServerException("Account not found"));
        UtilityBean<String> input = UtilityBean.<String>builder()
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, "acc-1", Constants.SEARCH_TERM_KEY, ""))
                .metadata(Map.of(Constants.USER_ID_KEY, "user-1"))
                .pageable(pageable)
                .build();
        assertThrows(ServerException.class, () -> getApplicationsBL.serverValidation(input));
    }

    @Test
    void testServerValidation_nullMetadata_throwsNullPointerException() {
        UtilityBean<String> input = UtilityBean.<String>builder()
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, "acc-1", Constants.SEARCH_TERM_KEY, ""))
                .build();
        assertThrows(NullPointerException.class, () -> getApplicationsBL.serverValidation(input));
    }

    @Test
    void testProcess_success() throws DataProcessingException, HealControlCenterException {
        when(userValidationUtil.getAccessibleApplicationsCountForUser(anyString(), anyString(), anyString())).thenReturn(1L);
        when(userValidationUtil.getAccessibleApplicationsForUserPaginated(anyString(), anyString(), anyString(), any())).thenReturn(List.of(controllerBean));
        when(controllerDao.getServicesMappedToApplications(anyInt(), anyList())).thenReturn(Collections.emptyList());
        when(userDao.getUsernameFromIdentifier(anyString())).thenReturn("user1");
        when(dateTimeUtil.getGMTToEpochTime(anyString())).thenReturn(123456789L);
        when(timeZoneDao.getApplicationTimezoneDetails(anyInt())).thenReturn(new TimezoneBean());
        Page<ApplicationPojo> result = getApplicationsBL.process(utilityBean);
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals("App1", result.getContent().get(0).getName());
    }

    @Test
    void testProcess_noApplications_returnsEmptyPage() throws DataProcessingException, HealControlCenterException {
        when(userValidationUtil.getAccessibleApplicationsCountForUser(anyString(), anyString(), anyString())).thenReturn(0L);
        Page<ApplicationPojo> result = getApplicationsBL.process(utilityBean);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testProcess_exceptionThrown() throws HealControlCenterException {
        when(userValidationUtil.getAccessibleApplicationsCountForUser(anyString(), anyString(), anyString())).thenThrow(new DataProcessingException("DB error"));
        assertThrows(DataProcessingException.class, () -> getApplicationsBL.process(utilityBean));
    }

    @Test
    void testProcess_pageableSortNotSorted() throws DataProcessingException, HealControlCenterException {
        when(userValidationUtil.getAccessibleApplicationsCountForUser(anyString(), anyString(), anyString())).thenReturn(1L);
        when(userValidationUtil.getAccessibleApplicationsForUserPaginated(anyString(), anyString(), anyString(), any())).thenReturn(List.of(controllerBean));
        when(controllerDao.getServicesMappedToApplications(anyInt(), anyList())).thenReturn(Collections.emptyList());
        when(userDao.getUsernameFromIdentifier(anyString())).thenReturn("user1");
        when(dateTimeUtil.getGMTToEpochTime(anyString())).thenReturn(123456789L);
        when(timeZoneDao.getApplicationTimezoneDetails(anyInt())).thenReturn(new TimezoneBean());
        UtilityBean<Account> bean = UtilityBean.<Account>builder()
                .pojoObject(account)
                .metadata(Map.of(Constants.USER_ID_KEY, "user-1"))
                .requestParams(Map.of(Constants.SEARCH_TERM_KEY, "", Constants.ACCOUNT_IDENTIFIER, "acc-1"))
                .pageable(PageRequest.of(0, 10))
                .build();
        Page<ApplicationPojo> result = getApplicationsBL.process(bean);
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
    }

    @Test
    void testProcess_withSorting() throws DataProcessingException, HealControlCenterException {
        ControllerBean cb1 = new ControllerBean();
        cb1.setId(1);
        cb1.setIdentifier("app-1");
        cb1.setName("App1");
        cb1.setEnvironment("dev");
        cb1.setLastModifiedBy("bob");
        cb1.setUpdatedTime("2025-07-17 10:00:00");
        ControllerBean cb2 = new ControllerBean();
        cb2.setId(2);
        cb2.setIdentifier("app-2");
        cb2.setName("App2");
        cb2.setEnvironment("prod");
        cb2.setLastModifiedBy("alice");
        cb2.setUpdatedTime("2025-07-18 10:00:00");
        ControllerBean cb3 = new ControllerBean();
        cb3.setId(3);
        cb3.setIdentifier("app-3");
        cb3.setName("App3");
        cb3.setEnvironment("dev");
        cb3.setLastModifiedBy("bob");
        cb3.setUpdatedTime("2025-07-19 10:00:00");
        List<ControllerBean> beans = List.of(cb1, cb2, cb3);
        when(userValidationUtil.getAccessibleApplicationsCountForUser(anyString(), anyString(), anyString())).thenReturn(3L);
        when(userValidationUtil.getAccessibleApplicationsForUserPaginated(anyString(), anyString(), anyString(), any())).thenReturn(beans);
        when(controllerDao.getServicesMappedToApplications(anyInt(), anyList())).thenReturn(Collections.emptyList());
        when(userDao.getUsernameFromIdentifier(anyString())).thenReturn("user1");
        when(dateTimeUtil.getGMTToEpochTime(anyString())).thenReturn(123456789L);
        when(timeZoneDao.getApplicationTimezoneDetails(anyInt())).thenReturn(new TimezoneBean());
        Pageable pageable = PageRequest.of(0, 10, org.springframework.data.domain.Sort.by(
                org.springframework.data.domain.Sort.Order.asc("lastModifiedBy"),
                org.springframework.data.domain.Sort.Order.desc("lastModifiedOn")
        ));
        UtilityBean<Account> bean = UtilityBean.<Account>builder()
                .pojoObject(account)
                .metadata(Map.of(Constants.USER_ID_KEY, "user-1"))
                .requestParams(Map.of(Constants.SEARCH_TERM_KEY, "", Constants.ACCOUNT_IDENTIFIER, "acc-1"))
                .pageable(pageable)
                .build();
        Page<ApplicationPojo> result = getApplicationsBL.process(bean);
        assertEquals(3, result.getTotalElements());
        List<ApplicationPojo> sorted = result.getContent();
        assertEquals("app-1", sorted.get(0).getIdentifier()); // bob, older
    }

    @Test
    void testProcess_unexpectedException() throws HealControlCenterException {
        when(userValidationUtil.getAccessibleApplicationsCountForUser(anyString(), anyString(), anyString())).thenThrow(new DataProcessingException("Unexpected error"));
        DataProcessingException exception = assertThrows(DataProcessingException.class, () -> getApplicationsBL.process(utilityBean));
        assertTrue(exception.getMessage().contains("Failed to process applications"));
    }

    @Test
    void testGetApplicationComparator_lastModifiedByAsc_lastModifiedOnDesc() throws Exception {
        Pageable pageable = PageRequest.of(0, 10, Sort.by(
                Sort.Order.asc("lastModifiedBy"),
                Sort.Order.desc("lastModifiedOn")
        ));
        Comparator<ApplicationPojo> comparator = invokeGetApplicationComparator(pageable);
        ApplicationPojo app1 = ApplicationPojo.builder().lastModifiedBy("bob").lastModifiedOn(100L).build();
        ApplicationPojo app2 = ApplicationPojo.builder().lastModifiedBy("alice").lastModifiedOn(200L).build();
        ApplicationPojo app3 = ApplicationPojo.builder().lastModifiedBy("bob").lastModifiedOn(300L).build();
        List<ApplicationPojo> apps = new ArrayList<>(List.of(app1, app2, app3));
        apps.sort(comparator);
        assertEquals("alice", apps.get(0).getLastModifiedBy());
        assertEquals("bob", apps.get(1).getLastModifiedBy());
        assertEquals(300L, apps.get(1).getLastModifiedOn()); // bob, latest
        assertEquals(100L, apps.get(2).getLastModifiedOn()); // bob, older
    }

    @Test
    void testGetApplicationComparator_lastModifiedByDesc_lastModifiedOnAsc() throws Exception {
        Pageable pageable = PageRequest.of(0, 10, Sort.by(
                Sort.Order.desc("lastModifiedBy"),
                Sort.Order.asc("lastModifiedOn")
        ));
        Comparator<ApplicationPojo> comparator = invokeGetApplicationComparator(pageable);
        ApplicationPojo app1 = ApplicationPojo.builder().lastModifiedBy("bob").lastModifiedOn(100L).build();
        ApplicationPojo app2 = ApplicationPojo.builder().lastModifiedBy("alice").lastModifiedOn(200L).build();
        ApplicationPojo app3 = ApplicationPojo.builder().lastModifiedBy("bob").lastModifiedOn(300L).build();
        List<ApplicationPojo> apps = new ArrayList<>(List.of(app1, app2, app3));
        apps.sort(comparator);
        assertEquals("bob", apps.get(0).getLastModifiedBy());
        assertEquals(100L, apps.get(0).getLastModifiedOn());
        assertEquals("bob", apps.get(1).getLastModifiedBy());
        assertEquals(300L, apps.get(1).getLastModifiedOn());
        assertEquals("alice", apps.get(2).getLastModifiedBy());
    }

    @Test
    void testGetApplicationComparator_lastModifiedOnAscOnly() throws Exception {
        Pageable pageable = PageRequest.of(0, 10, Sort.by(
                Sort.Order.asc("lastModifiedOn")
        ));
        Comparator<ApplicationPojo> comparator = invokeGetApplicationComparator(pageable);
        ApplicationPojo app1 = ApplicationPojo.builder().lastModifiedOn(100L).build();
        ApplicationPojo app2 = ApplicationPojo.builder().lastModifiedOn(200L).build();
        ApplicationPojo app3 = ApplicationPojo.builder().lastModifiedOn(50L).build();
        List<ApplicationPojo> apps = new ArrayList<>(List.of(app1, app2, app3));
        apps.sort(comparator);
        assertEquals(50L, apps.get(0).getLastModifiedOn());
        assertEquals(100L, apps.get(1).getLastModifiedOn());
        assertEquals(200L, apps.get(2).getLastModifiedOn());
    }

    @Test
    void testGetApplicationComparator_lastModifiedOnDescOnly() throws Exception {
        Pageable pageable = PageRequest.of(0, 10, Sort.by(
                Sort.Order.desc("lastModifiedOn")
        ));
        Comparator<ApplicationPojo> comparator = invokeGetApplicationComparator(pageable);
        ApplicationPojo app1 = ApplicationPojo.builder().lastModifiedOn(100L).build();
        ApplicationPojo app2 = ApplicationPojo.builder().lastModifiedOn(200L).build();
        ApplicationPojo app3 = ApplicationPojo.builder().lastModifiedOn(50L).build();
        List<ApplicationPojo> apps = new ArrayList<>(List.of(app1, app2, app3));
        apps.sort(comparator);
        assertEquals(200L, apps.get(0).getLastModifiedOn());
        assertEquals(100L, apps.get(1).getLastModifiedOn());
        assertEquals(50L, apps.get(2).getLastModifiedOn());
    }

    @Test
    void testGetApplicationComparator_nullSort_returnsNull() throws Exception {
        Pageable pageable = PageRequest.of(0, 10);
        Comparator<ApplicationPojo> comparator = invokeGetApplicationComparator(pageable);
        assertNull(comparator);
    }

    @Test
    void testGetMappedServices_clusterDataNotRequired_returnsBasicDetails() throws Exception {
        // Set clusterDataRequired to false
        var field = GetApplicationsBL.class.getDeclaredField("clusterDataRequired");
        field.setAccessible(true);
        field.set(getApplicationsBL, false);
        ViewApplicationServiceMappingBean service = ViewApplicationServiceMappingBean.builder()
                .serviceId(1)
                .serviceName("Service1")
                .serviceIdentifier("svc-1")
                .build();
        List<ViewApplicationServiceMappingBean> mappedServices = List.of(service);
        List<ServiceClusterDetails> result = invokeGetMappedServices(mappedServices);
        assertEquals(1, result.size());
        assertEquals("Service1", result.get(0).getName());
        assertNull(result.get(0).getHostCluster());
        assertNull(result.get(0).getComponentCluster());
    }

    @Test
    void testGetMappedServices_clusterDataRequired_returnsClusterDetails() throws Exception {
        var field = GetApplicationsBL.class.getDeclaredField("clusterDataRequired");
        field.setAccessible(true);
        field.set(getApplicationsBL, true);
        ViewApplicationServiceMappingBean service = ViewApplicationServiceMappingBean.builder()
                .serviceId(1)
                .serviceName("Service1")
                .serviceIdentifier("svc-1")
                .build();
        List<ViewApplicationServiceMappingBean> mappedServices = List.of(service);
        ClusterComponentDetails host = ClusterComponentDetails.builder().serviceIdentifier("svc-1").componentName("Host1").build();
        ClusterComponentDetails comp = ClusterComponentDetails.builder().serviceIdentifier("svc-1").componentName("Comp1").build();
        when(controllerDao.getHostClusterComponentDetailsForServices(anyList())).thenReturn(List.of(host));
        when(controllerDao.getComponentClusterComponentDetailsForServices(anyList())).thenReturn(List.of(comp));
        List<ServiceClusterDetails> result = invokeGetMappedServices(mappedServices);
        assertEquals(1, result.size());
        assertEquals("Service1", result.get(0).getName());
        assertEquals(1, result.get(0).getHostCluster().size());
        assertEquals("Host1", result.get(0).getHostCluster().get(0).getComponentName());
        assertEquals(1, result.get(0).getComponentCluster().size());
        assertEquals("Comp1", result.get(0).getComponentCluster().get(0).getComponentName());
    }

    @Test
    void testGetMappedServices_exception_returnsEmptyList() throws Exception {
        var field = GetApplicationsBL.class.getDeclaredField("clusterDataRequired");
        field.setAccessible(true);
        field.set(getApplicationsBL, true);
        ViewApplicationServiceMappingBean service = ViewApplicationServiceMappingBean.builder()
                .serviceId(1)
                .serviceName("Service1")
                .serviceIdentifier("svc-1")
                .build();
        List<ViewApplicationServiceMappingBean> mappedServices = List.of(service);
        when(controllerDao.getHostClusterComponentDetailsForServices(anyList())).thenThrow(new DataProcessingException("DB error"));
        List<ServiceClusterDetails> result = invokeGetMappedServices(mappedServices);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    // Helper to access private static getApplicationComparator
    private Comparator<ApplicationPojo> invokeGetApplicationComparator(Pageable pageable) throws Exception {
        var method = GetApplicationsBL.class.getDeclaredMethod("getApplicationComparator", Pageable.class);
        method.setAccessible(true);
        return (Comparator<ApplicationPojo>) method.invoke(null, pageable);
    }

    // Helper to access private getMappedServices
    private List<ServiceClusterDetails> invokeGetMappedServices(List<ViewApplicationServiceMappingBean> mappedServices) throws Exception {
        var method = GetApplicationsBL.class.getDeclaredMethod("getMappedServices", List.class);
        method.setAccessible(true);
        return (List<ServiceClusterDetails>) method.invoke(getApplicationsBL, mappedServices);
    }
}
