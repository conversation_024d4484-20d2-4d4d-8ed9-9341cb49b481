package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.beans.AccountKPIKey;
import com.heal.controlcenter.beans.CategoryDetailBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.CategoryDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.CategoryDetails;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.ServerValidationUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class GetCategoriesBLTest {
    @InjectMocks
    GetCategoriesBL getCategoriesBL;
    @Mock
    CategoryDao categoryDao;
    @Mock
    ClientValidationUtils clientValidationUtils;
    @Mock
    ServerValidationUtils serverValidationUtils;

    private Pageable pageable;
    private Account account;
    private UtilityBean<AccountKPIKey> utilityBean;

    @BeforeEach
    void setup() {
        pageable = PageRequest.of(0, 10);
        account = new Account();
        account.setId(1);
        account.setIdentifier("acc-1");
        AccountKPIKey key = AccountKPIKey.builder().accountId(1).accountIdentifier("acc-1").kpiTypeId(-1).build();
        utilityBean = UtilityBean.<AccountKPIKey>builder()
                .pojoObject(key)
                .requestParams(Map.of("searchTerm", "", "type", "CUSTOM", "subType", "WORKLOAD", Constants.ACCOUNT_IDENTIFIER, "acc-1"))
                .metadata(new HashMap<>())
                .pageable(pageable)
                .build();
    }

    /**
     * Tests successful client validation for fetching categories.
     * Ensures that valid parameters return a UtilityBean with correct request params.
     */
    @Test
    void testClientValidation_success() throws ClientException {
        doNothing().when(clientValidationUtils).accountIdentifierValidation(anyString());
        UtilityBean<String> result = getCategoriesBL.clientValidation(null, "acc-1", "search", "CUSTOM", "WORKLOAD", "kpiType");
        assertNotNull(result);
        assertEquals("acc-1", result.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
        assertEquals("search", result.getRequestParams().get("searchTerm"));
        assertEquals("CUSTOM", result.getRequestParams().get("type"));
        assertEquals("WORKLOAD", result.getRequestParams().get("subType"));
        assertEquals("kpiType", result.getRequestParams().get(Constants.KPI_TYPE));
    }

    /**
     * Tests client validation with an invalid account, expecting a ClientException.
     */
    @Test
    void testClientValidation_invalidAccount_throwsException() throws Exception {
        doThrow(new ClientException("Invalid")).when(clientValidationUtils).accountIdentifierValidation(anyString());
        assertThrows(ClientException.class, () -> getCategoriesBL.clientValidation(null, "bad", null, null, null, null));
    }

    /**
     * Tests server validation with a valid KPI type, expecting correct AccountKPIKey in result.
     */
    @Test
    void testServerValidation_successWithKpiType() throws Exception {
        UtilityBean<String> input = UtilityBean.<String>builder()
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, "acc-1", Constants.KPI_TYPE, "kpiType"))
                .metadata(new HashMap<>())
                .build();
        Account acc = new Account();
        acc.setId(1);
        when(serverValidationUtils.accountValidation(anyString())).thenReturn(acc);
        when(categoryDao.getKpiTypeBySubTypeName(anyString())).thenReturn(com.heal.controlcenter.beans.ViewTypesBean.builder().subTypeId(2).subTypeName("kpiType").build());
        UtilityBean<AccountKPIKey> result = getCategoriesBL.serverValidation(input);
        assertNotNull(result);
        assertEquals(1, result.getPojoObject().getAccountId());
        assertEquals(2, result.getPojoObject().getKpiTypeId());
    }

    /**
     * Tests server validation with no KPI type provided, expecting default kpiTypeId in result.
     */
    @Test
    void testServerValidation_noKpiType() throws Exception {
        UtilityBean<String> input = UtilityBean.<String>builder()
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, "acc-1"))
                .metadata(new HashMap<>())
                .build();
        Account acc = new Account();
        acc.setId(1);
        when(serverValidationUtils.accountValidation(anyString())).thenReturn(acc);
        UtilityBean<AccountKPIKey> result = getCategoriesBL.serverValidation(input);
        assertNotNull(result);
        assertEquals(1, result.getPojoObject().getAccountId());
        assertEquals(-1, result.getPojoObject().getKpiTypeId());
    }

    /**
     * Tests server validation when fetching KPI type throws an exception, expecting ServerException.
     */
    @Test
    void testServerValidation_kpiTypeException() throws Exception {
        UtilityBean<String> input = UtilityBean.<String>builder()
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, "acc-1", Constants.KPI_TYPE, "bad"))
                .metadata(new HashMap<>())
                .build();
        Account acc = new Account();
        acc.setId(1);
        when(serverValidationUtils.accountValidation(anyString())).thenReturn(acc);
        when(categoryDao.getKpiTypeBySubTypeName(anyString())).thenThrow(new com.heal.controlcenter.exception.HealControlCenterException("not found"));
        assertThrows(ServerException.class, () -> getCategoriesBL.serverValidation(input));
    }

    /**
     * Tests server validation when account validation fails, expecting ServerException.
     */
    @Test
    void testServerValidation_accountValidationFails() throws Exception {
        UtilityBean<String> input = UtilityBean.<String>builder()
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, "acc-1"))
                .metadata(new HashMap<>())
                .build();
        when(serverValidationUtils.accountValidation(anyString())).thenThrow(new ServerException("Account not found"));
        assertThrows(ServerException.class, () -> getCategoriesBL.serverValidation(input));
    }

    /**
     * Tests successful processing of categories, expecting correct result content.
     */
    @Test
    void testProcess_success() throws Exception {
        List<CategoryDetailBean> beans = List.of(CategoryDetailBean.builder().id(1).name("cat1").identifier("cat-1").isCustom(1).isWorkLoad(1).isInformative(0).status(1).description("desc").build());
        when(categoryDao.getCategoriesForAccountWithFilters(anyInt(), anyString(), anyString(), anyString(), any())).thenReturn(beans);
        when(categoryDao.countCategoriesForAccountWithFilters(anyInt(), anyString(), anyString(), anyString())).thenReturn(1);
        when(categoryDao.getKpiCountForCategories(anyList())).thenReturn(Collections.emptyList());
        UtilityBean<AccountKPIKey> utilityBean = UtilityBean.<AccountKPIKey>builder()
                .pojoObject(AccountKPIKey.builder().accountId(1).accountIdentifier("acc-1").kpiTypeId(-1).build())
                .requestParams(Map.of("searchTerm", "", "type", "CUSTOM", "subType", "WORKLOAD", Constants.ACCOUNT_IDENTIFIER, "acc-1"))
                .metadata(new HashMap<>())
                .pageable(PageRequest.of(0, 10))
                .build();
        Page<CategoryDetails> result = getCategoriesBL.process(utilityBean);
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals("cat1", result.getContent().get(0).getName());
    }

    /**
     * Tests process method when an exception is thrown by the DAO, expecting DataProcessingException.
     */
    @Test
    void testProcess_exceptionThrown() throws Exception {
        when(categoryDao.getCategoriesForAccountWithFilters(anyInt(), anyString(), anyString(), anyString(), any())).thenThrow(new com.heal.controlcenter.exception.HealControlCenterException("DB error"));
        UtilityBean<AccountKPIKey> utilityBean = UtilityBean.<AccountKPIKey>builder()
                .pojoObject(AccountKPIKey.builder().accountId(1).accountIdentifier("acc-1").kpiTypeId(-1).build())
                .requestParams(Map.of("searchTerm", "", "type", "CUSTOM", "subType", "WORKLOAD", Constants.ACCOUNT_IDENTIFIER, "acc-1"))
                .metadata(new HashMap<>())
                .pageable(PageRequest.of(0, 10))
                .build();
        assertThrows(DataProcessingException.class, () -> getCategoriesBL.process(utilityBean));
    }

    /**
     * Tests process method for subType WORKLOAD.
     */
    @Test
    void testProcess_subTypeWorkload() throws Exception {
        CategoryDetailBean bean = CategoryDetailBean.builder()
                .id(1)
                .name("cat1")
                .identifier("cat-1")
                .isCustom(1)
                .isWorkLoad(1)
                .isInformative(0)
                .status(1)
                .description("desc")
                .build();
        List<CategoryDetailBean> beans = List.of(bean);
        when(categoryDao.getCategoriesForAccountWithFilters(anyInt(), anyString(), anyString(), anyString(), any())).thenReturn(beans);
        when(categoryDao.countCategoriesForAccountWithFilters(anyInt(), anyString(), anyString(), anyString())).thenReturn(1);
        when(categoryDao.getKpiCountForCategories(anyList())).thenReturn(Collections.emptyList());
        UtilityBean<AccountKPIKey> utilityBean = UtilityBean.<AccountKPIKey>builder()
                .pojoObject(AccountKPIKey.builder().accountId(1).accountIdentifier("acc-1").kpiTypeId(-1).build())
                .requestParams(Map.of("searchTerm", "", "type", "CUSTOM", "subType", "WORKLOAD", Constants.ACCOUNT_IDENTIFIER, "acc-1"))
                .metadata(new HashMap<>())
                .pageable(PageRequest.of(0, 10))
                .build();
        Page<CategoryDetails> result = getCategoriesBL.process(utilityBean);
        assertEquals(1, result.getContent().size());
        assertEquals("WORKLOAD", result.getContent().get(0).getSubType());
    }

    /**
     * Tests process method for subType INFO.
     */
    @Test
    void testProcess_subTypeInfo() throws Exception {
        CategoryDetailBean bean = CategoryDetailBean.builder()
                .id(2)
                .name("cat2")
                .identifier("cat-2")
                .isCustom(1)
                .isWorkLoad(0)
                .isInformative(1)
                .status(1)
                .description("desc")
                .build();
        List<CategoryDetailBean> beans = List.of(bean);
        when(categoryDao.getCategoriesForAccountWithFilters(anyInt(), anyString(), anyString(), anyString(), any())).thenReturn(beans);
        when(categoryDao.countCategoriesForAccountWithFilters(anyInt(), anyString(), anyString(), anyString())).thenReturn(1);
        when(categoryDao.getKpiCountForCategories(anyList())).thenReturn(Collections.emptyList());
        UtilityBean<AccountKPIKey> utilityBean = UtilityBean.<AccountKPIKey>builder()
                .pojoObject(AccountKPIKey.builder().accountId(1).accountIdentifier("acc-1").kpiTypeId(-1).build())
                .requestParams(Map.of("searchTerm", "", "type", "CUSTOM", "subType", "WORKLOAD", Constants.ACCOUNT_IDENTIFIER, "acc-1"))
                .metadata(new HashMap<>())
                .pageable(PageRequest.of(0, 10))
                .build();
        Page<CategoryDetails> result = getCategoriesBL.process(utilityBean);
        assertEquals(1, result.getContent().size());
        assertEquals("INFO", result.getContent().get(0).getSubType());
    }

    /**
     * Tests process method for subType NON_INFO.
     */
    @Test
    void testProcess_subTypeNonInfo() throws Exception {
        CategoryDetailBean bean = CategoryDetailBean.builder()
                .id(3)
                .name("cat3")
                .identifier("cat-3")
                .isCustom(1)
                .isWorkLoad(0)
                .isInformative(0)
                .status(1)
                .description("desc")
                .build();
        List<CategoryDetailBean> beans = List.of(bean);
        when(categoryDao.getCategoriesForAccountWithFilters(anyInt(), anyString(), anyString(), anyString(), any())).thenReturn(beans);
        when(categoryDao.countCategoriesForAccountWithFilters(anyInt(), anyString(), anyString(), anyString())).thenReturn(1);
        when(categoryDao.getKpiCountForCategories(anyList())).thenReturn(Collections.emptyList());
        UtilityBean<AccountKPIKey> utilityBean = UtilityBean.<AccountKPIKey>builder()
                .pojoObject(AccountKPIKey.builder().accountId(1).accountIdentifier("acc-1").kpiTypeId(-1).build())
                .requestParams(Map.of("searchTerm", "", "type", "CUSTOM", "subType", "WORKLOAD", Constants.ACCOUNT_IDENTIFIER, "acc-1"))
                .metadata(new HashMap<>())
                .pageable(PageRequest.of(0, 10))
                .build();
        Page<CategoryDetails> result = getCategoriesBL.process(utilityBean);
        assertEquals(1, result.getContent().size());
        assertEquals("NON_INFO", result.getContent().get(0).getSubType());
    }
}
