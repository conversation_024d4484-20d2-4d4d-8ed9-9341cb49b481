package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.ProducerValidationBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetProducersBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.GetProducerPojo;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.HealthMetrics;
import com.heal.controlcenter.util.JsonFileParser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Collections;
import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

class ProducersControllerTest {

    @Mock
    private GetProducersBL getProducersBL;
    @Mock
    private HealthMetrics healthMetrics;
    @Mock
    private JsonFileParser headersParser;

    @InjectMocks
    private ProducersController controller;

    private BasicUserDetails userDetails;
    private UtilityBean<String> clientBean;
    private UtilityBean<ProducerValidationBean> serverBean;
    private Page<GetProducerPojo> page;
    private Pageable pageable;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier("user1");

        clientBean = UtilityBean.<String>builder().metadata(new HashMap<>()).build();
        serverBean = UtilityBean.<ProducerValidationBean>builder().build();
        pageable = PageRequest.of(0, 10);
        page = new PageImpl<>(Collections.emptyList(), pageable, 0);
        when(headersParser.loadHeaderConfiguration()).thenReturn(null);
    }

    @Test
    void getProducers_Success() throws DataProcessingException, ServerException, ClientException {
        when(getProducersBL.clientValidation(any(), any(), any(), any(), any(), any())).thenReturn(clientBean);
        when(getProducersBL.serverValidation(clientBean)).thenReturn(serverBean);
        when(getProducersBL.process(serverBean)).thenReturn(page);

        ResponseEntity<ResponsePojo<Page<GetProducerPojo>>> response = controller.getProducers(
                "acc", null, null, null, 1, pageable, userDetails);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("Producers fetching successful.", response.getBody().getMessage());
        assertEquals(page, response.getBody().getData());
        assertEquals("user1", clientBean.getMetadata().get(Constants.USER_ID_KEY));
    }

    @Test
    void getProducers_ClientException() throws ClientException {
        when(getProducersBL.clientValidation(any(), any(), any(), any(), any(), any()))
                .thenThrow(new ClientException("Client error"));

        ResponseEntity<ResponsePojo<Page<GetProducerPojo>>> response = controller.getProducers(
                "acc", null, null, null, 1, pageable, userDetails);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("ClientException : Client error", response.getBody().getMessage());
    }

    @Test
    void getProducers_ServerException() throws ServerException, ClientException {
        when(getProducersBL.clientValidation(any(), any(), any(), any(), any(), any())).thenReturn(clientBean);
        when(getProducersBL.serverValidation(clientBean)).thenThrow(new ServerException("Server error"));

        ResponseEntity<ResponsePojo<Page<GetProducerPojo>>> response = controller.getProducers(
                 "acc", null, null, null, 1, pageable, userDetails);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("ServerException : Server error", response.getBody().getMessage());
    }

    @Test
    void getProducers_DataProcessingException() throws DataProcessingException, ServerException, ClientException {
        when(getProducersBL.clientValidation(any(), any(), any(), any(), any(), any())).thenReturn(clientBean);
        when(getProducersBL.serverValidation(clientBean)).thenReturn(serverBean);
        when(getProducersBL.process(serverBean)).thenThrow(new DataProcessingException("Data processing error"));

        ResponseEntity<ResponsePojo<Page<GetProducerPojo>>> response = controller.getProducers(
                 "acc", null, null, null, 1, pageable, userDetails);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("DataProcessingException : Data processing error", response.getBody().getMessage());
    }
}
