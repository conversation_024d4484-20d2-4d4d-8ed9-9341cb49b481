package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetForensicActionsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.ForensicActionsPojo;
import com.heal.controlcenter.util.HealthMetrics;
import com.heal.controlcenter.util.JsonFileParser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

class ForensicActionsControllerTest {

    @InjectMocks
    private ForensicActionsController forensicActionsController;

    @Mock
    private GetForensicActionsBL getForensicActionsBL;

    @Mock
    private JsonFileParser headersParser;

    @Mock
    private HealthMetrics healthMetrics;

    private BasicUserDetails userDetails;
    private Pageable pageable;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier("test-user");
        pageable = PageRequest.of(0, 10);
    }

    @Test
    @DisplayName("Should return forensic actions successfully for valid input")
    void getForensicActions_success() throws Exception {
        String accountIdentifier = "account-1";
        UtilityBean<Object> utilityBean = UtilityBean.builder().pojoObject(new Object()).build();
        utilityBean.setMetadata(new HashMap<>()); // Ensure metadata is not null
        when(getForensicActionsBL.clientValidation(any(), any(), any(), any(), any(), any(), any(), any(), any())).thenReturn(utilityBean);

        UtilityBean<Integer> accountIdBean = UtilityBean.<Integer>builder().pojoObject(1).build();
        when(getForensicActionsBL.serverValidation(any())).thenReturn(accountIdBean);

        List<ForensicActionsPojo> forensicActions = Collections.singletonList(new ForensicActionsPojo());
        Page<ForensicActionsPojo> page = new PageImpl<>(forensicActions, pageable, 1);
        when(getForensicActionsBL.process(any())).thenReturn(page);

        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders());

        ResponseEntity<?> response = forensicActionsController.getForensicActions(accountIdentifier, pageable, null, null, null, null, null, null, null, userDetails);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
    }

    @Test
    @DisplayName("Should throw ServerException for server validation error")
    void getForensicActions_serverException() throws Exception {
        String accountIdentifier = "account-1";
        UtilityBean<Object> utilityBean = UtilityBean.builder().pojoObject(new Object()).build();
        utilityBean.setMetadata(new HashMap<>());
        when(getForensicActionsBL.clientValidation(any(), any(), any(), any(), any(), any(), any(), any())).thenReturn(utilityBean);
        when(getForensicActionsBL.serverValidation(any())).thenThrow(new ServerException("Server error"));

        assertThrows(Exception.class, () -> {
            forensicActionsController.getForensicActions(accountIdentifier, pageable, null, null, null, null, null, null, null,userDetails);
        });
    }

    @Test
    @DisplayName("Should throw ClientException for client validation error")
    void getForensicActions_clientException() throws Exception {
        String accountIdentifier = "account-1";
        when(getForensicActionsBL.clientValidation(any(), any(), any(), any(), any(), any(), any(), any()))
                .thenThrow(new ClientException("Client error"));

        assertThrows(Exception.class, () -> {
            forensicActionsController.getForensicActions(accountIdentifier, pageable, null, null, null, null, null, null,null, userDetails);
        });
    }

    @Test
    @DisplayName("Should throw generic Exception for unexpected errors")
    void getForensicActions_genericException() throws Exception {
        String accountIdentifier = "account-1";
        when(getForensicActionsBL.clientValidation(any(), any(), any(), any(), any(), any(), any(), any()))
                .thenThrow(new RuntimeException("Generic error"));

        assertThrows(Exception.class, () -> {
            forensicActionsController.getForensicActions(accountIdentifier, pageable, null, null, null, null, null, null, null ,userDetails);
        });
    }

    @Test
    @DisplayName("Should return empty page when no forensic actions found")
    void getForensicActions_emptyResult() throws Exception {
        String accountIdentifier = "account-1";
        UtilityBean<Object> utilityBean = UtilityBean.builder().pojoObject(new Object()).build();
        utilityBean.setMetadata(new HashMap<>()); // Ensure metadata is not null
        when(getForensicActionsBL.clientValidation(any(), any(), any(), any(), any(), any(), any(), any(), any())).thenReturn(utilityBean);
        UtilityBean<Integer> accountIdBean = UtilityBean.<Integer>builder().pojoObject(1).build();
        accountIdBean.setMetadata(new HashMap<>()); // Ensure metadata is not null
        when(getForensicActionsBL.serverValidation(any())).thenReturn(accountIdBean);
        Page<ForensicActionsPojo> page = new PageImpl<>(Collections.emptyList(), pageable, 0);
        when(getForensicActionsBL.process(any())).thenReturn(page);
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders());
        ResponseEntity<?> response = forensicActionsController.getForensicActions(accountIdentifier, pageable, null, null, null, null, null, null, null, userDetails);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
    }

    @Test
    @DisplayName("Should handle null categoryList gracefully")
    void getForensicActions_nullCategoryList() throws Exception {
        String accountIdentifier = "account-1";
        UtilityBean<Object> utilityBean = UtilityBean.builder().pojoObject(new Object()).build();
        utilityBean.setMetadata(new HashMap<>()); // Ensure metadata is not null
        when(getForensicActionsBL.clientValidation(any(), any(), any(), any(), any(), any(), any(), any(), any())).thenReturn(utilityBean);
        UtilityBean<Integer> accountIdBean = UtilityBean.<Integer>builder().pojoObject(1).build();
        accountIdBean.setMetadata(new HashMap<>()); // Ensure metadata is not null
        when(getForensicActionsBL.serverValidation(any())).thenReturn(accountIdBean);
        List<ForensicActionsPojo> forensicActions = Collections.singletonList(new ForensicActionsPojo());
        Page<ForensicActionsPojo> page = new PageImpl<>(forensicActions, pageable, 1);
        when(getForensicActionsBL.process(any())).thenReturn(page);
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders());
        ResponseEntity<?> response = forensicActionsController.getForensicActions(accountIdentifier, pageable, null, null, null, null, null, null, null, userDetails);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
    }

    @Test
    @DisplayName("Should log and throw Exception for DataProcessingException in getForensicActions")
    void getForensicActions_dataProcessingException() throws Exception {
        String accountIdentifier = "account-1";
        UtilityBean<Object> utilityBean = UtilityBean.builder().pojoObject(new Object()).build();
        utilityBean.setMetadata(new HashMap<>());
        when(getForensicActionsBL.clientValidation(any(), any(), any(), any(), any(), any(), any(), any(), any())).thenReturn(utilityBean);
        UtilityBean<Integer> accountIdBean = UtilityBean.<Integer>builder().pojoObject(1).build();
        accountIdBean.setMetadata(new HashMap<>());
        when(getForensicActionsBL.serverValidation(any())).thenReturn(accountIdBean);
        when(getForensicActionsBL.process(any())).thenThrow(new DataProcessingException("Data processing error"));
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders());
        Exception ex = assertThrows(Exception.class, () ->
            forensicActionsController.getForensicActions(accountIdentifier, pageable, null, null, null, null, null, null, null, userDetails)
        );
        assertTrue(ex.getCause() instanceof DataProcessingException);
    }

    @Test
    @DisplayName("Should log and throw Exception for ServerException in getForensicActions")
    void getForensicActions_serverExceptionCoverage() throws Exception {
        String accountIdentifier = "account-1";
        UtilityBean<Object> utilityBean = UtilityBean.builder().pojoObject(new Object()).build();
        utilityBean.setMetadata(new HashMap<>());
        when(getForensicActionsBL.clientValidation(any(), any(), any(), any(), any(), any(), any(), any(), any())).thenReturn(utilityBean);
        when(getForensicActionsBL.serverValidation(any())).thenThrow(new ServerException("Server validation error"));
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders());
        Exception ex = assertThrows(Exception.class, () ->
            forensicActionsController.getForensicActions(accountIdentifier, pageable, null, null, null, null, null, null, null, userDetails)
        );
        assertTrue(ex.getCause() instanceof ServerException);
    }

    @Test
    @DisplayName("Should log and throw Exception for ClientException in getForensicActions")
    void getForensicActions_clientExceptionCoverage() throws Exception {
        String accountIdentifier = "account-1";
        when(getForensicActionsBL.clientValidation(any(), any(), any(), any(), any(), any(), any(), any(), any())).thenThrow(new ClientException("Client validation error"));
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders());
        Exception ex = assertThrows(Exception.class, () ->
            forensicActionsController.getForensicActions(accountIdentifier, pageable, null, null, null, null, null, null, null, userDetails)
        );
        assertTrue(ex.getCause() instanceof ClientException);
    }
}
