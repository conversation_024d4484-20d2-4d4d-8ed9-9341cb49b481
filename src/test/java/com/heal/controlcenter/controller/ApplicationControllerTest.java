package com.heal.controlcenter.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.controlcenter.beans.ApplicationBean;
import com.heal.controlcenter.beans.ControllerBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.PostApplicationsBL;
import com.heal.controlcenter.businesslogic.DeleteApplicationsBL;
import com.heal.controlcenter.businesslogic.GetApplicationsBL;
import com.heal.controlcenter.pojo.*;
import com.heal.controlcenter.util.JsonFileParser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
class ApplicationControllerTest {

    @InjectMocks
    private ApplicationController applicationController;

    @Mock
    private GetApplicationsBL getApplicationsBL;
    @Mock
    private PostApplicationsBL postApplicationsBL;
    @Mock
    private DeleteApplicationsBL deleteApplicationsBL;
    @Mock
    private JsonFileParser jsonFileParser;

    private MockMvc mockMvc;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private static final String USER_ID = "test-user";
    private static final String ACCOUNT_IDENTIFIER = "account-001";

    @BeforeEach
    void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(applicationController).build();
    }

    private BasicUserDetails createTestUserDetails() {
        BasicUserDetails userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier(USER_ID);
        return userDetails;
    }

    @Test
    void getApplications_success() throws Exception {
        Pageable pageable = PageRequest.of(0, 10);
        Page<ApplicationBean> applicationPage = new PageImpl<>(Collections.singletonList(new ApplicationBean()));
        UtilityBean<String> clientValidationBean = UtilityBean.<String>builder().metadata(new HashMap<>()).build();
        UtilityBean<com.heal.configuration.pojos.Account> serverValidationBean = UtilityBean.<com.heal.configuration.pojos.Account>builder().build();

        when(getApplicationsBL.clientValidation(any(), anyString(), anyString(), anyString())).thenReturn(clientValidationBean);
        when(getApplicationsBL.serverValidation(any(UtilityBean.class))).thenReturn(serverValidationBean);
        when(getApplicationsBL.process(serverValidationBean)).thenReturn((Page)applicationPage);
        when(jsonFileParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders());

        ResponseEntity<ResponsePojo<Page<ApplicationPojo>>> response = applicationController.getApplications(
                ACCOUNT_IDENTIFIER, "true", "", pageable, createTestUserDetails());

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("Applications fetched successfully", response.getBody().getMessage());
        assertEquals(applicationPage, response.getBody().getData());
    }

    @Test
    void addApplications_success() throws Exception {
        List<Application> applications = Collections.singletonList(Application.builder().name("TestApp").build());
        UtilityBean<List<Application>> clientValidationBean = UtilityBean.<List<Application>>builder().metadata(new HashMap<>()).build();
        UtilityBean<List<ApplicationBean>> serverValidationBean = UtilityBean.<List<ApplicationBean>>builder().build();
        List<IdPojo> idPojoList = Collections.singletonList(IdPojo.builder().id(1).name("TestApp").identifier("test-app").build());

        when(postApplicationsBL.clientValidation(anyList(), anyString())).thenReturn(clientValidationBean);
        when(postApplicationsBL.serverValidation(any(UtilityBean.class))).thenReturn(serverValidationBean);
        when(postApplicationsBL.process(any(UtilityBean.class))).thenReturn(idPojoList);
        when(jsonFileParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders());

        ResponseEntity<ResponsePojo<List<IdPojo>>> response = applicationController.addApplications(
                ACCOUNT_IDENTIFIER, applications, createTestUserDetails());

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("Applications added successfully", response.getBody().getMessage());
        assertEquals(idPojoList, response.getBody().getData());
    }

    @Test
    void addApplications_postRequest() throws Exception {
        List<Application> applications = Collections.singletonList(Application.builder().name("TestApp").build());
        UtilityBean<List<Application>> clientValidationBean = UtilityBean.<List<Application>>builder().metadata(new HashMap<>()).build();
        UtilityBean<List<ApplicationBean>> serverValidationBean = UtilityBean.<List<ApplicationBean>>builder().build();
        List<IdPojo> idPojoList = Collections.singletonList(IdPojo.builder().id(1).name("TestApp").identifier("test-app").build());

        when(postApplicationsBL.clientValidation(anyList(), anyString())).thenReturn(clientValidationBean);
        when(postApplicationsBL.serverValidation(any(UtilityBean.class))).thenReturn(serverValidationBean);
        when(postApplicationsBL.process(any(UtilityBean.class))).thenReturn(idPojoList);
        when(jsonFileParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders());

        mockMvc.perform(post("/accounts/" + ACCOUNT_IDENTIFIER + "/applications")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(applications))
                        .with(request -> {
                            request.setAttribute("basicUserDetails", createTestUserDetails());
                            return request;
                        }))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Applications added successfully"));
    }
    @Test
    void deleteApplications_success() throws Exception {
        DeleteApplicationsPojo requestBody = DeleteApplicationsPojo.builder()
                .applicationIdentifiers(List.of("app-123"))
                .build();

        UtilityBean<DeleteApplicationsPojo> clientValidationBean =
                UtilityBean.<DeleteApplicationsPojo>builder()
                        .pojoObject(requestBody)
                        .metadata(new HashMap<>())
                        .build();

        UtilityBean<List<ControllerBean>> serverValidationBean =
                UtilityBean.<List<ControllerBean>>builder().build();

        when(deleteApplicationsBL.clientValidation(any(DeleteApplicationsPojo.class), eq(ACCOUNT_IDENTIFIER)))
                .thenReturn(clientValidationBean);
        when(deleteApplicationsBL.serverValidation(any(UtilityBean.class)))
                .thenReturn(serverValidationBean);
        when(jsonFileParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders());

        mockMvc.perform(post("/accounts/" + ACCOUNT_IDENTIFIER + "/applications") // use delete() below
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestBody))
                        .with(request -> {
                            request.setAttribute("basicUserDetails", createTestUserDetails());
                            return request;
                        })
                        .with(request -> {
                            request.setMethod("DELETE"); // override POST to DELETE since mockMvc doesn't support .delete with body
                            return request;
                        }))
                .andExpect(status().isOk());
    }
}

