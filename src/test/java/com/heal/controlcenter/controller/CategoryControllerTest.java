package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.CategoryDetailBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.PostCategoriesBL;
import com.heal.controlcenter.businesslogic.DeleteCategoriesBL;
import com.heal.controlcenter.businesslogic.GetCategoriesBL;
import com.heal.controlcenter.businesslogic.PutCategoriesBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.*;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.JsonFileParser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CategoryControllerTest {
    @Mock
    PostCategoriesBL postCategoriesBL;
    @Mock
    PutCategoriesBL putCategoriesBL;
    @Mock
    JsonFileParser headersParser;
    @Mock
    DeleteCategoriesBL deleteCategoriesBL;
    @Mock
    GetCategoriesBL getCategoriesBL;

    @InjectMocks
    CategoryController categoryController;

    private BasicUserDetails userDetails;
    private String accountIdentifier = "acc-1";
    private List<CategoryDetails> categoryDetailsList;
    private List<CategoryDetailBean> categoryDetailBeans;
    private List<IdPojo> idPojoList;

    @BeforeEach
    void setup() {
        userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier("user-1");
        CategoryDetails details = CategoryDetails.builder()
                .name("TestCategory")
                .identifier("test-category")
                .description("desc")
                .type("CUSTOM")
                .subType("Workload")
                .build();
        categoryDetailsList = Collections.singletonList(details);
        CategoryDetailBean bean = CategoryDetailBean.builder()
                .name("TestCategory")
                .identifier("test-category")
                .accountId(10)
                .description("desc")
                .userDetailsId("user-1")
                .createdTime("2025-07-23 10:00:00")
                .updatedTime("2025-07-23 10:00:00")
                .status(1)
                .isCustom(1)
                .isInformative(0)
                .isWorkLoad(1)
                .accountIdentifier(accountIdentifier)
                .build();
        categoryDetailBeans = Collections.singletonList(bean);
        IdPojo idPojo = IdPojo.builder().id(123).name("TestCategory").identifier("test-category").build();
        idPojoList = Collections.singletonList(idPojo);
    }

    /**
     * Tests successful addition of categories via the controller.
     * Ensures a valid request returns a 200 OK response with created IDs.
     */
    @Test
    void testAddCategories_success() throws Exception {
        UtilityBean<List<CategoryDetails>> clientValidated = UtilityBean.<List<CategoryDetails>>builder()
                .pojoObject(categoryDetailsList)
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .metadata(new HashMap<>())
                .build();
        UtilityBean<List<CategoryDetailBean>> serverValidated = UtilityBean.<List<CategoryDetailBean>>builder()
                .pojoObject(categoryDetailBeans)
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .metadata(new HashMap<>())
                .build();
        when(postCategoriesBL.clientValidation(anyList(), anyString())).thenReturn(clientValidated);
        when(postCategoriesBL.serverValidation(any())).thenReturn(serverValidated);
        when(postCategoriesBL.process(any())).thenReturn(idPojoList);
        HttpHeaders headers = new HttpHeaders();
        when(headersParser.loadHeaderConfiguration()).thenReturn(headers);
        ResponseEntity<ResponsePojo<List<IdPojo>>> response = categoryController.addCategories(accountIdentifier, categoryDetailsList, userDetails);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("Categories added successfully", response.getBody().getMessage());
        assertEquals(idPojoList, response.getBody().getData());
    }

    /**
     * Tests controller handling of client validation errors.
     * Should return 400 Bad Request with error message for ClientException.
     */
    @Test
    void testAddCategories_clientException() throws Exception {
        when(postCategoriesBL.clientValidation(anyList(), anyString())).thenThrow(new ClientException("Client error"));
        ResponseEntity<ResponsePojo<List<IdPojo>>> response = categoryController.addCategories(accountIdentifier, categoryDetailsList, userDetails);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("ClientException : Client error", response.getBody().getMessage());
        assertNull(response.getBody().getData());
    }

    /**
     * Tests controller handling of server validation errors.
     * Should return 400 Bad Request with error message for ServerException.
     */
    @Test
    void testAddCategories_serverException() throws Exception {
        UtilityBean<List<CategoryDetails>> clientValidated = UtilityBean.<List<CategoryDetails>>builder()
                .pojoObject(categoryDetailsList)
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .metadata(new HashMap<>())
                .build();
        when(postCategoriesBL.clientValidation(anyList(), anyString())).thenReturn(clientValidated);
        when(postCategoriesBL.serverValidation(any())).thenThrow(new ServerException("Server error"));
        ResponseEntity<ResponsePojo<List<IdPojo>>> response = categoryController.addCategories(accountIdentifier, categoryDetailsList, userDetails);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("ServerException : Server error", response.getBody().getMessage());
        assertNull(response.getBody().getData());
    }

    /**
     * Tests controller handling of data processing errors.
     * Should return 400 Bad Request with error message for DataProcessingException.
     */
    @Test
    void testAddCategories_dataProcessingException() throws Exception {
        UtilityBean<List<CategoryDetails>> clientValidated = UtilityBean.<List<CategoryDetails>>builder()
                .pojoObject(categoryDetailsList)
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .metadata(new HashMap<>())
                .build();
        UtilityBean<List<CategoryDetailBean>> serverValidated = UtilityBean.<List<CategoryDetailBean>>builder()
                .pojoObject(categoryDetailBeans)
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .metadata(new HashMap<>())
                .build();
        when(postCategoriesBL.clientValidation(anyList(), anyString())).thenReturn(clientValidated);
        when(postCategoriesBL.serverValidation(any())).thenReturn(serverValidated);
        when(postCategoriesBL.process(any())).thenThrow(new DataProcessingException("Processing error"));
        ResponseEntity<ResponsePojo<List<IdPojo>>> response = categoryController.addCategories(accountIdentifier, categoryDetailsList, userDetails);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("DataProcessingException : Processing error", response.getBody().getMessage());
        assertNull(response.getBody().getData());
    }

    /**
     * Tests controller handling of unexpected exceptions.
     * Should return 500 Internal Server Error with error message.
     */
    @Test
    void testAddCategories_unexpectedException() throws Exception {
        when(postCategoriesBL.clientValidation(anyList(), anyString())).thenThrow(new RuntimeException("Unexpected error"));
        ResponseEntity<ResponsePojo<List<IdPojo>>> response = categoryController.addCategories(accountIdentifier, categoryDetailsList, userDetails);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("Unexpected error", response.getBody().getMessage());
        assertNull(response.getBody().getData());
    }

    /**
     * Tests successful update of categories via the controller.
     * Ensures a valid request returns a 200 OK response with updated IDs.
     */
    @Test
    void testUpdateCategories_success() throws Exception {
        List<CategoryDetails> categoryDetailsList = Collections.singletonList(CategoryDetails.builder()
                .name("TestCategory")
                .identifier("test-category")
                .description("desc")
                .type("CUSTOM")
                .subType("Workload")
                .status(1)
                .build());
        BasicUserDetails userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier("user-1");
        UtilityBean<List<CategoryDetails>> clientValidated = UtilityBean.<List<CategoryDetails>>builder()
                .pojoObject(categoryDetailsList)
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .metadata(new HashMap<>())
                .build();
        UtilityBean<List<CategoryDetailBean>> serverValidated = UtilityBean.<List<CategoryDetailBean>>builder()
                .pojoObject(Collections.singletonList(CategoryDetailBean.builder()
                        .name("TestCategory")
                        .identifier("test-category")
                        .accountId(10)
                        .description("desc")
                        .userDetailsId("user-1")
                        .createdTime("2025-07-23 10:00:00")
                        .updatedTime("2025-07-23 10:00:00")
                        .status(1)
                        .isCustom(1)
                        .isInformative(0)
                        .isWorkLoad(1)
                        .accountIdentifier(accountIdentifier)
                        .build()))
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .metadata(new HashMap<>())
                .build();
        List<IdPojo> idPojoList = Collections.singletonList(IdPojo.builder().id(123).name("TestCategory").identifier("test-category").build());
        when(putCategoriesBL.clientValidation(anyList(), anyString())).thenReturn(clientValidated);
        when(putCategoriesBL.serverValidation(any())).thenReturn(serverValidated);
        when(putCategoriesBL.process(any())).thenReturn(idPojoList);
        HttpHeaders headers = new HttpHeaders();
        when(headersParser.loadHeaderConfiguration()).thenReturn(headers);
        ResponseEntity<ResponsePojo<List<IdPojo>>> response = categoryController.updateCategories(accountIdentifier, categoryDetailsList, userDetails);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("Categories updated successfully", response.getBody().getMessage());
        assertEquals(idPojoList, response.getBody().getData());
    }

    /**
     * Tests controller handling of client validation errors for update.
     * Should return 400 Bad Request with error message for ClientException.
     */
    @Test
    void testUpdateCategories_clientException() throws Exception {
        BasicUserDetails userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier("user-1");
        List<CategoryDetails> categoryDetailsList = Collections.singletonList(CategoryDetails.builder().name("TestCategory").identifier("test-category").build());
        when(putCategoriesBL.clientValidation(anyList(), anyString())).thenThrow(new ClientException("Client error"));
        ResponseEntity<ResponsePojo<List<IdPojo>>> response = categoryController.updateCategories(accountIdentifier, categoryDetailsList, userDetails);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("ClientException : Client error", response.getBody().getMessage());
        assertNull(response.getBody().getData());
    }

    /**
     * Tests controller handling of server validation errors for update.
     * Should return 400 Bad Request with error message for ServerException.
     */
    @Test
    void testUpdateCategories_serverException() throws Exception {
        BasicUserDetails userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier("user-1");
        List<CategoryDetails> categoryDetailsList = Collections.singletonList(CategoryDetails.builder().name("TestCategory").identifier("test-category").build());
        UtilityBean<List<CategoryDetails>> clientValidated = UtilityBean.<List<CategoryDetails>>builder()
                .pojoObject(categoryDetailsList)
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .metadata(new HashMap<>())
                .build();
        when(putCategoriesBL.clientValidation(anyList(), anyString())).thenReturn(clientValidated);
        when(putCategoriesBL.serverValidation(any())).thenThrow(new ServerException("Server error"));
        ResponseEntity<ResponsePojo<List<IdPojo>>> response = categoryController.updateCategories(accountIdentifier, categoryDetailsList, userDetails);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("ServerException : Server error", response.getBody().getMessage());
        assertNull(response.getBody().getData());
    }

    /**
     * Tests controller handling of data processing errors for update.
     * Should return 400 Bad Request with error message for DataProcessingException.
     */
    @Test
    void testUpdateCategories_dataProcessingException() throws Exception {
        BasicUserDetails userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier("user-1");
        List<CategoryDetails> categoryDetailsList = Collections.singletonList(CategoryDetails.builder().name("TestCategory").identifier("test-category").build());
        UtilityBean<List<CategoryDetails>> clientValidated = UtilityBean.<List<CategoryDetails>>builder()
                .pojoObject(categoryDetailsList)
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .metadata(new HashMap<>())
                .build();
        UtilityBean<List<CategoryDetailBean>> serverValidated = UtilityBean.<List<CategoryDetailBean>>builder()
                .pojoObject(Collections.singletonList(CategoryDetailBean.builder().name("TestCategory").identifier("test-category").build()))
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .metadata(new HashMap<>())
                .build();
        when(putCategoriesBL.clientValidation(anyList(), anyString())).thenReturn(clientValidated);
        when(putCategoriesBL.serverValidation(any())).thenReturn(serverValidated);
        when(putCategoriesBL.process(any())).thenThrow(new DataProcessingException("Processing error"));
        ResponseEntity<ResponsePojo<List<IdPojo>>> response = categoryController.updateCategories(accountIdentifier, categoryDetailsList, userDetails);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("DataProcessingException : Processing error", response.getBody().getMessage());
        assertNull(response.getBody().getData());
    }

    /**
     * Tests controller handling of unexpected exceptions for update.
     * Should return 500 Internal Server Error with error message.
     */
    @Test
    void testUpdateCategories_unexpectedException() throws Exception {
        BasicUserDetails userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier("user-1");
        List<CategoryDetails> categoryDetailsList = Collections.singletonList(CategoryDetails.builder().name("TestCategory").identifier("test-category").build());
        when(putCategoriesBL.clientValidation(anyList(), anyString())).thenThrow(new RuntimeException("Unexpected error"));
        ResponseEntity<ResponsePojo<List<IdPojo>>> response = categoryController.updateCategories(accountIdentifier, categoryDetailsList, userDetails);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("Unexpected error", response.getBody().getMessage());
        assertNull(response.getBody().getData());
    }

    /**
     * Tests successful get categories via the controller.
     */
    @Test
    void testGetCategories_success() throws Exception {
        Pageable pageable = PageRequest.of(0, 10);
        Page<CategoryDetails> page = new PageImpl<>(categoryDetailsList, pageable, 1);
        UtilityBean<String> clientValidated = UtilityBean.<String>builder()
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .metadata(new HashMap<>())
                .build();
        UtilityBean<com.heal.controlcenter.beans.AccountKPIKey> serverValidated = UtilityBean.<com.heal.controlcenter.beans.AccountKPIKey>builder()
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .metadata(new HashMap<>())
                .build();
        when(getCategoriesBL.clientValidation(any(), anyString(), any(), any(), any(), any())).thenReturn(clientValidated);
        when(getCategoriesBL.serverValidation(any())).thenReturn(serverValidated);
        when(getCategoriesBL.process(any())).thenReturn(page);
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders());
        ResponseEntity<ResponsePojo<Page<CategoryDetails>>> response = categoryController.getCategories(accountIdentifier, null, null, null, null, pageable, userDetails);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("Fetched categories successfully", response.getBody().getMessage());
        assertEquals(page, response.getBody().getData());
    }

    /**
     * Tests controller handling of client validation errors for get categories.
     */
    @Test
    void testGetCategories_clientException() throws Exception {
        when(getCategoriesBL.clientValidation(any(), anyString(), any(), any(), any(), any())).thenThrow(new ClientException("Client error"));
        ResponseEntity<ResponsePojo<Page<CategoryDetails>>> response = categoryController.getCategories(accountIdentifier, null, null, null, null, PageRequest.of(0, 10), userDetails);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("ClientException : Client error", response.getBody().getMessage());
        assertNull(response.getBody().getData());
    }

    /**
     * Tests controller handling of server validation errors for get categories.
     */
    @Test
    void testGetCategories_serverException() throws Exception {
        UtilityBean<String> clientValidated = UtilityBean.<String>builder()
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .metadata(new HashMap<>())
                .build();
        when(getCategoriesBL.clientValidation(any(), anyString(), any(), any(), any(), any())).thenReturn(clientValidated);
        when(getCategoriesBL.serverValidation(any())).thenThrow(new ServerException("Server error"));
        ResponseEntity<ResponsePojo<Page<CategoryDetails>>> response = categoryController.getCategories(accountIdentifier, null, null, null, null, PageRequest.of(0, 10), userDetails);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("ServerException : Server error", response.getBody().getMessage());
        assertNull(response.getBody().getData());
    }

    /**
     * Tests controller handling of data processing errors for get categories.
     */
    @Test
    void testGetCategories_dataProcessingException() throws Exception {
        UtilityBean<String> clientValidated = UtilityBean.<String>builder()
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .metadata(new HashMap<>())
                .build();
        UtilityBean<com.heal.controlcenter.beans.AccountKPIKey> serverValidated = UtilityBean.<com.heal.controlcenter.beans.AccountKPIKey>builder()
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .metadata(new HashMap<>())
                .build();
        when(getCategoriesBL.clientValidation(any(), anyString(), any(), any(), any(), any())).thenReturn(clientValidated);
        when(getCategoriesBL.serverValidation(any())).thenReturn(serverValidated);
        when(getCategoriesBL.process(any())).thenThrow(new DataProcessingException("Processing error"));
        ResponseEntity<ResponsePojo<Page<CategoryDetails>>> response = categoryController.getCategories(accountIdentifier, null, null, null, null, PageRequest.of(0, 10), userDetails);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("DataProcessingException : Processing error", response.getBody().getMessage());
        assertNull(response.getBody().getData());
    }

    /**
     * Tests controller handling of unexpected exceptions for get categories.
     */
    @Test
    void testGetCategories_unexpectedException() throws Exception {
        when(getCategoriesBL.clientValidation(any(), anyString(), any(), any(), any(), any())).thenThrow(new RuntimeException("Unexpected error"));
        ResponseEntity<ResponsePojo<Page<CategoryDetails>>> response = categoryController.getCategories(accountIdentifier, null, null, null, null, PageRequest.of(0, 10), userDetails);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("Unexpected error", response.getBody().getMessage());
        assertNull(response.getBody().getData());
    }

    /**
     * Tests successful delete categories via the controller.
     */
    @Test
    void testDeleteCategories_success() throws Exception {
        DeleteCategoriesPojo requestBody = DeleteCategoriesPojo.builder().categoryIdentifiers(List.of("cat-1")).hardDelete(false).build();
        UtilityBean<DeleteCategoriesPojo> clientValidated = UtilityBean.<DeleteCategoriesPojo>builder()
                .pojoObject(requestBody)
                .metadata(new HashMap<>())
                .build();
        UtilityBean<List<Integer>> serverValidated = UtilityBean.<List<Integer>>builder().build();
        when(deleteCategoriesBL.clientValidation(any(DeleteCategoriesPojo.class), anyString())).thenReturn(clientValidated);
        when(deleteCategoriesBL.serverValidation(any())).thenReturn(serverValidated);
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders());
        when(deleteCategoriesBL.process(any())).thenReturn("Category(ies) deleted successfully.");
        ResponseEntity<?> response = categoryController.deleteCategories(accountIdentifier, requestBody, userDetails);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().toString().contains("Categories removed successfully"));
    }

    /**
     * Tests controller handling of client validation errors for delete categories.
     */
    @Test
    void testDeleteCategories_clientException() throws Exception {
        DeleteCategoriesPojo requestBody = DeleteCategoriesPojo.builder().categoryIdentifiers(List.of("cat-1")).hardDelete(false).build();
        when(deleteCategoriesBL.clientValidation(any(DeleteCategoriesPojo.class), anyString())).thenThrow(new ClientException("Client error"));
        ResponseEntity<?> response = categoryController.deleteCategories(accountIdentifier, requestBody, userDetails);
        assertEquals(HttpStatus.BAD_REQUEST, ((ResponseEntity<?>) response).getStatusCode());
        assertTrue(((ResponsePojo<?>) response.getBody()).getMessage().contains("Client error"));
    }

    /**
     * Tests controller handling of server validation errors for delete categories.
     */
    @Test
    void testDeleteCategories_serverException() throws Exception {
        DeleteCategoriesPojo requestBody = DeleteCategoriesPojo.builder().categoryIdentifiers(List.of("cat-1")).hardDelete(false).build();
        UtilityBean<DeleteCategoriesPojo> clientValidated = UtilityBean.<DeleteCategoriesPojo>builder()
                .pojoObject(requestBody)
                .metadata(new HashMap<>())
                .build();
        when(deleteCategoriesBL.clientValidation(any(DeleteCategoriesPojo.class), anyString())).thenReturn(clientValidated);
        when(deleteCategoriesBL.serverValidation(any())).thenThrow(new ServerException("Server error"));
        ResponseEntity<?> response = categoryController.deleteCategories(accountIdentifier, requestBody, userDetails);
        assertEquals(HttpStatus.BAD_REQUEST, ((ResponseEntity<?>) response).getStatusCode());
        assertTrue(((ResponsePojo<?>) response.getBody()).getMessage().contains("Server error"));
    }

    /**
     * Tests controller handling of data processing errors for delete categories.
     */
    @Test
    void testDeleteCategories_dataProcessingException() throws Exception {
        DeleteCategoriesPojo requestBody = DeleteCategoriesPojo.builder().categoryIdentifiers(List.of("cat-1")).hardDelete(false).build();
        UtilityBean<DeleteCategoriesPojo> clientValidated = UtilityBean.<DeleteCategoriesPojo>builder()
                .pojoObject(requestBody)
                .metadata(new HashMap<>())
                .build();
        UtilityBean<List<Integer>> serverValidated = UtilityBean.<List<Integer>>builder().build();
        when(deleteCategoriesBL.clientValidation(any(DeleteCategoriesPojo.class), anyString())).thenReturn(clientValidated);
        when(deleteCategoriesBL.serverValidation(any())).thenReturn(serverValidated);
        doThrow(new DataProcessingException("Processing error")).when(deleteCategoriesBL).process(any());
        ResponseEntity<?> response = categoryController.deleteCategories(accountIdentifier, requestBody, userDetails);
        assertEquals(HttpStatus.BAD_REQUEST, ((ResponseEntity<?>) response).getStatusCode());
        assertTrue(((ResponsePojo<?>) response.getBody()).getMessage().contains("Processing error"));
    }

    /**
     * Tests controller handling of unexpected exceptions for delete categories.
     */
    @Test
    void testDeleteCategories_unexpectedException() throws Exception {
        DeleteCategoriesPojo requestBody = DeleteCategoriesPojo.builder().categoryIdentifiers(List.of("cat-1")).hardDelete(false).build();
        when(deleteCategoriesBL.clientValidation(any(DeleteCategoriesPojo.class), anyString())).thenThrow(new RuntimeException("Unexpected error"));
        ResponseEntity<?> response = categoryController.deleteCategories(accountIdentifier, requestBody, userDetails);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, ((ResponseEntity<?>) response).getStatusCode());
        assertTrue(((ResponsePojo<?>) response.getBody()).getMessage().contains("Unexpected error"));
    }
}
