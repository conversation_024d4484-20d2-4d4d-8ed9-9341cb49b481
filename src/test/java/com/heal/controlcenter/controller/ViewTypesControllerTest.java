package com.heal.controlcenter.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.controlcenter.businesslogic.GetViewTypesByNameBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.pojo.ViewTypeResponse;
import com.heal.controlcenter.util.JsonFileParser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.List;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
class ViewTypesControllerTest {

    @InjectMocks
    private ViewTypesController viewTypesController;

    @Mock
    private GetViewTypesByNameBL getViewTypesByNameBL;
    @Mock
    private JsonFileParser jsonFileParser;

    private MockMvc mockMvc;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private static final String USER_ID = "test-user";
    private static final String TYPE_NAME = "Environment";

    @BeforeEach
    void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(viewTypesController).build();
    }

    private BasicUserDetails createTestUserDetails() {
        BasicUserDetails userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier(USER_ID);
        return userDetails;
    }

    /**
     * Tests the successful scenario for fetching view types by type name via the controller.
     * <p>
     * Steps performed:
     * <ol>
     *   <li>Mocks the business logic and header parser dependencies.</li>
     *   <li>Performs a GET request to the /view-types endpoint with a valid typeName and user details.</li>
     *   <li>Verifies that the response status is OK and the returned data matches the expected values.</li>
     * </ol>
     */
    @Test
    void getViewTypesByTypeName_success() throws Exception {
        List<ViewTypeResponse> viewTypeResponses = List.of(new ViewTypeResponse(1, "SubType1"));
        ResponsePojo<List<ViewTypeResponse>> responsePojo = new ResponsePojo<>(
                String.format("ViewTypes fetched successfully for typeName '%s'.", TYPE_NAME),
                viewTypeResponses,
                HttpStatus.OK
        );

        // Use proper matchers for all arguments in stubbing
        com.heal.controlcenter.beans.UtilityBean<String> mockUtilityBean = org.mockito.Mockito.mock(com.heal.controlcenter.beans.UtilityBean.class);
        when(getViewTypesByNameBL.clientValidation(eq(TYPE_NAME))).thenReturn(mockUtilityBean);
        when(getViewTypesByNameBL.serverValidation(eq(mockUtilityBean))).thenReturn(mockUtilityBean);
        when(getViewTypesByNameBL.process(eq(mockUtilityBean))).thenReturn(viewTypeResponses);
        when(jsonFileParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders());

        mockMvc.perform(get("/view-types")
                        .param("typeName", TYPE_NAME)
                        .contentType(MediaType.APPLICATION_JSON)
                        .requestAttr("basicUserDetails", createTestUserDetails()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value(String.format("ViewTypes fetched successfully for typeName '%s'.", TYPE_NAME)))
                .andExpect(jsonPath("$.data[0].id").value(1))
                .andExpect(jsonPath("$.data[0].name").value("SubType1"));
    }

    /**
     * Tests the error scenario for fetching view types by type name via the controller.
     * <p>
     * Steps performed:
     * <ol>
     *   <li>Mocks the business logic to throw an exception during client validation.</li>
     *   <li>Performs a GET request to the /view-types endpoint with a valid typeName and user details.</li>
     *   <li>Verifies that the response status is INTERNAL_SERVER_ERROR and the error message is returned.</li>
     * </ol>
     */
    @Test
    void getViewTypesByTypeName_error() throws Exception {
        when(getViewTypesByNameBL.clientValidation(TYPE_NAME)).thenThrow(new ClientException("Some error"));
        when(jsonFileParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders());

        mockMvc.perform(get("/view-types")
                        .param("typeName", TYPE_NAME)
                        .contentType(MediaType.APPLICATION_JSON)
                        .requestAttr("basicUserDetails", createTestUserDetails()))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.message").value("Some error"));
    }
}
