package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetEmailConfigurationsBL;
import com.heal.controlcenter.businesslogic.PutEmailConfigurationsBL;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.SMTPDetailsPojo;
import com.heal.controlcenter.util.JsonFileParser;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(EmailConfigurationsController.class)
class EmailConfigurationsControllerIT {

    @Autowired
    private MockMvc mockMvc;
    @MockBean
    GetEmailConfigurationsBL getEmailConfigurationsBL;
    @MockBean
    PutEmailConfigurationsBL putEmailConfigurationsBL;
    @MockBean
    JsonFileParser headersParser;

    String[] requestParams = new String[2];
    UtilityBean<Object> getUtilityBean;
    UtilityBean<SMTPDetailsPojo> putUtilityBean;

    SMTPDetailsPojo smtpDetailsPojo = new SMTPDetailsPojo();

    @BeforeEach
    void setUp() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "mockAccountIdentifier";

        getUtilityBean = UtilityBean.builder()
                .requestParams(Map.of(
                        "Authorization", "mockAuthToken",
                        "accountIdentifier", "mockAccountIdentifier"
                ))
                .metadata(Map.of(
                        "userId", "mockUserId"
                ))
                .build();

        putUtilityBean = UtilityBean.<SMTPDetailsPojo>builder()
                .requestParams(Map.of(
                        "Authorization", "mockAuthToken",
                        "accountIdentifier", "mockAccountIdentifier"
                ))
                .metadata(Map.of(
                        "userId", "mockUserId"
                ))
                .pojoObject(smtpDetailsPojo)
                .build();
    }

    @AfterEach
    void tearDown() {
    }

    @Test
    void getEmailConfigurations_WhenSuccess() throws Exception {
        when(getEmailConfigurationsBL.clientValidation(null, requestParams)).thenReturn(getUtilityBean);
        when(getEmailConfigurationsBL.serverValidation(getUtilityBean)).thenReturn(anyInt());
        when(getEmailConfigurationsBL.process(1)).thenReturn(smtpDetailsPojo);
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders() {{
            set("authorization", "mockAuthorization");
        }});
        MockHttpServletResponse mockHttpServletResponse = mockMvc.perform(get("/accounts/{identifier}/email-configurations", "mockIdentifier")
                        .header("authorization", "mockAuthorization"))
                .andExpect(status().isOk())
                .andExpect(header().string("authorization", "mockAuthorization"))
                .andExpect(jsonPath("$.responseStatus", Matchers.equalTo("OK")))
                .andExpect(jsonPath("$.message", Matchers.equalTo("Email configurations fetched successfully.")))
                .andReturn()
                .getResponse();
        assertEquals(mockHttpServletResponse.getStatus(), HttpStatus.OK.value());
    }

    @Test
    void getEmailConfigurations_WhenServerException() throws Exception {
        getUtilityBean = null;
        when(getEmailConfigurationsBL.clientValidation(null, requestParams)).thenReturn(getUtilityBean);
        when(getEmailConfigurationsBL.serverValidation(getUtilityBean)).thenThrow(ServerException.class);
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders() {{
            set("authorization", "mockAuthorization");
        }});
        MockHttpServletResponse mockHttpServletResponse = mockMvc.perform(get("/accounts/{identifier}/email-configurations", "mockIdentifier")
                        .header("authorization", "mockAuthorization"))
                .andExpect(status().isBadRequest())
                .andExpect(header().string("authorization", "mockAuthorization"))
                .andExpect(jsonPath("$.responseStatus", Matchers.equalTo("BAD_REQUEST")))
                .andExpect(jsonPath("$.data", Matchers.aMapWithSize(6)))
                .andExpect(jsonPath("$.data", Matchers.hasKey("status")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("type")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("path")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("error")))
                .andReturn()
                .getResponse();
        assertEquals(mockHttpServletResponse.getStatus(), HttpStatus.BAD_REQUEST.value());
    }

    @Test
    void putEmailConfigurations_WhenSuccess() throws Exception {
        when(putEmailConfigurationsBL.clientValidation(smtpDetailsPojo, requestParams)).thenReturn(putUtilityBean);
        when(putEmailConfigurationsBL.serverValidation(putUtilityBean)).thenReturn(putUtilityBean);
        when(putEmailConfigurationsBL.process(putUtilityBean)).thenReturn(null);
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders() {{
            set("authorization", "mockAuthorization");
        }});
        MockHttpServletResponse mockHttpServletResponse = mockMvc.perform(put("/accounts/{identifier}/email-configurations", "mockIdentifier")
                        .header("authorization", "mockAuthorization")
                        .content("{}")
                        .accept(MediaType.APPLICATION_JSON)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(header().string("authorization", "mockAuthorization"))
                .andExpect(jsonPath("$.responseStatus", Matchers.equalTo("OK")))
                .andExpect(jsonPath("$.message", Matchers.equalTo("Email configurations updated successfully.")))
                .andReturn()
                .getResponse();
        assertEquals(mockHttpServletResponse.getStatus(), HttpStatus.OK.value());
    }

    @Test
    void putEmailConfigurations_WhenServerException() throws Exception {
        putUtilityBean = null;
        when(putEmailConfigurationsBL.clientValidation(smtpDetailsPojo, requestParams)).thenReturn(putUtilityBean);
        when(putEmailConfigurationsBL.serverValidation(putUtilityBean)).thenThrow(ServerException.class);
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders() {{
            set("authorization", "mockAuthorization");
        }});
        MockHttpServletResponse mockHttpServletResponse = mockMvc.perform(put("/accounts/{identifier}/email-configurations", "mockIdentifier")
                        .header("authorization", "mockAuthorization")
                        .content("{}")
                        .accept(MediaType.APPLICATION_JSON)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(header().string("authorization", "mockAuthorization"))
                .andExpect(jsonPath("$.responseStatus", Matchers.equalTo("BAD_REQUEST")))
                .andExpect(jsonPath("$.data", Matchers.aMapWithSize(6)))
                .andExpect(jsonPath("$.data", Matchers.hasKey("status")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("type")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("path")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("error")))
                .andReturn()
                .getResponse();
        assertEquals(mockHttpServletResponse.getStatus(), HttpStatus.BAD_REQUEST.value());
    }
}
