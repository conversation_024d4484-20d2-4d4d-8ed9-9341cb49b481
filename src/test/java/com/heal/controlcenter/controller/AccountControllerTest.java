package com.heal.controlcenter.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.controlcenter.beans.AccessDetailsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.PostAccountBL;
import com.heal.controlcenter.businesslogic.GetAccountsBL;
import com.heal.controlcenter.pojo.Account;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.util.JsonFileParser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Collections;
import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
class AccountControllerTest {

    @InjectMocks
    private AccountController accountController;

    @Mock
    private GetAccountsBL getAccountsBL;
    @Mock
    private PostAccountBL postAccountBL;
    @Mock
    private JsonFileParser jsonFileParser;

    private MockMvc mockMvc;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private static final String USER_ID = "test-user";

    @BeforeEach
    void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(accountController).build();
    }

    private BasicUserDetails createTestUserDetails() {
        BasicUserDetails userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier(USER_ID);
        return userDetails;
    }

    @Test
    void getAccounts_success() throws Exception {
        String searchTerm = "test";
        Pageable pageable = PageRequest.of(0, 10);
        Page<Account> accountPage = new PageImpl<>(Collections.singletonList(new Account()));
        UtilityBean<String> clientValidationBean = UtilityBean.<String>builder().metadata(new HashMap<>()).build();
        UtilityBean<AccessDetailsBean> serverValidationBean = UtilityBean.<AccessDetailsBean>builder().build();

        when(getAccountsBL.clientValidation(null, searchTerm)).thenReturn(clientValidationBean);
        when(getAccountsBL.serverValidation(any(UtilityBean.class))).thenReturn(serverValidationBean);
        when(getAccountsBL.process(serverValidationBean)).thenReturn(accountPage);
        when(jsonFileParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders());

        ResponseEntity<ResponsePojo<Page<Account>>> response = accountController.getAllAccounts(pageable, searchTerm, createTestUserDetails());

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("Accounts fetched successfully.", response.getBody().getMessage());
        assertEquals(accountPage, response.getBody().getData());
    }

    @Test
    void createAccount_success() throws Exception {
        Account account = new Account();
        account.setIdentifier("test-account");
        UtilityBean<Account> clientValidationBean = UtilityBean.<Account>builder().metadata(new HashMap<>()).build();
        UtilityBean<Account> serverValidationBean = UtilityBean.<Account>builder().build();

        when(postAccountBL.clientValidation(account, "test-account")).thenReturn(clientValidationBean);
        when(postAccountBL.serverValidation(any(UtilityBean.class))).thenReturn(serverValidationBean);
        when(postAccountBL.process(serverValidationBean)).thenReturn(account);
        when(jsonFileParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders());

        ResponseEntity<ResponsePojo<Account>> response = accountController.createAccount(account, createTestUserDetails());

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("Account created successfully .", response.getBody().getMessage());
    }

    @Test
    void createAccount_postRequest() throws Exception {
        Account account = new Account();
        account.setIdentifier("test-account");

        when(postAccountBL.clientValidation(any(Account.class), any(String.class)))
                .thenReturn(UtilityBean.<Account>builder().metadata(new HashMap<>()).build());
        when(postAccountBL.serverValidation(any(UtilityBean.class)))
                .thenReturn(UtilityBean.<Account>builder().build());
        when(postAccountBL.process(any(UtilityBean.class))).thenReturn(account);
        when(jsonFileParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders());

        mockMvc.perform(post("/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(account))
                        .with(request -> {
                            request.setAttribute("basicUserDetails", createTestUserDetails());
                            return request;
                        }))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Account created successfully ."));
    }
}