package com.heal.controlcenter.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.controlcenter.businesslogic.PostComponentInstanceBL;
import com.heal.controlcenter.pojo.ComponentInstancePojo;
import com.heal.controlcenter.pojo.IdPojo;
import com.heal.controlcenter.util.JsonFileParser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Unit tests for ComponentInstanceController POST endpoint.
 */
@ExtendWith(MockitoExtension.class)
class ComponentInstanceControllerTest {

    @Mock
    private PostComponentInstanceBL postComponentInstanceBL;

    @Mock
    private JsonFileParser headersParser;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        ComponentInstanceController controller = new ComponentInstanceController(null, postComponentInstanceBL, headersParser);
        mockMvc = MockMvcBuilders.standaloneSetup(controller).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    void testCreateComponentInstances_Success() throws Exception {
        // Arrange
        String accountIdentifier = "test_account";
        ComponentInstancePojo request = new ComponentInstancePojo();
        request.setName("Test Instance");
        request.setComponentName("TestComponent");
        request.setComponentVersion("1.0.0");
        request.setServiceIdentifiers(Arrays.asList("service1", "service2"));

        List<ComponentInstancePojo> requests = Arrays.asList(request);
        
        IdPojo expectedResult = IdPojo.builder()
                .id(1)
                .name("Test Instance")
                .identifier("test-instance-id")
                .build();

        List<IdPojo> expectedResults = Arrays.asList(expectedResult);

        // Mock the business logic chain
        when(postComponentInstanceBL.clientValidation(any(), anyString())).thenReturn(any());
        when(postComponentInstanceBL.serverValidation(any())).thenReturn(any());
        when(postComponentInstanceBL.process(any())).thenReturn(expectedResults);

        // Act & Assert
        mockMvc.perform(post("/accounts/{accountIdentifier}/instances", accountIdentifier)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requests)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.message").value("Component Instance(s) created successfully."))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].id").value(1))
                .andExpect(jsonPath("$.data[0].name").value("Test Instance"));
    }

    @Test
    void testCreateComponentInstances_ValidationError() throws Exception {
        // Arrange
        String accountIdentifier = "test_account";
        ComponentInstancePojo invalidRequest = new ComponentInstancePojo();
        invalidRequest.setName(""); // Invalid empty name

        List<ComponentInstancePojo> requests = Arrays.asList(invalidRequest);

        // Act & Assert
        mockMvc.perform(post("/accounts/{accountIdentifier}/instances", accountIdentifier)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requests)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testCreateComponentInstances_EmptyRequestList() throws Exception {
        // Arrange
        String accountIdentifier = "test_account";
        List<ComponentInstancePojo> emptyRequests = Arrays.asList();

        // Act & Assert
        mockMvc.perform(post("/accounts/{accountIdentifier}/instances", accountIdentifier)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(emptyRequests)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testCreateComponentInstances_InvalidJson() throws Exception {
        // Arrange
        String accountIdentifier = "test_account";
        String invalidJson = "{ invalid json }";

        // Act & Assert
        mockMvc.perform(post("/accounts/{accountIdentifier}/instances", accountIdentifier)
                .contentType(MediaType.APPLICATION_JSON)
                .content(invalidJson))
                .andExpect(status().isBadRequest());
    }
}
