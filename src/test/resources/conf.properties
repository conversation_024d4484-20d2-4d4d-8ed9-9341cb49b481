spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=************************************************************************************************************************************************************
spring.datasource.username=dbadmin
spring.datasource.password=cm9vdEAxMjM=
spring.datasource.hikari.connection-timeout=5000
spring.datasource.hikari.minimumIdle=5
spring.datasource.hikari.maximumPoolSize=5
spring.datasource.hikari.poolName=Heal_ControlCenter_Pool

server.servlet.context-path=/v2.0/api
ds.setup.type=
ds.filename.headers.properties=headers_details.json
ds.filename.keycloak.details=keycloak_details.json

ds.keycloak.ip=keycloak.appnomic
ds.keycloak.port=8443
ds.keycloak.user=appsoneadmin
ds.keycloak.pwd=QXBwc29uZUAxMjM=

logging.path=/opt/jboss/keycloak/standalone/log

# ======================================
# Dormant Schedular Configuration
# ======================================
user.dormant.creation.time.days=30
user.dormant.login.time.days=90

# ======================================
# Cassandra Configuration
# =======================================
spring.data.cassandra.contactpoints=*************
spring.data.cassandra.port=9142
spring.data.cassandra.keyspace=appsone
spring.data.cassandra.basePackages=com.heal.controlcenter.pojo
spring.data.cassandra.username=
spring.data.cassandra.password=
spring.data.cassandra.sslEnabled=true
